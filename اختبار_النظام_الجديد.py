#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام الجديد - تبويب واحد مع قائمة منسدلة
الأزرار تحل محل التبويبات
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon

def test_new_system():
    """اختبار النظام الجديد"""
    print("🆕 اختبار النظام الجديد - تبويب واحد مع قائمة منسدلة")
    print("=" * 70)
    
    app = QApplication(sys.argv)
    
    try:
        from main_window import MainWindow
        print("✅ تم استيراد MainWindow")
        
        main_window = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # فحص النظام الجديد
        def check_new_system():
            print("\n🔍 فحص النظام الجديد:")
            
            # فحص التبويبات
            tab_count = main_window.tabWidget.count()
            print(f"📋 عدد التبويبات: {tab_count}")
            
            for i in range(tab_count):
                tab_text = main_window.tabWidget.tabText(i)
                tab_data = main_window.tabWidget.tabData(i)
                print(f"   {i}: {tab_text} ({tab_data})")
            
            # فحص القائمة المنسدلة
            if hasattr(main_window, 'dropdown_widget'):
                print("✅ القائمة المنسدلة موجودة")
                dropdown = main_window.dropdown_widget
                print(f"   الحجم: {dropdown.size()}")
                print(f"   الارتفاع: {dropdown.height()}")
                print(f"   مرئية: {dropdown.isVisible()}")
                
                # فحص الأزرار في القائمة
                if hasattr(main_window, 'dropdown_items'):
                    print("📋 أزرار القائمة المنسدلة:")
                    for text, window_key, color in main_window.dropdown_items:
                        print(f"   {text} -> {window_key} ({color})")
                        
                        # التحقق من وجود النافذة
                        if window_key in main_window.windows:
                            print(f"      ✅ النافذة موجودة")
                        else:
                            print(f"      ❌ النافذة غير موجودة")
                
            else:
                print("❌ القائمة المنسدلة غير موجودة")
            
            # اختبار إظهار القائمة
            print("\n🎯 اختبار إظهار القائمة...")
            if hasattr(main_window, '_show_dropdown'):
                main_window._show_dropdown()
                print(f"   بعد الإظهار - الارتفاع: {dropdown.height()}")
                print(f"   بعد الإظهار - مرئية: {dropdown.isVisible()}")
                
                # إخفاء بعد 5 ثوان
                QTimer.singleShot(5000, lambda: main_window._hide_dropdown())
                
            else:
                print("❌ دالة _show_dropdown غير موجودة")
        
        # فحص النظام بعد ثانية واحدة
        QTimer.singleShot(1000, check_new_system)
        
        main_window.show()
        main_window.setWindowTitle("النظام الجديد - تبويب واحد مع قائمة منسدلة")
        
        print("\n📋 تعليمات الاختبار:")
        print("1. لاحظ أن التبويبات قلت (إزالة تبويبات منفصلة)")
        print("2. انقر على تبويب 'بيانات المؤسسة'")
        print("3. يجب أن تظهر قائمة منسدلة بـ 4 أزرار:")
        print("   🏢 بيانات المؤسسة (أزرق)")
        print("   ⚙️ تهيئة البرنامج (برتقالي)")
        print("   🏫 البنية التربوية (بنفسجي)")
        print("   📊 الإحصائيات (أخضر)")
        print("4. انقر على أي زر في القائمة")
        print("5. يجب أن ينقلك مباشرة للنافذة المطلوبة")
        print("6. انقر على تبويب آخر - يجب أن تختفي القائمة")
        
        print("\n🎯 الميزات الجديدة:")
        print("   ✅ تبويب واحد بدلاً من عدة تبويبات")
        print("   ✅ قائمة منسدلة تحل محل التبويبات")
        print("   ✅ الأزرار تنقل مباشرة للنوافذ")
        print("   ✅ واجهة أكثر تنظيماً وأناقة")
        print("   ✅ توفير مساحة في شريط التبويبات")
        
        print("\n🔄 الفرق عن النظام السابق:")
        print("   ❌ السابق: تبويب منفصل لكل نافذة")
        print("   ✅ الجديد: تبويب واحد مع قائمة منسدلة")
        print("   ❌ السابق: شريط تبويبات مزدحم")
        print("   ✅ الجديد: شريط تبويبات نظيف ومنظم")
        print("   ❌ السابق: صعوبة في التنقل")
        print("   ✅ الجديد: تنقل سهل وسريع")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return 1

def show_system_comparison():
    """مقارنة النظام القديم والجديد"""
    print("\n" + "=" * 70)
    print("📊 مقارنة النظام القديم والجديد")
    print("=" * 70)
    
    comparison = [
        ("التبويبات", "9 تبويبات منفصلة", "تبويب واحد + قائمة منسدلة"),
        ("التنقل", "نقر على التبويب", "نقر على التبويب → قائمة → زر"),
        ("المساحة", "شريط تبويبات مزدحم", "شريط تبويبات نظيف"),
        ("التنظيم", "تبويبات متناثرة", "تجميع منطقي في قائمة"),
        ("المظهر", "تقليدي", "حديث وأنيق"),
        ("سهولة الاستخدام", "مباشر لكن مزدحم", "خطوة إضافية لكن منظم"),
        ("الوضوح", "كل شيء مرئي", "مخفي حتى الحاجة")
    ]
    
    print(f"{'الخاصية':<20} {'النظام القديم':<25} {'النظام الجديد':<25}")
    print("-" * 70)
    for feature, old, new in comparison:
        print(f"{feature:<20} {old:<25} {new:<25}")

if __name__ == "__main__":
    print("🆕 مرحباً بك في اختبار النظام الجديد!")
    
    # عرض مقارنة الأنظمة
    show_system_comparison()
    
    print("\n" + "=" * 70)
    print("🚀 بدء تشغيل النظام الجديد...")
    print("=" * 70)
    
    # تشغيل الاختبار
    exit_code = test_new_system()
    
    print(f"\n🔚 انتهى الاختبار بكود الخروج: {exit_code}")
    sys.exit(exit_code)
