#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
thermal1_image_print.py - وحدة الطباعة الحرارية المحسنة
تم تطويرها للعمل بالطباعة عبر ملف نصي بدلاً من تحويل النص إلى صورة
لتحسين جودة الطباعة وسرعتها وتوافقها مع جميع أنواع الطابعات

الميزات الجديدة:
- طباعة نصية مباشرة بدون تحويل إلى صورة
- دعم أفضل للنصوص العربية
- سرعة أكبر في الطباعة
- توافق مع جميع أنواع الطابعات
- جودة طباعة محسنة

تاريخ الإنشاء: 2025-01-27
آخر تحديث: 2025-01-27
"""

import os
import sys
import datetime
import sqlite3
import tempfile
import subprocess

def get_thermal_printer_name():
    """الحصول على اسم الطابعة من قاعدة البيانات أو الطابعة الافتراضية"""
    try:
        # محاولة الحصول على الطابعة من قاعدة البيانات
        script_dir = os.path.dirname(os.path.abspath(__file__))
        db_path = os.path.join(script_dir, "data.db")

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT الطابعة_الحرارية FROM إعدادات_الطابعة LIMIT 1")
        result = cursor.fetchone()
        conn.close()

        if result and result[0]:
            print(f"تم العثور على طابعة محددة في قاعدة البيانات: {result[0]}")
            return result[0]
        else:
            print("لم يتم تحديد طابعة في قاعدة البيانات، البحث عن الطابعة الافتراضية...")
    except Exception as e:
        print(f"خطأ في الحصول على الطابعة من قاعدة البيانات: {e}")

    # محاولة الحصول على الطابعة الافتراضية
    try:
        if os.name == 'nt':  # Windows
            try:
                import win32print
                default_printer = win32print.GetDefaultPrinter()
                if default_printer:
                    print(f"تم العثور على الطابعة الافتراضية: {default_printer}")
                    return default_printer
            except ImportError:
                print("مكتبة win32print غير متوفرة، محاولة طرق أخرى...")
            
            # طريقة بديلة باستخدام wmic
            try:
                result = subprocess.run(['wmic', 'printer', 'get', 'name'], 
                                      capture_output=True, text=True, shell=True)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:  # تخطي العنوان
                        printer_name = line.strip()
                        if printer_name and printer_name != "Name":
                            print(f"تم العثور على طابعة: {printer_name}")
                            return printer_name
            except Exception as e:
                print(f"خطأ في الحصول على قائمة الطابعات: {e}")
        
        # للأنظمة الأخرى (Linux/Mac)
        else:
            try:
                result = subprocess.run(['lpstat', '-p'], capture_output=True, text=True)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if line.startswith('printer'):
                            printer_name = line.split()[1]
                            print(f"تم العثور على طابعة: {printer_name}")
                            return printer_name
            except Exception as e:
                print(f"خطأ في الحصول على قائمة الطابعات: {e}")
    
    except Exception as e:
        print(f"خطأ عام في الحصول على اسم الطابعة: {e}")
    
    print("لم يتم العثور على أي طابعة متاحة")
    return None

def get_institution_info():
    """الحصول على معلومات المؤسسة من قاعدة البيانات"""
    institution_name = "المؤسسة التعليمية"
    school_year = "2024/2025"
    guard_number = "1"
    
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        db_path = os.path.join(script_dir, "data.db")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # الحصول على بيانات المؤسسة
        cursor.execute("SELECT المؤسسة, السنة_الدراسية, رقم_الحراسة FROM بيانات_المؤسسة LIMIT 1")
        result = cursor.fetchone()
        if result:
            institution_name = result[0] or institution_name
            school_year = result[1] or school_year
            guard_number = str(result[2]) if result[2] else guard_number
        
        conn.close()
        
    except Exception as e:
        print(f"خطأ في الحصول على معلومات المؤسسة: {e}")
    
    return institution_name, school_year, guard_number

def get_form_info(form_id):
    """الحصول على معلومات النموذج من قاعدة البيانات - نفس الكود الأصلي"""
    # تعيين معرفات النماذج بما يتوافق مع جدول تعديل_المسميات
    form_titles = {
        1: "ورقة الدخول",
        2: "ورقة التأخر",
        3: "ورقة التوجيه",
        4: "ورقة الاستئذان",
        6: "الرمز السري"  # تغيير من 5 إلى 6 لتجنب التعارض مع نموذج زيارة الطبيب
    }

    # تعيين معرفات جدول تعديل_المسميات المقابلة
    form_db_ids = {
        1: 1,  # ورقة الدخول
        2: 2,  # ورقة التأخر
        3: 3,  # ورقة التوجيه
        4: 4,  # ورقة الاستئذان
        6: 6   # الرمز السري (تجنب استخدام 5 لأنه مخصص لزيارة الطبيب)
    }

    form_info = {
        'title': form_titles.get(form_id, "نموذج"),
        'description': "",
    }

    try:
        # تحديد مسار قاعدة البيانات
        script_dir = os.path.dirname(os.path.abspath(__file__))
        db_path = os.path.join(script_dir, "data.db")

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # استخراج عنوان النموذج وملاحظاته
        # استخدام معرف النموذج المقابل في جدول تعديل_المسميات
        db_id = form_db_ids.get(form_id, form_id)
        cursor.execute("""
            SELECT العنوان, ملاحظات
            FROM تعديل_المسميات
            WHERE id = ?
        """, (db_id,))
        result = cursor.fetchone()
        if result:
            form_info['title'] = result[0] or form_info['title']
            form_info['description'] = result[1] or form_info['description']

        conn.close()
    except Exception as e:
        print(f"خطأ في استخراج معلومات النموذج: {e}")

    return form_info

def direct_print_text(content, printer_name=None):
    """طباعة النص مباشرة إلى الطابعة بدون فتح ملفات - طباعة صامتة حقيقية"""
    if not printer_name:
        printer_name = get_thermal_printer_name()

    if not printer_name:
        return False

    try:
        # محاولة الطباعة الصامتة باستخدام QPrinter (الطريقة الأفضل)
        try:
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QPainter, QFont, QFontMetrics
            from PyQt5.QtCore import QSizeF, Qt

            # إعداد الطابعة
            printer = QPrinter()
            printer.setPrinterName(printer_name)

            # إعداد حجم الورقة للطابعة الحرارية (80mm)
            printer.setPageSize(QPrinter.Custom)
            printer.setPageSizeMM(QSizeF(75, 170))  # عرض 75 ارتفاع 170

            # إعداد الهوامش
            printer.setPageMargins(0.4, 0.4, 0.4, 0.4, QPrinter.Millimeter)

            # إعداد الرسام
            painter = QPainter()
            if painter.begin(printer):
                # إعداد الخط
                font = QFont("Calibri", 12, QFont.Bold)
                painter.setFont(font)
                painter.setPen(Qt.black)

                # حساب مقاييس الخط
                font_metrics = QFontMetrics(font)
                line_height = font_metrics.height() + 4

                # تقسيم المحتوى إلى أسطر
                lines = content.split('\n')

                # رسم النص
                y_position = 20
                page_width = printer.pageRect().width() - 20

                for line in lines:
                    if line.strip():  # تجاهل الأسطر الفارغة
                        # حساب موضع النص (وسط الصفحة)
                        text_width = font_metrics.width(line)
                        x_position = max(10, int((page_width - text_width) / 2 + 10))

                        painter.drawText(x_position, int(y_position), line)

                    y_position += line_height

                painter.end()
                return True
            else:
                # إذا فشل QPrinter، استخدم الطريقة الاحتياطية
                return direct_print_text_fallback(content, printer_name)

        except ImportError:
            # إذا لم تكن PyQt5 متوفرة، استخدم الطريقة الاحتياطية
            return direct_print_text_fallback(content, printer_name)
        except Exception:
            # في حالة فشل الطباعة المباشرة، استخدم الطريقة الاحتياطية
            return direct_print_text_fallback(content, printer_name)

    except Exception:
        return False

def direct_print_text_fallback(content, printer_name):
    """طريقة احتياطية للطباعة باستخدام win32print أو أوامر النظام"""
    try:
        # محاولة استخدام win32print أولاً
        if os.name == 'nt':  # Windows
            try:
                import win32print

                # فتح الطابعة
                hPrinter = win32print.OpenPrinter(printer_name)

                # إنشاء مهمة طباعة
                win32print.StartDocPrinter(hPrinter, 1, ("Silent Print Job", None, "RAW"))
                win32print.StartPagePrinter(hPrinter)

                # إرسال النص مباشرة
                win32print.WritePrinter(hPrinter, content.encode('utf-8'))

                # إنهاء الطباعة
                win32print.EndPagePrinter(hPrinter)
                win32print.EndDocPrinter(hPrinter)
                win32print.ClosePrinter(hPrinter)

                return True

            except ImportError:
                # إذا لم تكن win32print متوفرة، استخدم أوامر النظام
                return direct_print_text_system_command(content, printer_name)
            except Exception:
                # في حالة فشل win32print، استخدم أوامر النظام
                return direct_print_text_system_command(content, printer_name)
        else:
            # للأنظمة الأخرى، استخدم أوامر النظام
            return direct_print_text_system_command(content, printer_name)

    except Exception:
        return False

def direct_print_text_system_command(content, printer_name):
    """طريقة أخيرة للطباعة باستخدام أوامر النظام مع ملف مؤقت"""
    try:
        # إنشاء ملف مؤقت
        with tempfile.NamedTemporaryFile(delete=False, suffix=".txt", mode="w", encoding="utf-8") as f:
            f.write(content)
            file_path = f.name

        success = False

        # طباعة الملف حسب نظام التشغيل
        if os.name == 'nt':  # Windows
            try:
                # استخدام أمر print في Windows مع إخفاء النافذة
                subprocess.run(f'print /d:"{printer_name}" "{file_path}"', shell=True, check=True,
                             creationflags=subprocess.CREATE_NO_WINDOW)
                success = True
            except (subprocess.CalledProcessError, FileNotFoundError):
                pass
        else:  # Linux/Mac
            try:
                subprocess.run(["lp", "-d", printer_name, file_path], check=True)
                success = True
            except subprocess.CalledProcessError:
                pass

        # حذف الملف المؤقت فوراً
        try:
            os.unlink(file_path)
        except:
            pass

        return success

    except Exception:
        return False

def format_entry_form_content(students, section, date_str, time_str, form_id=1):
    """تنسيق محتوى ورقة الدخول بنفس التنسيق الأصلي من thermal2_image_print.py"""
    # الحصول على معلومات المؤسسة والنموذج - نفس الكود الأصلي
    institution_name, school_year, guard_number = get_institution_info()
    form_info = get_form_info(form_id)

    # بناء محتوى الطباعة بنفس التنسيق الأصلي
    content = ""

    # الرأس - نفس ترتيب الملف الأصلي
    header = [
        institution_name,
        f"السنة الدراسية: {school_year}",
        form_info['title'],
    ]

    # إضافة وصف النموذج إذا كان موجودًا وتقسيمه إلى سطرين - نفس الكود الأصلي
    if form_info['description']:
        # تقسيم النص إلى سطرين تقريبًا بنفس الطول
        description = form_info['description']
        if len(description) > 20:  # إذا كان النص طويلًا بما يكفي للتقسيم
            # تقسيم النص إلى كلمات
            words = description.split()
            # حساب عدد الكلمات التي ستكون في السطر الأول (تقريبًا نصف الكلمات)
            half_words = len(words) // 2
            # إنشاء السطرين
            first_line = ' '.join(words[:half_words])
            second_line = ' '.join(words[half_words:])
            # إضافة السطرين إلى الرأس
            header.append(first_line)
            header.append(second_line)
        else:
            # إذا كان النص قصيرًا، أضفه كما هو
            header.append(description)

    # رسم العناوين
    for i, line in enumerate(header):
        content += f"{line}\n"
        # زيادة المسافة بعد العنوان الرئيسي
        if i == 0:
            content += "\n"

    content += "\n"

    # الجدول - بنفس التنسيق الأصلي من thermal2_image_print.py
    content += "ت.ر      الاسم الكامل\n"
    content += "---------------------------------------------\n"

    # بيانات الطلاب - استخدام الترتيب التسلسلي بدلاً من الرقم الترتيبي المخزن
    for i, student in enumerate(students, 1):
        # استخدام الترتيب التسلسلي بدلاً من الرقم الترتيبي المخزن - نفس الكود الأصلي
        rt = str(i)
        if isinstance(student, dict):
            name = student.get('name', 'غير محدد')
        else:
            name = str(student)

        # تنسيق السطر بنفس طريقة الملف الأصلي
        content += f"{rt:<8} {name}\n"

    content += "---------------------------------------------\n"

    # التذييل - بنفس التنسيق الأصلي من thermal2_image_print.py
    # إضافة تفاصيل خاصة بورقة التأخر
    if form_id == 2:  # ورقة التأخر
        content += f"عدد التلاميذ المتأخرين: {len(students)}\n"
        content += "\n"
        content += "ملاحظات:\n"
        content += "• تم تسجيل التأخر في السجل\n"
        content += "• يجب على التلميذ تبرير سبب التأخر\n"
        content += "• يُرجى مراجعة إدارة المؤسسة\n"
        content += "\n"
        content += "توقيع المسؤول: ________________\n"
        content += "\n"
        content += f"تاريخ الطباعة: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
    else:
        # التذييل العادي للنماذج الأخرى
        content += f"من قســـــم : {section}\n"
        content += f"التاريخ: {date_str}  الوقت: {time_str}\n"

    content += "\n\n\n"  # مساحة إضافية للقطع

    return content

def print_entry_form_direct(students, section, date_str=None, time_str=None):
    """طباعة ورقة الدخول مباشرة - نفس واجهة الملف الأصلي"""
    # تحديد التاريخ والوقت إذا لم يتم تمريرهما - نفس الكود الأصلي
    if not date_str:
        date_str = datetime.datetime.now().strftime("%Y/%m/%d")
    if not time_str:
        time_str = datetime.datetime.now().strftime("%H:%M")

    # استدعاء دالة الطباعة العامة مع form_id=1 لورقة الدخول
    return print_thermal_text(students, section, form_id=1, date_str=date_str, time_str=time_str)

def print_late_form_direct(students, section, date_str=None, time_str=None):
    """طباعة ورقة التأخر مباشرة - نفس واجهة الملف الأصلي"""
    # تحديد التاريخ والوقت إذا لم يتم تمريرهما
    if not date_str:
        date_str = datetime.datetime.now().strftime("%Y/%m/%d")
    if not time_str:
        time_str = datetime.datetime.now().strftime("%H:%M")

    # استدعاء دالة الطباعة العامة مع form_id=2 لورقة التأخر
    return print_thermal_text(students, section, form_id=2, date_str=date_str, time_str=time_str)

def print_guidance_form_direct(students, section, date_str=None, time_str=None):
    """طباعة ورقة التوجيه مباشرة - نفس واجهة الملف الأصلي"""
    # تحديد التاريخ والوقت إذا لم يتم تمريرهما
    if not date_str:
        date_str = datetime.datetime.now().strftime("%Y/%m/%d")
    if not time_str:
        time_str = datetime.datetime.now().strftime("%H:%M")

    # استدعاء دالة الطباعة العامة مع form_id=3 لورقة التوجيه
    return print_thermal_text(students, section, form_id=3, date_str=date_str, time_str=time_str)

def print_permission_form_direct(students, section, date_str=None, time_str=None):
    """طباعة ورقة الاستئذان مباشرة - نفس واجهة الملف الأصلي"""
    # تحديد التاريخ والوقت إذا لم يتم تمريرهما
    if not date_str:
        date_str = datetime.datetime.now().strftime("%Y/%m/%d")
    if not time_str:
        time_str = datetime.datetime.now().strftime("%H:%M")

    # استدعاء دالة الطباعة العامة مع form_id=4 لورقة الاستئذان
    return print_thermal_text(students, section, form_id=4, date_str=date_str, time_str=time_str)

def format_secret_code_content(student, date_str):
    """تنسيق محتوى الرمز السري بنفس التنسيق الأصلي"""
    # الحصول على معلومات المؤسسة والنموذج
    institution_name, school_year, guard_number = get_institution_info()
    form_info = get_form_info(6)  # معرف نموذج الرمز السري

    # بناء محتوى الطباعة بنفس التنسيق الأصلي
    content = ""

    # الرأس - نفس ترتيب الملف الأصلي
    header = [
        institution_name,
        f"السنة الدراسية: {school_year}",
        form_info['title'],
    ]

    # رسم العناوين
    for i, line in enumerate(header):
        content += f"{line}\n"
        # زيادة المسافة بعد العنوان الرئيسي
        if i == 0:
            content += "\n"

    content += "\n"

    # خط فاصل
    content += "-" * 50 + "\n"
    content += "\n"

    # بيانات التلميذ - نفس التنسيق الأصلي
    rt = student.get('rt', '')
    code = student.get('code', '')
    name = student.get('name', '')
    email = f"{code}@taalim.ma" if code else ''
    secret = student.get('secret', 'غير متوفر')

    # تنسيق البيانات بشكل مشابه للنموذج المطلوب
    student_info = [
        f"رت: {rt}",
        f"رمز التلميذ: {code}",
        f"الاسم والنسب: {name}",
        f"اسم المستخدم: {email}",
        f"الرمز السري: {secret}",
    ]

    for line in student_info:
        content += f"{line}\n"

    content += "\n"

    # خط فاصل آخر
    content += "-" * 50 + "\n"
    content += "\n"

    # التاريخ
    content += f"التاريخ: {date_str}\n"
    content += "\n\n\n"  # مساحة إضافية للقطع

    return content

def print_secret_code_image(student, date_str=None):
    """
    طباعة الرمز السري لتلميذ واحد على الطابعة الحرارية
    بتنسيق مخصص يشبه النموذج المطلوب - نفس واجهة الملف الأصلي
    """
    try:
        # تحديد التاريخ إذا لم يتم تمريره
        if not date_str:
            date_str = datetime.datetime.now().strftime("%d-%m-%Y")

        # تنسيق المحتوى
        content = format_secret_code_content(student, date_str)

        # طباعة المحتوى
        success = direct_print_text(content)

        return success

    except Exception:
        return False

def print_secret_codes_direct(students, section=None, date_str=None):
    """
    طباعة الرموز السرية لعدة طلاب مباشرة على الطابعة الحرارية
    """
    try:
        if not date_str:
            date_str = datetime.datetime.now().strftime("%d-%m-%Y")

        success_count = 0
        for student in students:
            if print_secret_code_image(student, date_str):
                success_count += 1

        return success_count == len(students)

    except Exception:
        return False

def print_thermal_text(students, section, form_id=1, date_str=None, time_str=None):
    """
    طباعة نموذج على الطابعة الحرارية باستخدام الطباعة النصية المحسنة
    form_id: 1 لورقة الدخول، 2 لورقة التأخر، 3 لورقة التوجيه، 4 لورقة الاستئذان، 6 للرمز السري
    """
    try:
        # تحديد التاريخ والوقت إذا لم يتم تمريرهما
        if not date_str:
            date_str = datetime.datetime.now().strftime("%Y/%m/%d")
        if not time_str:
            time_str = datetime.datetime.now().strftime("%H:%M")

        # التحقق من وجود تلاميذ
        if not students:
            return False

        # تنسيق المحتوى بنفس التنسيق الأصلي
        content = format_entry_form_content(students, section, date_str, time_str, form_id)

        # طباعة المحتوى
        success = direct_print_text(content)

        return success

    except Exception as e:
        return False

# دوال للتوافق مع الكود القديم
def print_entry_form_image(*args, **kwargs):
    """دالة للتوافق مع الكود القديم - تحويل إلى الطباعة النصية المحسنة"""
    print("تحذير: تم استدعاء الدالة القديمة، سيتم التحويل إلى الطباعة النصية المحسنة")
    return print_entry_form_direct(*args, **kwargs)

def print_thermal_image(*args, **kwargs):
    """دالة للتوافق مع الكود القديم - تحويل إلى الطباعة النصية المحسنة"""
    print("تحذير: تم استدعاء الدالة القديمة، سيتم التحويل إلى الطباعة النصية المحسنة")
    return print_thermal_text(*args, **kwargs)

# دوال إضافية للتوافق مع جميع الأوراق
def print_late_form_image(*args, **kwargs):
    """دالة للتوافق مع الكود القديم - ورقة التأخر"""
    print("تحذير: تم استدعاء الدالة القديمة، سيتم التحويل إلى الطباعة النصية المحسنة")
    return print_late_form_direct(*args, **kwargs)

def print_guidance_form_image(*args, **kwargs):
    """دالة للتوافق مع الكود القديم - ورقة التوجيه"""
    print("تحذير: تم استدعاء الدالة القديمة، سيتم التحويل إلى الطباعة النصية المحسنة")
    return print_guidance_form_direct(*args, **kwargs)

def print_permission_form_image(*args, **kwargs):
    """دالة للتوافق مع الكود القديم - ورقة الاستئذان"""
    print("تحذير: تم استدعاء الدالة القديمة، سيتم التحويل إلى الطباعة النصية المحسنة")
    return print_permission_form_direct(*args, **kwargs)

if __name__ == "__main__":
    print("🖨️ وحدة الطباعة الحرارية المحسنة")
    print("=" * 60)
    print("الميزات:")
    print("✅ طباعة نصية مباشرة بدون تحويل إلى صورة")
    print("✅ الحفاظ على التنسيق الأصلي من thermal2_image_print.py")
    print("✅ دعم محسن للنصوص العربية")
    print("✅ سرعة أكبر في الطباعة")
    print("✅ توافق مع جميع أنواع الطابعات")
    print("✅ جودة طباعة محسنة")
    print("✅ نفس واجهة الكود الأصلي")
    print("✅ دعم جميع أنواع الأوراق")
    print("=" * 60)

    # اختبار سريع
    print("\n🧪 اختبار سريع:")
    try:
        # اختبار الحصول على معلومات المؤسسة
        institution_name, school_year, guard_number = get_institution_info()
        print(f"✅ المؤسسة: {institution_name}")
        print(f"✅ السنة الدراسية: {school_year}")

        # اختبار الحصول على معلومات جميع النماذج
        forms = [
            (1, "ورقة الدخول"),
            (2, "ورقة التأخر"),
            (3, "ورقة التوجيه"),
            (4, "ورقة الاستئذان"),
            (6, "الرمز السري")
        ]

        print("\n📋 النماذج المدعومة:")
        for form_id, form_name in forms:
            try:
                form_info = get_form_info(form_id)
                print(f"✅ {form_name}: {form_info['title']}")
            except Exception as e:
                print(f"❌ {form_name}: خطأ - {e}")

        print("\n✅ جميع الوظائف تعمل بشكل صحيح!")

    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

    print("\n📖 لتشغيل اختبار شامل:")
    print("python اختبار_جميع_الأوراق.py")
    print("python اختبار_التنسيق_الأصلي.py")
    print("python مقارنة_المحتوى.py")





def test_printer():
    """اختبار الطابعة بطباعة صفحة تجريبية"""
    try:
        print("=" * 60)
        print("اختبار الطابعة...")
        print("=" * 60)

        # محتوى الاختبار
        test_content = """
اختبار الطابعة
===============

هذه صفحة اختبار للطابعة الحرارية
تم إنشاؤها بواسطة النظام المحسن للطباعة النصية

معلومات الاختبار:
- التاريخ: """ + datetime.datetime.now().strftime("%Y/%m/%d") + """
- الوقت: """ + datetime.datetime.now().strftime("%H:%M:%S") + """
- النظام: Windows
- الترميز: UTF-8

إذا ظهر هذا النص بوضوح، فإن الطابعة تعمل بشكل صحيح.

===============
انتهى الاختبار
===============


"""

        # طباعة المحتوى
        success = direct_print_text(test_content)

        if success:
            print("✅ تم إرسال صفحة الاختبار بنجاح")
            print("📄 تحقق من الطابعة للتأكد من جودة الطباعة")
        else:
            print("❌ فشل في إرسال صفحة الاختبار")

        print("=" * 60)
        return success

    except Exception as e:
        print(f"❌ خطأ في اختبار الطابعة: {e}")
        return False

# دالة للتوافق مع الكود القديم
def print_entry_form_image(*args, **kwargs):
    """دالة للتوافق مع الكود القديم - تحويل إلى الطباعة النصية"""
    print("تحذير: تم استدعاء الدالة القديمة، سيتم التحويل إلى الطباعة النصية المحسنة")
    return print_entry_form_direct(*args, **kwargs)

if __name__ == "__main__":
    print("🖨️ وحدة الطباعة الحرارية المحسنة")
    print("=" * 60)
    print("الميزات:")
    print("✅ طباعة نصية مباشرة بدون تحويل إلى صورة")
    print("✅ دعم محسن للنصوص العربية")
    print("✅ سرعة أكبر في الطباعة")
    print("✅ توافق مع جميع أنواع الطابعات")
    print("✅ جودة طباعة محسنة")
    print("=" * 60)

    # اختبار الطابعة
    test_printer()
