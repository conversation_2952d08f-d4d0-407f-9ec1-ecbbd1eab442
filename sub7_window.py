import sys
import os
import sqlite3
import platform

from PyQt5.QtWidgets import (
    QApplication, QWidget, QComboBox, QVBoxLayout, QHBoxLayout,
    QLabel, QMessageBox, QTabWidget, QFormLayout, QLineEdit,
    QSpinBox, QCheckBox, QPushButton, QGroupBox, QSizePolicy,
    QFrame, QDialog
)
from PyQt5.QtGui import QFont
from PyQt5.QtCore import Qt
try:
    from PyQt5.QtPrintSupport import QPrinterInfo
except ImportError:
    print("خطأ: لم يتم العثور على PyQt5.QtPrintSupport. تأكد من تثبيتها.")
    QPrinterInfo = None

# لم نعد نستخدم نافذة اختيار الطابعة المنفصلة

# محاولة استيراد win32print، ولكن تعيينه كـ None في حالة الفشل
win32print = None
if platform.system() == "Windows":
    try:
        import win32print
        print("تم استيراد win32print بنجاح")
    except ImportError:
        print("تحذير: مكتبة win32print غير مثبتة. لن تعمل وظيفة تعيين الطابعة الافتراضية على ويندوز.")
        print("للتثبيت: pip install pywin32")
        win32print = None
else:
    win32print = None

import subprocess

DATABASE_NAME = "data.db"
SETTINGS_TABLE_NAME = "إعدادات_الطابعة"

def setup_printer_database():
    """إعداد جدول إعدادات الطابعة في قاعدة البيانات"""
    db_path = os.path.join(os.path.dirname(__file__), DATABASE_NAME)
    conn = None
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # التحقق من وجود الجدول القديم
        cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{SETTINGS_TABLE_NAME}'")
        table_exists = cursor.fetchone() is not None

        if table_exists:
            # التحقق من هيكل الجدول الحالي
            cursor.execute(f"PRAGMA table_info({SETTINGS_TABLE_NAME})")
            columns = [column[1] for column in cursor.fetchall()]

            # إذا كان الجدول يحتوي فقط على الأعمدة القديمة، قم بتحديثه
            if set(columns) == set(['id', 'الطابعة_الحرارية', 'الطابعة_العادية']):
                print("تحديث جدول إعدادات الطابعة بالأعمدة الجديدة...")

                # حفظ البيانات الحالية
                cursor.execute(f"SELECT * FROM {SETTINGS_TABLE_NAME} WHERE id = 1")
                old_data = cursor.fetchone()

                # إعادة تسمية الجدول القديم
                cursor.execute(f"ALTER TABLE {SETTINGS_TABLE_NAME} RENAME TO {SETTINGS_TABLE_NAME}_old")

                # إنشاء الجدول الجديد
                cursor.execute(f'''
                    CREATE TABLE "{SETTINGS_TABLE_NAME}" (
                        id INTEGER PRIMARY KEY,
                        الطابعة_الحرارية TEXT,
                        الطابعة_العادية TEXT,
                        عرض_الورق INTEGER DEFAULT 80,
                        نوع_الاتصال TEXT DEFAULT 'windows',
                        معرف_البائع TEXT,
                        معرف_المنتج TEXT,
                        عنوان_IP TEXT,
                        المنفذ INTEGER DEFAULT 9100,
                        المنفذ_التسلسلي TEXT,
                        معدل_الباود INTEGER DEFAULT 9600,
                        قص_الورق BOOLEAN DEFAULT 1,
                        عدد_الأحرف_في_السطر INTEGER DEFAULT 42
                    )
                ''')

                # نقل البيانات القديمة إلى الجدول الجديد
                if old_data:
                    cursor.execute(f'''
                        INSERT INTO "{SETTINGS_TABLE_NAME}"
                        (id, الطابعة_الحرارية, الطابعة_العادية, عرض_الورق, نوع_الاتصال, عدد_الأحرف_في_السطر)
                        VALUES (?, ?, ?, 80, 'windows', 42)
                    ''', (old_data[0], old_data[1], old_data[2]))

                # حذف الجدول القديم
                cursor.execute(f"DROP TABLE {SETTINGS_TABLE_NAME}_old")

                print("تم تحديث جدول إعدادات الطابعة بنجاح.")
            elif len(columns) < 13:
                # إذا كان الجدول يحتوي على بعض الأعمدة الجديدة ولكن ليس كلها
                missing_columns = []
                required_columns = [
                    'id', 'الطابعة_الحرارية', 'الطابعة_العادية', 'عرض_الورق', 'نوع_الاتصال',
                    'معرف_البائع', 'معرف_المنتج', 'عنوان_IP', 'المنفذ', 'المنفذ_التسلسلي',
                    'معدل_الباود', 'قص_الورق', 'عدد_الأحرف_في_السطر'
                ]

                for col in required_columns:
                    if col not in columns:
                        missing_columns.append(col)

                # إضافة الأعمدة المفقودة
                for col in missing_columns:
                    if col == 'عرض_الورق':
                        cursor.execute(f"ALTER TABLE {SETTINGS_TABLE_NAME} ADD COLUMN {col} INTEGER DEFAULT 80")
                    elif col == 'نوع_الاتصال':
                        cursor.execute(f"ALTER TABLE {SETTINGS_TABLE_NAME} ADD COLUMN {col} TEXT DEFAULT 'windows'")
                    elif col == 'المنفذ':
                        cursor.execute(f"ALTER TABLE {SETTINGS_TABLE_NAME} ADD COLUMN {col} INTEGER DEFAULT 9100")
                    elif col == 'معدل_الباود':
                        cursor.execute(f"ALTER TABLE {SETTINGS_TABLE_NAME} ADD COLUMN {col} INTEGER DEFAULT 9600")
                    elif col == 'قص_الورق':
                        cursor.execute(f"ALTER TABLE {SETTINGS_TABLE_NAME} ADD COLUMN {col} BOOLEAN DEFAULT 1")
                    elif col == 'عدد_الأحرف_في_السطر':
                        cursor.execute(f"ALTER TABLE {SETTINGS_TABLE_NAME} ADD COLUMN {col} INTEGER DEFAULT 42")
                    else:
                        cursor.execute(f"ALTER TABLE {SETTINGS_TABLE_NAME} ADD COLUMN {col} TEXT")

                print(f"تم إضافة الأعمدة المفقودة: {', '.join(missing_columns)}")
        else:
            # إنشاء الجدول الجديد إذا لم يكن موجودًا
            cursor.execute(f'''
                CREATE TABLE "{SETTINGS_TABLE_NAME}" (
                    id INTEGER PRIMARY KEY,
                    الطابعة_الحرارية TEXT,
                    الطابعة_العادية TEXT,
                    عرض_الورق INTEGER DEFAULT 80,
                    نوع_الاتصال TEXT DEFAULT 'windows',
                    معرف_البائع TEXT,
                    معرف_المنتج TEXT,
                    عنوان_IP TEXT,
                    المنفذ INTEGER DEFAULT 9100,
                    المنفذ_التسلسلي TEXT,
                    معدل_الباود INTEGER DEFAULT 9600,
                    قص_الورق BOOLEAN DEFAULT 1,
                    عدد_الأحرف_في_السطر INTEGER DEFAULT 42
                )
            ''')

            # إدراج صف افتراضي
            cursor.execute(f'''
                INSERT OR IGNORE INTO "{SETTINGS_TABLE_NAME}"
                (id, الطابعة_الحرارية, الطابعة_العادية, عرض_الورق, نوع_الاتصال, عدد_الأحرف_في_السطر)
                VALUES (1, NULL, NULL, 80, 'windows', 42)
            ''')

            print(f"تم إنشاء جدول '{SETTINGS_TABLE_NAME}' وإدراج صف الإعدادات الافتراضية.")

        conn.commit()
        print(f"تم التأكد من وجود جدول '{SETTINGS_TABLE_NAME}' بالهيكل المطلوب.")
    except sqlite3.Error as e:
        print(f"خطأ في قاعدة البيانات: {e}")
        QMessageBox.critical(None, "خطأ قاعدة البيانات",
                             f"حدث خطأ أثناء إعداد أو الوصول إلى جدول إعدادات الطابعة:\n{e}")
    finally:
        if conn:
            conn.close()


class Sub7Window(QDialog):
    def __init__(self):
        super().__init__()

        # جعل النافذة مشروطة (modal) بحيث لا يمكن العودة إلى النافذة السابقة إلا بعد إغلاقها
        self.setWindowModality(Qt.ApplicationModal)

        # تعيين أعلام النافذة لإظهار زر الإغلاق فقط
        self.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowCloseButtonHint)

        setup_printer_database()
        self.db_path = os.path.join(os.path.dirname(__file__), DATABASE_NAME)
        self._loading_settings = False

        # الحصول على قائمة الطابعات المتاحة
        self._all_printers = self.get_printers()

        # طباعة قائمة الطابعات المتاحة للتشخيص
        print("الطابعات المتاحة على الجهاز:")
        for i, printer in enumerate(self._all_printers):
            print(f"{i+1}. {printer}")

        # التأكد من وجود سكريبت تعيين الطابعة الافتراضية
        self.ensure_default_printer_script_exists()

        # توحيد الخط ليكون Calibri بحجم 14 غامق لجميع العناصر
        self.app_font = QFont('Calibri', 14)
        self.app_font.setBold(True)
        self.setFont(self.app_font)

        # تعريف خط العناوين
        self.title_font = QFont('Calibri', 15)
        self.title_font.setBold(True)

        self.setWindowTitle("إعدادات الطابعة")

        # إزالة سطر setWindowFlags لاستخدام شريط عنوان النظام الافتراضي بدلاً من المخصص
        # self.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint)  <- تم إزالة هذا السطر

        # استخدام سياسة الحجم المرنة للنافذة بأكملها
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # تعيين الحجم الأدنى للنافذة
        self.setMinimumSize(800, 800)

        # تعيين الحجم المفضل
        self.resize(1000, 800)

        # تحسين مظهر النافذة
        self.setStyleSheet("""
            QWidget {
                background-color: #f5f5f5;
                font-family: 'Calibri';
                font-size: 14pt;
                font-weight: bold;
                color: black;
            }
            QLabel {
                color: black;
                font-weight: bold;
                padding: 2px;
            }
            QLabel[title="true"] {
                color: #0066cc;
                font-size: 15pt;
                font-weight: bold;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 4px 8px;
                font-weight: bold;
                min-width: 80px;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
            QGroupBox {
                border: 1px solid #cccccc;
                border-radius: 4px;
                margin-top: 12px;
                font-weight: bold;
                color: #0066cc;
                font-size: 15pt;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
            QTabBar::tab {
                background-color: #f0f0f0;
                border: 1px solid #cccccc;
                border-bottom-color: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                padding: 4px 8px;
                min-width: 80px;
                min-height: 30px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom-color: white;
                color: #0066cc;
            }
            QComboBox, QSpinBox, QLineEdit {
                padding: 4px;
                border: 1px solid #cccccc;
                border-radius: 4px;
                min-height: 30px;
                font-weight: bold;
            }
        """)

        self.initUI()
        self.load_settings()

    def get_printers(self):
        """الحصول على قائمة الطابعات المتاحة في النظام"""
        printers = []

        try:
            # محاولة استخدام win32print إذا كان متاحًا
            if platform.system() == "Windows" and win32print:
                try:
                    # استخدام كتلة try-except منفصلة فقط لاستدعاء EnumPrinters
                    # هذا يعزل أي أخطاء في هذه الوظيفة المحددة
                    printers = [printer[2] for printer in win32print.EnumPrinters(2)]
                    print("تم الحصول على الطابعات باستخدام win32print")
                    if printers:  # التحقق من أن القائمة ليست فارغة
                        return printers
                except Exception as e:
                    print(f"خطأ في الحصول على الطابعات باستخدام win32print: {e}")
                    # في حالة الفشل، سنستخدم طريقة بديلة

            # استخدام wmic كبديل في Windows
            if platform.system() == "Windows":
                try:
                    print("محاولة الحصول على الطابعات باستخدام wmic...")
                    cmd = 'wmic printer get name'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)

                    if result.returncode == 0 and result.stdout:
                        # معالجة الناتج لاستخراج أسماء الطابعات
                        lines = result.stdout.strip().split('\n')
                        # تخطي السطر الأول (العنوان)
                        for line in lines[1:]:
                            printer_name = line.strip()
                            if printer_name:  # تجاهل الأسطر الفارغة
                                printers.append(printer_name)

                        print(f"تم الحصول على {len(printers)} طابعة باستخدام wmic")
                        if printers:  # التحقق من أن القائمة ليست فارغة
                            return printers
                except Exception as e:
                    print(f"خطأ في الحصول على الطابعات باستخدام wmic: {str(e)}")
                    # في حالة الفشل، سنستخدم QPrinterInfo كبديل

            # استخدام QPrinterInfo كخيار أخير
            if QPrinterInfo:
                try:
                    print("محاولة الحصول على الطابعات باستخدام QPrinterInfo...")
                    printers = QPrinterInfo.availablePrinterNames()
                    print(f"تم الحصول على {len(printers)} طابعة باستخدام QPrinterInfo")
                    return printers
                except Exception as e:
                    print(f"خطأ في الحصول على الطابعات باستخدام QPrinterInfo: {str(e)}")

            # إذا وصلنا إلى هنا ولم نحصل على أي طابعات
            if not printers:
                print("لم يتم العثور على أي طابعات باستخدام أي طريقة.")
                # إضافة طابعة افتراضية للتجربة
                printers = ["Microsoft Print to PDF", "Microsoft XPS Document Writer"]
                print(f"تمت إضافة {len(printers)} طابعة افتراضية للتجربة")

            return printers

        except Exception as e:
            print(f"خطأ عام في الحصول على الطابعات: {str(e)}")
            # إضافة طابعة افتراضية في حالة الخطأ
            default_printers = ["Microsoft Print to PDF", "Microsoft XPS Document Writer"]
            print(f"تمت إضافة {len(default_printers)} طابعة افتراضية بسبب الخطأ")
            return default_printers

    def safe_refresh_printers(self):
        """وظيفة آمنة لتحديث قائمة الطابعات تمنع توقف البرنامج"""
        try:
            # عرض رسالة للمستخدم بأن التحديث قيد التنفيذ
            QMessageBox.information(self, "جاري التحديث", "جاري تحديث قائمة الطابعات، يرجى الانتظار...")

            # تحديث قائمة الطابعات
            updated_printers = self.refresh_printers()

            # عرض رسالة نجاح للمستخدم
            if updated_printers:
                QMessageBox.information(self, "تم التحديث", f"تم تحديث قائمة الطابعات بنجاح.\nتم العثور على {len(updated_printers)} طابعة.")
            else:
                QMessageBox.warning(self, "تنبيه", "لم يتم العثور على أي طابعات في النظام.")
        except Exception as e:
            print(f"حدث خطأ أثناء تحديث قائمة الطابعات: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحديث قائمة الطابعات:\n{str(e)}")

    def refresh_printers(self):
        """تحديث قائمة الطابعات المتاحة"""
        # تخزين القائمة القديمة للاستخدام في حالة الفشل
        old_printers = self._all_printers.copy() if self._all_printers else []

        try:
            # حفظ الطابعات المحددة حالياً
            current_thermal = self.combo_thermal.currentText() if hasattr(self, 'combo_thermal') else ""
            current_regular = self.combo_regular.currentText() if hasattr(self, 'combo_regular') else ""

            # لا نحتاج لحفظ نص البحث لأننا استخدمنا مربعات تحرير وسرد بسيطة بدون تصفية

            # استخدام الطريقة المحسنة للحصول على قائمة الطابعات
            print("جاري الحصول على قائمة الطابعات المحدثة...")
            new_printers = self.get_printers()
            print(f"تم الحصول على {len(new_printers)} طابعة")

            # التحقق من أن القائمة ليست فارغة
            if not new_printers:
                print("تحذير: قائمة الطابعات فارغة، استخدام القائمة القديمة")
                new_printers = old_printers

            # تحديث القائمة الرئيسية
            self._all_printers = new_printers

            # طباعة قائمة الطابعات المتاحة للتشخيص
            print("الطابعات المتاحة على الجهاز (بعد التحديث):")
            for i, printer in enumerate(self._all_printers):
                print(f"{i+1}. {printer}")

            # تحديث قوائم الطابعات بطريقة آمنة
            try:
                if hasattr(self, 'combo_thermal'):
                    self.combo_thermal.blockSignals(True)
                    self.combo_thermal.clear()
                    self.combo_thermal.addItems(self._all_printers)

                    # إعادة تحديد الطابعة السابقة إذا كانت لا تزال متاحة
                    if current_thermal:
                        index = self.combo_thermal.findText(current_thermal)
                        if index >= 0:
                            self.combo_thermal.setCurrentIndex(index)
                        else:
                            # إذا لم يتم العثور على الطابعة السابقة، أضفها للقائمة
                            self.combo_thermal.addItem(current_thermal)
                            self.combo_thermal.setCurrentText(current_thermal)

                    # لا نحتاج لإعادة تطبيق نص البحث لأننا استخدمنا مربعات تحرير وسرد بسيطة بدون تصفية

                    self.combo_thermal.blockSignals(False)
            except Exception as e:
                print(f"خطأ في تحديث قائمة الطابعات الحرارية: {str(e)}")

            try:
                if hasattr(self, 'combo_regular'):
                    self.combo_regular.blockSignals(True)
                    self.combo_regular.clear()
                    self.combo_regular.addItems(self._all_printers)

                    # إعادة تحديد الطابعة السابقة إذا كانت لا تزال متاحة
                    if current_regular:
                        index = self.combo_regular.findText(current_regular)
                        if index >= 0:
                            self.combo_regular.setCurrentIndex(index)
                        else:
                            # إذا لم يتم العثور على الطابعة السابقة، أضفها للقائمة
                            self.combo_regular.addItem(current_regular)
                            self.combo_regular.setCurrentText(current_regular)

                    # لا نحتاج لإعادة تطبيق نص البحث لأننا استخدمنا مربعات تحرير وسرد بسيطة بدون تصفية

                    self.combo_regular.blockSignals(False)
            except Exception as e:
                print(f"خطأ في تحديث قائمة الطابعات العادية: {str(e)}")

            # تم التحديث بنجاح
            print(f"تم تحديث قائمة الطابعات بنجاح. تم العثور على {len(self._all_printers)} طابعة.")
            return self._all_printers

        except Exception as e:
            print(f"حدث خطأ عام أثناء تحديث قائمة الطابعات: {str(e)}")
            return old_printers  # إرجاع القائمة القديمة في حالة حدوث خطأ


    def initUI(self):
        # إنشاء إطار خارجي بنمط مربع حوار
        self.main_frame = QFrame(self)
        self.main_frame.setFrameShape(QFrame.StyledPanel)
        self.main_frame.setFrameShadow(QFrame.Raised)
        self.main_frame.setStyleSheet("""
            QFrame {
                background-color: #f5f5f5;
                border: 1px solid #cccccc;
                border-radius: 5px;
            }
        """)

        # إنشاء تخطيط رئيسي للنافذة
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)  # تعديل الهوامش لتناسب شريط العنوان الافتراضي
        main_layout.setSpacing(10)  # زيادة المسافة بين العناصر

        # تم إزالة كود إنشاء شريط العنوان المخصص وجميع العناصر المرتبطة به

        # إضافة الإطار الرئيسي إلى التخطيط الرئيسي مباشرة
        main_layout.addWidget(self.main_frame)

        # إنشاء تخطيط للمحتوى الرئيسي
        content_layout = QVBoxLayout(self.main_frame)
        content_layout.setContentsMargins(10, 10, 10, 10)
        content_layout.setSpacing(5)

        # إنشاء علامات التبويب
        tab_widget = QTabWidget()
        tab_widget.setDocumentMode(True)  # مظهر أكثر تكاملاً

        # تم تعريف أنماط التبويبات في ستايل النافذة الرئيسية

        # تعريف نمط موحد للقوائم المنسدلة الأخرى
        combo_stylesheet = """
            QComboBox {
                padding: 4px;
                border: 1px solid #cccccc;
                border-radius: 4px;
                min-height: 30px;
                font-weight: bold;
                text-align: right;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top left;
                width: 20px;
                border-left: 1px solid #cccccc;
            }
        """

        # استخدام نسب مئوية بدلاً من القيم الثابتة
        # تعريف أحجام العناصر
        control_height = 35  # ارتفاع عناصر التحكم

        # استخدام قائمة الطابعات المخزنة مسبقاً
        printer_list = self._all_printers

        # تبويب الطابعة العادية (A4)
        regular_tab = QWidget()
        regular_layout = QVBoxLayout(regular_tab)
        regular_layout.setContentsMargins(20, 20, 20, 20)
        regular_layout.setSpacing(20)

        # عنوان تبويب الطابعة العادية
        regular_title_label = QLabel("إعدادات الطابعة العادية (A4)")
        regular_title_label.setProperty("title", "true")  # لتطبيق نمط العناوين
        regular_title_label.setFont(self.title_font)
        regular_title_label.setAlignment(Qt.AlignCenter)
        regular_layout.addWidget(regular_title_label)

        # إضافة مساحة بين العنوان والطابعات
        regular_layout.addSpacing(5)

        # قسم الطابعة العادية (الافتراضية) - استخدام تخطيط أفقي مضغوط
        regular_printer_layout = QHBoxLayout()
        regular_printer_layout.setSpacing(5)  # تقليل المسافة بين العناصر
        regular_printer_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش

        label_regular = QLabel("الطابعة العادية (الافتراضية):")
        label_regular.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        # استخدام مربع تحرير وسرد بسيط بدون تصفية
        self.combo_regular = QComboBox()
        # تعيين عرض ثابت 500 بكسل لإتاحة مساحة أكبر بين مربع التحرير والسرد وزر التحديث
        self.combo_regular.setFixedWidth(500)
        self.combo_regular.setMinimumHeight(control_height)
        # تعيين اتجاه النص من اليمين إلى اليسار
        self.combo_regular.setLayoutDirection(Qt.RightToLeft)
        self.combo_regular.addItems(printer_list)
        # إضافة تنسيق CSS لمحاذاة النص إلى اليمين
        self.combo_regular.setStyleSheet("""
            QComboBox {
                padding: 4px;
                border: 1px solid #cccccc;
                border-radius: 4px;
                min-height: 30px;
                font-weight: bold;
                text-align: right;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top left;
                width: 20px;
                border-left: 1px solid #cccccc;
            }
        """)
        # إلغاء خاصية التحرير والتصفية التي تسبب المشكلة
        self.combo_regular.setEditable(False)

        # زر تحديث قائمة الطابعات - وضعه في نفس صف مربع التحرير والسرد
        refresh_button = QPushButton("تحديث")
        refresh_button.clicked.connect(self.safe_refresh_printers)
        refresh_button.setMinimumHeight(control_height)
        refresh_button.setMaximumWidth(80)  # تحديد عرض الزر ليكون مناسباً

        regular_printer_layout.addWidget(label_regular)
        regular_printer_layout.addWidget(self.combo_regular)
        regular_printer_layout.addSpacing(20)  # إضافة مسافة أكبر (20 بكسل) بين مربع التحرير والسرد وزر التحديث
        regular_printer_layout.addWidget(refresh_button)  # إضافة الزر في نفس الصف
        regular_layout.addLayout(regular_printer_layout)

        # تخطيط أفقي للأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(5)
        buttons_layout.setContentsMargins(0, 0, 0, 0)

        # زر حفظ الطابعة العادية في قاعدة البيانات
        save_regular_button = QPushButton("حفظ الطابعة")
        save_regular_button.clicked.connect(self.save_regular_printer_only)
        buttons_layout.addWidget(save_regular_button)

        # زر تعيين كطابعة افتراضية للنظام
        set_default_button = QPushButton("تعيين كافتراضية")
        set_default_button.clicked.connect(self.safe_set_default_printer)
        buttons_layout.addWidget(set_default_button)

        # إضافة تخطيط الأزرار إلى التخطيط الرئيسي
        regular_layout.addLayout(buttons_layout)

        regular_layout.addStretch(1)

        # تبويب الطابعة الحرارية
        thermal_tab = QWidget()
        thermal_layout = QVBoxLayout(thermal_tab)
        thermal_layout.setContentsMargins(20, 20, 20, 20)
        thermal_layout.setSpacing(5)

        # عنوان تبويب الطابعة الحرارية
        thermal_title_label = QLabel("إعدادات الطابعة الحرارية")
        thermal_title_label.setProperty("title", "true")  # لتطبيق نمط العناوين
        thermal_title_label.setFont(self.title_font)
        thermal_title_label.setAlignment(Qt.AlignCenter)
        thermal_layout.addWidget(thermal_title_label)

        # إضافة مساحة بين العنوان والطابعات
        thermal_layout.addSpacing(10)

        # قسم اختيار الطابعة الحرارية - استخدام تخطيط أفقي مضغوط
        thermal_printer_layout = QHBoxLayout()
        thermal_printer_layout.setSpacing(5)  # تقليل المسافة بين العناصر
        thermal_printer_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش

        label_thermal = QLabel("الطابعة الحرارية:")
        label_thermal.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        # استخدام مربع تحرير وسرد بسيط بدون تصفية
        self.combo_thermal = QComboBox()
        # تعيين عرض ثابت 500 بكسل لإتاحة مساحة أكبر بين مربع التحرير والسرد وزر التحديث
        self.combo_thermal.setFixedWidth(500)
        self.combo_thermal.setMinimumHeight(control_height)
        # تعيين اتجاه النص من اليمين إلى اليسار
        self.combo_thermal.setLayoutDirection(Qt.RightToLeft)
        self.combo_thermal.addItems(printer_list)
        # إضافة تنسيق CSS لمحاذاة النص إلى اليمين
        self.combo_thermal.setStyleSheet("""
            QComboBox {
                padding: 4px;
                border: 1px solid #cccccc;
                border-radius: 4px;
                min-height: 30px;
                font-weight: bold;
                text-align: right;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top left;
                width: 20px;
                border-left: 1px solid #cccccc;
            }
        """)
        # إلغاء خاصية التحرير والتصفية التي تسبب المشكلة
        self.combo_thermal.setEditable(False)

        # زر تحديث قائمة الطابعات - وضعه في نفس صف مربع التحرير والسرد
        refresh_thermal_button = QPushButton("تحديث")
        refresh_thermal_button.clicked.connect(self.safe_refresh_printers)
        refresh_thermal_button.setMinimumHeight(control_height)
        refresh_thermal_button.setMaximumWidth(80)  # تحديد عرض الزر ليكون مناسباً

        thermal_printer_layout.addWidget(label_thermal)
        thermal_printer_layout.addWidget(self.combo_thermal)
        thermal_printer_layout.addSpacing(20)  # إضافة مسافة أكبر (20 بكسل) بين مربع التحرير والسرد وزر التحديث
        thermal_printer_layout.addWidget(refresh_thermal_button)  # إضافة الزر في نفس الصف

        thermal_layout.addLayout(thermal_printer_layout)

        # إعدادات الطابعة الحرارية المتقدمة
        # مجموعة إعدادات الطابعة الحرارية
        thermal_group = QGroupBox("إعدادات الطابعة الحرارية")
        thermal_form = QFormLayout(thermal_group)
        thermal_form.setLabelAlignment(Qt.AlignRight)
        thermal_form.setFormAlignment(Qt.AlignRight)
        thermal_form.setSpacing(5)  # تقليل المسافة بين العناصر
        thermal_form.setContentsMargins(5, 15, 5, 5)  # تقليل الهوامش

        # عرض الورق
        self.paper_width = QComboBox()
        self.paper_width.addItems(["58 مم", "80 مم", "112 مم"])
        self.paper_width.setStyleSheet(combo_stylesheet)
        self.paper_width.setLayoutDirection(Qt.RightToLeft)
        thermal_form.addRow("عرض الورق:", self.paper_width)

        # عدد الأحرف في السطر
        self.char_per_line = QSpinBox()
        self.char_per_line.setRange(20, 100)
        self.char_per_line.setValue(42)
        self.char_per_line.setMinimumHeight(control_height)
        self.char_per_line.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        thermal_form.addRow("عدد الأحرف في السطر:", self.char_per_line)

        # قص الورق
        self.cut_paper = QCheckBox("قص الورق تلقائياً بعد الطباعة")
        self.cut_paper.setChecked(True)
        thermal_form.addRow("", self.cut_paper)

        thermal_layout.addWidget(thermal_group)

        # مجموعة نوع الاتصال
        connection_group = QGroupBox("نوع الاتصال")
        connection_layout = QVBoxLayout(connection_group)
        connection_layout.setSpacing(5)  # تقليل المسافة بين العناصر
        connection_layout.setContentsMargins(5, 15, 5, 5)  # تقليل الهوامش

        # نوع الاتصال - تخطيط أفقي مضغوط
        connection_type_layout = QHBoxLayout()
        connection_type_layout.setSpacing(5)  # تقليل المسافة بين العناصر
        connection_type_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش

        connection_type_label = QLabel("نوع الاتصال:")
        connection_type_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        self.connection_type = QComboBox()
        self.connection_type.addItems(["Windows", "USB", "شبكة", "تسلسلي"])
        self.connection_type.setStyleSheet(combo_stylesheet)
        self.connection_type.currentIndexChanged.connect(self.on_connection_type_changed)
        self.connection_type.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.connection_type.setMinimumHeight(control_height)
        self.connection_type.setLayoutDirection(Qt.RightToLeft)

        connection_type_layout.addWidget(connection_type_label)
        connection_type_layout.addWidget(self.connection_type)
        connection_layout.addLayout(connection_type_layout)

        # إعدادات USB
        self.usb_group = QGroupBox("إعدادات USB")
        usb_form = QFormLayout(self.usb_group)
        usb_form.setLabelAlignment(Qt.AlignRight)
        usb_form.setFormAlignment(Qt.AlignRight)

        self.vendor_id = QLineEdit()
        self.vendor_id.setPlaceholderText("مثال: 0x0416")

        usb_form.addRow("معرف البائع (Vendor ID):", self.vendor_id)

        self.product_id = QLineEdit()
        self.product_id.setPlaceholderText("مثال: 0x5011")

        usb_form.addRow("معرف المنتج (Product ID):", self.product_id)

        connection_layout.addWidget(self.usb_group)

        # إعدادات الشبكة
        self.network_group = QGroupBox("إعدادات الشبكة")
        network_form = QFormLayout(self.network_group)
        network_form.setLabelAlignment(Qt.AlignRight)
        network_form.setFormAlignment(Qt.AlignRight)

        self.ip_address = QLineEdit()
        self.ip_address.setPlaceholderText("مثال: *************")

        network_form.addRow("عنوان IP:", self.ip_address)

        self.port = QSpinBox()
        self.port.setRange(1, 65535)
        self.port.setValue(9100)
        self.port.setMinimumHeight(control_height)
        self.port.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        network_form.addRow("المنفذ:", self.port)

        connection_layout.addWidget(self.network_group)

        # إعدادات المنفذ التسلسلي
        self.serial_group = QGroupBox("إعدادات المنفذ التسلسلي")
        serial_form = QFormLayout(self.serial_group)
        serial_form.setLabelAlignment(Qt.AlignRight)
        serial_form.setFormAlignment(Qt.AlignRight)

        self.serial_port = QLineEdit()
        self.serial_port.setPlaceholderText("مثال: COM1 أو /dev/ttyS0")

        serial_form.addRow("المنفذ التسلسلي:", self.serial_port)

        self.baudrate = QComboBox()
        self.baudrate.addItems(["9600", "19200", "38400", "57600", "115200"])
        self.baudrate.setStyleSheet(combo_stylesheet)
        self.baudrate.setLayoutDirection(Qt.RightToLeft)
        serial_form.addRow("معدل الباود:", self.baudrate)

        connection_layout.addWidget(self.serial_group)
        thermal_layout.addWidget(connection_group)

        # تخطيط الأزرار السفلية
        bottom_button_layout = QHBoxLayout()
        bottom_button_layout.setSpacing(5)
        bottom_button_layout.setContentsMargins(0, 0, 0, 0)

        # زر اختبار الطباعة
        test_button = QPushButton("اختبار الطباعة")
        test_button.clicked.connect(self.safe_test_thermal_printer)
        bottom_button_layout.addWidget(test_button)

        # زر حفظ الإعدادات
        save_button = QPushButton("حفظ الإعدادات")
        save_button.clicked.connect(self.safe_save_thermal_settings)
        bottom_button_layout.addWidget(save_button)

        thermal_layout.addLayout(bottom_button_layout)
        thermal_layout.addStretch(1)

        # إضافة التبويبات
        tab_widget.addTab(regular_tab, "الطابعة العادية (A4)")
        tab_widget.addTab(thermal_tab, "الطابعة الحرارية")

        # إضافة التبويبات إلى التخطيط الرئيسي مباشرة
        content_layout.addWidget(tab_widget)

        # إضافة تخطيط للأزرار السفلية (التعليمات والإغلاق)
        bottom_buttons_layout = QHBoxLayout()
        bottom_buttons_layout.setContentsMargins(0, 15, 0, 0)  # زيادة المسافة من الأعلى
        bottom_buttons_layout.setSpacing(20)  # زيادة المسافة بين الأزرار
        bottom_buttons_layout.addStretch(1)

        # زر التعليمات
        help_button = QPushButton("عرض التعليمات")
        help_button.setMinimumWidth(150)
        help_button.setMinimumHeight(40)
        # استخدام نفس تنسيق الأزرار الأخرى في النافذة
        help_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 4px 8px;
                font-weight: bold;
                min-width: 80px;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
        """)
        help_button.clicked.connect(self.show_help_dialog)

        # زر الإغلاق - بنفس حجم وتنسيق زر التعليمات مع تغيير اللون فقط
        close_button = QPushButton("إغلاق")
        close_button.setMinimumWidth(150)
        close_button.setMinimumHeight(40)
        # استخدام نفس تنسيق الأزرار الأخرى مع تغيير اللون فقط
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #e53935;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 4px 8px;
                font-weight: bold;
                min-width: 80px;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
            QPushButton:pressed {
                background-color: #c62828;
            }
        """)
        close_button.clicked.connect(self.accept)  # استخدام accept() لإغلاق النافذة

        # إضافة الأزرار إلى التخطيط
        bottom_buttons_layout.addWidget(help_button)
        bottom_buttons_layout.addWidget(close_button)
        bottom_buttons_layout.addStretch(1)

        # تأكد من إضافة تخطيط الأزرار إلى التخطيط الرئيسي
        content_layout.addLayout(bottom_buttons_layout)
        content_layout.addStretch(1)  # إضافة مسافة مرنة في الأسفل

        # تأكد من أن الأزرار مرئية دائمًا
        help_button.setVisible(True)
        close_button.setVisible(True)

        # إنشاء نافذة التعليمات (مخفية في البداية)
        self.help_dialog = QMessageBox(self)
        self.help_dialog.setWindowTitle("تعليمات إعدادات الطابعة")
        self.help_dialog.setIcon(QMessageBox.Information)

        help_text = (
            "• يمكنك اختيار الطابعة العادية والطابعة الحرارية من القوائم المنسدلة.\n"
            "• استخدم زر 'تحديث قائمة الطابعات' للحصول على قائمة محدثة بالطابعات المتاحة.\n"
            "• لحفظ اختيار الطابعة العادية، انقر على زر 'حفظ الطابعة' في قاعدة البيانات.\n"
            "• لتعيين الطابعة كافتراضية للنظام، انقر على زر 'تعيين كافتراضية'.\n"
            "• يمكنك ضبط إعدادات الطابعة الحرارية مثل عرض الورق وعدد الأحرف في السطر.\n"
            "• لاختبار الطباعة على الطابعة الحرارية، انقر على زر 'اختبار الطباعة'."
        )

        self.help_dialog.setText("تعليمات استخدام إعدادات الطابعة")
        self.help_dialog.setInformativeText(help_text)
        self.help_dialog.setStandardButtons(QMessageBox.Ok)

        # ربط الإشارات بالوظائف الآمنة - استخدام currentTextChanged بدلاً من currentIndexChanged
        self.combo_thermal.currentTextChanged.connect(self.safe_save_thermal_printer)
        self.combo_regular.currentTextChanged.connect(self.safe_save_regular_printer)

        # تحديث واجهة المستخدم
        self.on_connection_type_changed()

    # --- الدوال الأخرى (load_settings, save_thermal_printer, save_and_set_default_printer) تبقى كما هي ---
    def load_settings(self):
        """تحميل إعدادات الطابعات المحفوظة من قاعدة البيانات"""
        if self._loading_settings: return
        self._loading_settings = True

        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute(f'SELECT الطابعة_الحرارية, الطابعة_العادية FROM "{SETTINGS_TABLE_NAME}" WHERE id = 1')
            result = cursor.fetchone()

            if result:
                thermal_printer_saved, regular_printer_saved = result

                self.combo_thermal.blockSignals(True)
                self.combo_regular.blockSignals(True)

                if thermal_printer_saved:
                    index_thermal = self.combo_thermal.findText(thermal_printer_saved, Qt.MatchFixedString)
                    if index_thermal >= 0:
                        self.combo_thermal.setCurrentIndex(index_thermal)
                    else:
                        print(f"تحذير: الطابعة الحرارية المحفوظة '{thermal_printer_saved}' غير موجودة.")
                        self.combo_thermal.setCurrentIndex(-1)

                if regular_printer_saved:
                    index_regular = self.combo_regular.findText(regular_printer_saved, Qt.MatchFixedString)
                    if index_regular >= 0:
                        self.combo_regular.setCurrentIndex(index_regular)
                    else:
                        print(f"تحذير: الطابعة العادية المحفوظة '{regular_printer_saved}' غير موجودة.")
                        self.combo_regular.setCurrentIndex(-1)

                self.combo_thermal.blockSignals(False)
                self.combo_regular.blockSignals(False)

            else:
                 print(f"لم يتم العثور على سجل الإعدادات (id=1) في جدول '{SETTINGS_TABLE_NAME}'.")

        except sqlite3.Error as e:
            print(f"خطأ أثناء تحميل إعدادات الطابعة: {e}")
            QMessageBox.warning(self, "خطأ تحميل الإعدادات",
                                f"لا يمكن تحميل إعدادات الطابعة من قاعدة البيانات:\n{e}")
        finally:
            if conn:
                conn.close()
            self._loading_settings = False


    def safe_save_thermal_printer(self):
        """وظيفة آمنة لحفظ اختيار الطابعة الحرارية"""
        try:
            self.save_thermal_printer()
        except Exception as e:
            print(f"خطأ عام أثناء حفظ الطابعة الحرارية: {str(e)}")
            # لا نعرض رسالة خطأ للمستخدم لتجنب إزعاجه

    def save_thermal_printer(self):
        """حفظ اختيار الطابعة الحرارية في قاعدة البيانات"""
        if self._loading_settings: return

        try:
            # الحصول على الطابعة المحددة بطريقة آمنة
            selected_printer = None
            if hasattr(self, 'combo_thermal') and self.combo_thermal is not None:
                selected_printer = self.combo_thermal.currentText()
                if not selected_printer or selected_printer.strip() == "":
                    selected_printer = None

            conn = None
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute(f'UPDATE "{SETTINGS_TABLE_NAME}" SET الطابعة_الحرارية = ? WHERE id = 1', (selected_printer,))
                conn.commit()
                print(f"تم حفظ الطابعة الحرارية: {selected_printer}")
            except sqlite3.Error as e:
                print(f"خطأ أثناء حفظ الطابعة الحرارية في قاعدة البيانات: {e}")
            finally:
                if conn:
                    conn.close()
        except Exception as e:
            print(f"خطأ عام أثناء حفظ الطابعة الحرارية: {str(e)}")

    def safe_save_regular_printer(self):
        """وظيفة آمنة لا تقوم بأي شيء عند تغيير اختيار الطابعة العادية"""
        # لا تقوم بأي شيء عند تغيير الاختيار
        print("تم تغيير اختيار الطابعة العادية - لن يتم حفظها تلقائياً")

    def save_regular_printer_only(self):
        """حفظ اختيار الطابعة العادية في قاعدة البيانات فقط بدون محاولة تعيينها كافتراضية

        ملاحظة مهمة: هذه الدالة تحفظ فقط اختيار الطابعة في قاعدة البيانات ولا تقوم بتعيينها كطابعة افتراضية للنظام.
        لتعيين الطابعة كافتراضية للنظام، يجب على المستخدم النقر على زر "تعيين كطابعة افتراضية للنظام".
        """
        if self._loading_settings: return

        try:
            # الحصول على الطابعة المحددة بطريقة آمنة
            selected_printer = None
            if hasattr(self, 'combo_regular') and self.combo_regular is not None:
                selected_printer = self.combo_regular.currentText()
                if not selected_printer or selected_printer.strip() == "":
                    selected_printer = None

            # حفظ في قاعدة البيانات فقط (بدون تعيين كافتراضية)
            conn = None
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute(f'UPDATE "{SETTINGS_TABLE_NAME}" SET الطابعة_العادية = ? WHERE id = 1', (selected_printer,))
                conn.commit()
                print(f"تم حفظ الطابعة العادية: {selected_printer} (بدون تعيينها كافتراضية)")

                # عرض رسالة نجاح للمستخدم
                QMessageBox.information(self, "تم الحفظ", f"تم حفظ الطابعة العادية '{selected_printer}' في قاعدة البيانات بنجاح.")
            except sqlite3.Error as e:
                print(f"خطأ أثناء حفظ الطابعة العادية في قاعدة البيانات: {e}")
                QMessageBox.critical(self, "خطأ في الحفظ", f"حدث خطأ أثناء حفظ الطابعة العادية في قاعدة البيانات:\n{e}")
            finally:
                if conn:
                    conn.close()
        except Exception as e:
            print(f"خطأ عام أثناء حفظ الطابعة العادية: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ عام أثناء حفظ الطابعة العادية:\n{str(e)}")

    def ensure_default_printer_script_exists(self):
        """التأكد من وجود سكريبت تعيين الطابعة الافتراضية، وإنشائه إذا لم يكن موجودًا"""
        script_path = os.path.join(os.path.dirname(__file__), "set_default_printer.py")

        if not os.path.exists(script_path):
            print("سكريبت تعيين الطابعة الافتراضية غير موجود. جاري إنشاؤه...")

            script_content = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
أداة مساعدة لتعيين الطابعة الافتراضية
تعمل كعملية منفصلة لتجنب توقف البرنامج الرئيسي
"""

import sys
import platform
import subprocess

def main():
    """الوظيفة الرئيسية"""
    if len(sys.argv) < 2:
        print("خطأ: يجب تحديد اسم الطابعة كمعامل")
        return 1

    printer_name = sys.argv[1]
    print(f"محاولة تعيين '{printer_name}' كطابعة افتراضية...")

    success = False
    error_msg = "سبب غير معروف"
    os_name = platform.system()

    try:
        if os_name == "Windows":
            # محاولة استخدام win32print أولاً
            try:
                import win32print
                win32print.SetDefaultPrinter(printer_name)
                success = True
                print(f"تم تعيين الطابعة '{printer_name}' كطابعة افتراضية بنجاح (Windows).")
            except ImportError:
                print("مكتبة win32print غير متاحة. سيتم استخدام الطريقة البديلة.")
                # استخدام rundll32 كبديل
                try:
                    cmd = f'rundll32 printui.dll,PrintUIEntry /y /n "{printer_name}"'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)

                    if result.returncode == 0:
                        success = True
                        print(f"تم تعيين الطابعة '{printer_name}' كطابعة افتراضية بنجاح (Windows rundll32).")
                    else:
                        error_msg = f"فشل تعيين الطابعة الافتراضية باستخدام rundll32. رمز الخطأ: {result.returncode}"
                        print(error_msg)
                except Exception as sub_e:
                    error_msg = f"فشل تعيين الطابعة الافتراضية باستخدام الطريقة البديلة: {str(sub_e)}"
                    print(error_msg)
            except Exception as e:
                error_msg = f"خطأ في تعيين الطابعة الافتراضية باستخدام win32print: {str(e)}"
                print(error_msg)

                # محاولة استخدام rundll32 كبديل
                try:
                    cmd = f'rundll32 printui.dll,PrintUIEntry /y /n "{printer_name}"'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)

                    if result.returncode == 0:
                        success = True
                        print(f"تم تعيين الطابعة '{printer_name}' كطابعة افتراضية بنجاح (Windows rundll32).")
                    else:
                        error_msg = f"فشل تعيين الطابعة الافتراضية باستخدام rundll32. رمز الخطأ: {result.returncode}"
                        print(error_msg)
                except Exception as sub_e:
                    error_msg = f"فشل تعيين الطابعة الافتراضية باستخدام الطريقة البديلة: {str(sub_e)}"
                    print(error_msg)

        elif os_name in ["Linux", "Darwin"]:
            try:
                subprocess.run(['lpoptions', '-d', printer_name],
                            check=True, capture_output=True, text=True, timeout=5)
                success = True
                print(f"تم تعيين الطابعة الافتراضية بنجاح ({os_name} - CUPS).")
            except Exception as e:
                error_msg = f"خطأ في تعيين الطابعة الافتراضية على {os_name}: {str(e)}"
                print(error_msg)
        else:
            error_msg = f"نظام التشغيل '{os_name}' غير مدعوم لتعيين الطابعة الافتراضية تلقائيًا."
            print(error_msg)

    except Exception as e:
        error_msg = f"خطأ عام أثناء تعيين الطابعة الافتراضية: {str(e)}"
        print(error_msg)

    if success:
        print(f"تم تعيين الطابعة '{printer_name}' كطابعة افتراضية للنظام بنجاح.")
        return 0
    else:
        print(f"فشل تعيين '{printer_name}' كطابعة افتراضية. السبب: {error_msg}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
'''

            try:
                with open(script_path, 'w', encoding='utf-8') as f:
                    f.write(script_content)
                print(f"تم إنشاء سكريبت تعيين الطابعة الافتراضية بنجاح: {script_path}")
                return True
            except Exception as e:
                print(f"فشل إنشاء سكريبت تعيين الطابعة الافتراضية: {str(e)}")
                return False
        else:
            print(f"سكريبت تعيين الطابعة الافتراضية موجود: {script_path}")
            return True

    def safe_set_default_printer(self):
        """وظيفة آمنة لتعيين الطابعة العادية كافتراضية للنظام باستخدام عملية منفصلة"""
        try:
            # الحصول على الطابعة المحددة بطريقة آمنة
            selected_printer = None
            if hasattr(self, 'combo_regular') and self.combo_regular is not None:
                selected_printer = self.combo_regular.currentText()
                if not selected_printer or selected_printer.strip() == "":
                    selected_printer = None

            # التحقق من وجود طابعة محددة
            if selected_printer is None:
                print("لم يتم اختيار طابعة عادية، لا يمكن التعيين كافتراضي.")
                QMessageBox.warning(self, "تنبيه", "لم يتم اختيار طابعة عادية، لا يمكن التعيين كافتراضي.")
                return

            # عرض رسالة تأكيد للمستخدم
            reply = QMessageBox.question(self, "تأكيد تعيين الطابعة الافتراضية",
                                        f"هل أنت متأكد من تعيين الطابعة '{selected_printer}' كطابعة افتراضية للنظام؟",
                                        QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

            if reply == QMessageBox.No:
                return

            # التأكد من وجود سكريبت تعيين الطابعة الافتراضية
            if not self.ensure_default_printer_script_exists():
                print("لا يمكن تعيين الطابعة الافتراضية: فشل إنشاء سكريبت تعيين الطابعة الافتراضية.")
                QMessageBox.critical(self, "خطأ", "لا يمكن تعيين الطابعة الافتراضية: فشل إنشاء سكريبت تعيين الطابعة الافتراضية.")
                return

            # تشغيل سكريبت منفصل لتعيين الطابعة الافتراضية
            # هذا يضمن أن أي أخطاء لن تؤثر على البرنامج الرئيسي
            script_path = os.path.join(os.path.dirname(__file__), "set_default_printer.py")
            cmd = [sys.executable, script_path, selected_printer]

            # استخدام subprocess.Popen بدلاً من run لتجنب انتظار انتهاء العملية
            if platform.system() == "Windows":
                # استخدام creationflags فقط في Windows
                subprocess.Popen(cmd,
                               stdout=subprocess.PIPE,
                               stderr=subprocess.PIPE,
                               text=True,
                               creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                # في أنظمة التشغيل الأخرى
                subprocess.Popen(cmd,
                               stdout=subprocess.PIPE,
                               stderr=subprocess.PIPE,
                               text=True)

            print(f"تم بدء عملية تعيين الطابعة '{selected_printer}' كافتراضية في عملية منفصلة.")
            QMessageBox.information(self, "تم", f"تم بدء عملية تعيين الطابعة '{selected_printer}' كافتراضية للنظام.")

        except Exception as e:
            print(f"خطأ عام أثناء بدء عملية تعيين الطابعة الافتراضية: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء محاولة تعيين الطابعة الافتراضية:\n{str(e)}")

    def set_default_printer(self):
        """تعيين الطابعة العادية كافتراضية للنظام باستخدام طريقة آمنة"""
        try:
            # الحصول على الطابعة المحددة بطريقة آمنة
            selected_printer = None
            if hasattr(self, 'combo_regular') and self.combo_regular is not None:
                selected_printer = self.combo_regular.currentText()
                if not selected_printer or selected_printer.strip() == "":
                    selected_printer = None

            # التحقق من وجود طابعة محددة
            if selected_printer is None:
                print("لم يتم اختيار طابعة عادية، لا يمكن التعيين كافتراضي.")
                return

            print(f"محاولة تعيين '{selected_printer}' كطابعة افتراضية...")
            success = False
            error_msg = "سبب غير معروف"
            os_name = platform.system()

            # استخدام الطريقة المناسبة لتعيين الطابعة الافتراضية
            if os_name == "Windows":
                # استخدام rundll32 مباشرة بدلاً من محاولة استخدام win32print
                try:
                    cmd = f'rundll32 printui.dll,PrintUIEntry /y /n "{selected_printer}"'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)

                    if result.returncode == 0:
                        success = True
                        print(f"تم تعيين الطابعة '{selected_printer}' كطابعة افتراضية بنجاح (Windows rundll32).")
                    else:
                        error_msg = f"فشل تعيين الطابعة الافتراضية باستخدام rundll32. رمز الخطأ: {result.returncode}"
                        print(error_msg)
                except Exception as e:
                    error_msg = f"خطأ غير متوقع: {str(e)}"
                    print(error_msg)

            # استخدام CUPS في Linux/macOS
            elif os_name in ["Linux", "Darwin"]:
                try:
                    subprocess.run(['lpoptions', '-d', selected_printer],
                                check=True, capture_output=True, text=True, timeout=5)
                    success = True
                    print(f"تم تعيين الطابعة الافتراضية بنجاح ({os_name} - CUPS).")
                except Exception as e:
                    error_msg = f"خطأ في تعيين الطابعة الافتراضية على {os_name}: {str(e)}"
                    print(error_msg)
            else:
                error_msg = f"نظام التشغيل '{os_name}' غير مدعوم لتعيين الطابعة الافتراضية تلقائيًا."
                print(error_msg)

            if success:
                print(f"تم تعيين الطابعة '{selected_printer}' كطابعة افتراضية للنظام بنجاح.")
            else:
                print(f"فشل تعيين '{selected_printer}' كطابعة افتراضية. السبب: {error_msg}")

        except Exception as e:
            print(f"خطأ عام أثناء تعيين الطابعة الافتراضية: {str(e)}")

    def save_and_set_default_printer(self):
        """حفظ اختيار الطابعة العادية ومحاولة تعيينها كافتراضية للنظام"""
        if self._loading_settings: return

        try:
            # أولاً: حفظ الطابعة في قاعدة البيانات
            self.save_regular_printer_only()

            # ثانياً: محاولة تعيينها كافتراضية
            self.set_default_printer()
        except Exception as e:
            print(f"خطأ عام أثناء حفظ وتعيين الطابعة العادية: {str(e)}")

    def on_connection_type_changed(self):
        """تحديث واجهة المستخدم بناءً على نوع الاتصال المحدد"""
        if not hasattr(self, 'usb_group'):
            return  # لم يتم إنشاء العناصر بعد

        connection_type = self.connection_type.currentText()

        # إخفاء جميع المجموعات
        self.usb_group.setVisible(False)
        self.network_group.setVisible(False)
        self.serial_group.setVisible(False)

        # إظهار المجموعة المناسبة
        if connection_type == "USB":
            self.usb_group.setVisible(True)
        elif connection_type == "شبكة":
            self.network_group.setVisible(True)
        elif connection_type == "تسلسلي":
            self.serial_group.setVisible(True)

    def load_settings(self):
        """تحميل إعدادات الطابعات المحفوظة من قاعدة البيانات"""
        if self._loading_settings: return
        self._loading_settings = True

        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # استرجاع جميع الإعدادات
            cursor.execute(f'SELECT * FROM "{SETTINGS_TABLE_NAME}" WHERE id = 1')
            result = cursor.fetchone()

            if result:
                # منع تشغيل الإشارات أثناء تحميل الإعدادات
                self.combo_thermal.blockSignals(True)
                self.combo_regular.blockSignals(True)
                self.connection_type.blockSignals(True)
                self.paper_width.blockSignals(True)

                # الطابعة الحرارية والعادية
                thermal_printer_saved, regular_printer_saved = result[1], result[2]

                if thermal_printer_saved:
                    index_thermal = self.combo_thermal.findText(thermal_printer_saved, Qt.MatchFixedString)
                    if index_thermal >= 0:
                        self.combo_thermal.setCurrentIndex(index_thermal)
                    else:
                        print(f"تحذير: الطابعة الحرارية المحفوظة '{thermal_printer_saved}' غير موجودة.")
                        self.combo_thermal.setCurrentIndex(-1)

                if regular_printer_saved:
                    index_regular = self.combo_regular.findText(regular_printer_saved, Qt.MatchFixedString)
                    if index_regular >= 0:
                        self.combo_regular.setCurrentIndex(index_regular)
                    else:
                        print(f"تحذير: الطابعة العادية المحفوظة '{regular_printer_saved}' غير موجودة.")
                        self.combo_regular.setCurrentIndex(-1)

                # إعدادات الطابعة الحرارية المتقدمة
                if len(result) > 3:
                    # عرض الورق
                    paper_width = result[3] or 80
                    if paper_width == 58:
                        self.paper_width.setCurrentIndex(0)
                    elif paper_width == 80:
                        self.paper_width.setCurrentIndex(1)
                    elif paper_width == 112:
                        self.paper_width.setCurrentIndex(2)

                    # نوع الاتصال
                    connection_type = result[4] or "windows"
                    if connection_type.lower() == "usb":
                        self.connection_type.setCurrentIndex(1)
                    elif connection_type.lower() == "network":
                        self.connection_type.setCurrentIndex(2)
                    elif connection_type.lower() == "serial":
                        self.connection_type.setCurrentIndex(3)
                    else:
                        self.connection_type.setCurrentIndex(0)

                    # إعدادات USB
                    self.vendor_id.setText(result[5] or "")
                    self.product_id.setText(result[6] or "")

                    # إعدادات الشبكة
                    self.ip_address.setText(result[7] or "")
                    self.port.setValue(result[8] or 9100)

                    # إعدادات المنفذ التسلسلي
                    self.serial_port.setText(result[9] or "")
                    baudrate = result[10] or 9600
                    baudrate_index = self.baudrate.findText(str(baudrate))
                    if baudrate_index >= 0:
                        self.baudrate.setCurrentIndex(baudrate_index)

                    # قص الورق
                    self.cut_paper.setChecked(bool(result[11]))

                    # عدد الأحرف في السطر
                    self.char_per_line.setValue(result[12] or 42)

                # إعادة تفعيل الإشارات
                self.combo_thermal.blockSignals(False)
                self.combo_regular.blockSignals(False)
                self.connection_type.blockSignals(False)
                self.paper_width.blockSignals(False)

                # تحديث واجهة المستخدم
                self.on_connection_type_changed()

            else:
                 print(f"لم يتم العثور على سجل الإعدادات (id=1) في جدول '{SETTINGS_TABLE_NAME}'.")

        except sqlite3.Error as e:
            print(f"خطأ أثناء تحميل إعدادات الطابعة: {e}")
            QMessageBox.warning(self, "خطأ تحميل الإعدادات",
                                f"لا يمكن تحميل إعدادات الطابعة من قاعدة البيانات:\n{e}")
        finally:
            if conn:
                conn.close()
            self._loading_settings = False

    def safe_save_thermal_settings(self):
        """وظيفة آمنة لحفظ إعدادات الطابعة الحرارية المتقدمة"""
        try:
            self.save_thermal_settings()
        except Exception as e:
            print(f"خطأ عام أثناء حفظ إعدادات الطابعة الحرارية: {str(e)}")
            # لا نعرض رسالة خطأ للمستخدم لتجنب إزعاجه

    def save_thermal_settings(self):
        """حفظ إعدادات الطابعة الحرارية المتقدمة"""
        if self._loading_settings: return

        try:
            # استخراج القيم من واجهة المستخدم بطريقة آمنة
            thermal_printer = None
            if hasattr(self, 'combo_thermal') and self.combo_thermal is not None:
                thermal_printer = self.combo_thermal.currentText()
                if not thermal_printer or thermal_printer.strip() == "":
                    thermal_printer = None

            # عرض الورق
            paper_width = 80  # القيمة الافتراضية
            if hasattr(self, 'paper_width') and self.paper_width is not None:
                paper_width_text = self.paper_width.currentText()
                if "58" in paper_width_text:
                    paper_width = 58
                elif "112" in paper_width_text:
                    paper_width = 112

            # نوع الاتصال
            connection_type = "windows"  # القيمة الافتراضية
            if hasattr(self, 'connection_type') and self.connection_type is not None:
                connection_type_text = self.connection_type.currentText().lower()
                if connection_type_text == "usb":
                    connection_type = "usb"
                elif connection_type_text == "شبكة":
                    connection_type = "network"
                elif connection_type_text == "تسلسلي":
                    connection_type = "serial"

            # إعدادات USB
            vendor_id = ""
            product_id = ""
            if hasattr(self, 'vendor_id') and self.vendor_id is not None:
                vendor_id = self.vendor_id.text()
            if hasattr(self, 'product_id') and self.product_id is not None:
                product_id = self.product_id.text()

            # إعدادات الشبكة
            ip_address = ""
            port = 9100
            if hasattr(self, 'ip_address') and self.ip_address is not None:
                ip_address = self.ip_address.text()
            if hasattr(self, 'port') and self.port is not None:
                port = self.port.value()

            # إعدادات المنفذ التسلسلي
            serial_port = ""
            baudrate = 9600
            if hasattr(self, 'serial_port') and self.serial_port is not None:
                serial_port = self.serial_port.text()
            if hasattr(self, 'baudrate') and self.baudrate is not None:
                try:
                    baudrate = int(self.baudrate.currentText())
                except (ValueError, TypeError):
                    baudrate = 9600

            # قص الورق
            cut_paper = 1
            if hasattr(self, 'cut_paper') and self.cut_paper is not None:
                cut_paper = 1 if self.cut_paper.isChecked() else 0

            # عدد الأحرف في السطر
            char_per_line = 42
            if hasattr(self, 'char_per_line') and self.char_per_line is not None:
                char_per_line = self.char_per_line.value()

            # حفظ الإعدادات في قاعدة البيانات
            conn = None
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                # تحديث الإعدادات
                cursor.execute(f'''
                    UPDATE "{SETTINGS_TABLE_NAME}"
                    SET الطابعة_الحرارية = ?,
                        عرض_الورق = ?,
                        نوع_الاتصال = ?,
                        معرف_البائع = ?,
                        معرف_المنتج = ?,
                        عنوان_IP = ?,
                        المنفذ = ?,
                        المنفذ_التسلسلي = ?,
                        معدل_الباود = ?,
                        قص_الورق = ?,
                        عدد_الأحرف_في_السطر = ?
                    WHERE id = 1
                ''', (thermal_printer, paper_width, connection_type, vendor_id, product_id,
                    ip_address, port, serial_port, baudrate, cut_paper, char_per_line))

                conn.commit()
                print("تم حفظ إعدادات الطابعة الحرارية بنجاح")
                print(f"القيم المحفوظة: طابعة={thermal_printer}, عرض={paper_width}, اتصال={connection_type}, أحرف={char_per_line}")
            except sqlite3.Error as e:
                print(f"خطأ أثناء حفظ إعدادات الطابعة الحرارية في قاعدة البيانات: {e}")
            finally:
                if conn:
                    conn.close()
        except Exception as e:
            print(f"خطأ عام أثناء حفظ إعدادات الطابعة الحرارية: {str(e)}")

    # تم إلغاء دوال التصفية لأننا استخدمنا مربعات تحرير وسرد بسيطة بدون تصفية

    def safe_test_thermal_printer(self):
        """وظيفة آمنة لاختبار الطباعة على الطابعة الحرارية"""
        try:
            self.test_thermal_printer()
        except Exception as e:
            print(f"خطأ عام أثناء اختبار الطباعة: {str(e)}")
            # لا نعرض رسالة خطأ للمستخدم لتجنب إزعاجه

    def test_thermal_printer(self):
        """اختبار الطباعة على الطابعة الحرارية"""
        try:
            # محاولة استيراد وحدة الطباعة الحرارية
            try:
                # استخدام استيراد نسبي للتأكد من استيراد الوحدة من نفس المجلد
                import sys
                import os

                # إضافة المجلد الحالي إلى مسار البحث
                current_dir = os.path.dirname(os.path.abspath(__file__))
                if current_dir not in sys.path:
                    sys.path.insert(0, current_dir)

                try:
                    import thermal_printer
                except ImportError:
                    print("خطأ: وحدة 'thermal_printer' غير مثبتة. يرجى التأكد من توفرها.")
                    QMessageBox.critical(self, "وحدة مفقودة", "وحدة 'thermal_printer' غير مثبتة. يرجى تثبيتها أو توفير الملف المطلوب.")
                    return

                # حفظ الإعدادات الحالية أولاً بطريقة آمنة
                self.safe_save_thermal_settings()

                # إنشاء محتوى اختبار
                test_content = """
اختبار الطباعة الحرارية
====================
تم إنشاء هذا النموذج لاختبار
إعدادات الطابعة الحرارية

1234567890
أبجد هوز حطي كلمن
====================
                """

                # طباعة المحتوى
                try:
                    success = thermal_printer.thermal_print(test_content)

                    if success:
                        print("تم إرسال اختبار الطباعة بنجاح")
                    else:
                        print("فشل إرسال اختبار الطباعة")
                except Exception as e:
                    print(f"خطأ أثناء الطباعة: {str(e)}")
                    return

            except ImportError:
                print("وحدة الطباعة الحرارية غير متوفرة. تأكد من وجود ملف thermal_printer.py")

        except Exception as e:
            print(f"خطأ عام أثناء اختبار الطباعة: {str(e)}")

    def resizeEvent(self, event):
        """معالجة حدث تغيير حجم النافذة"""
        super().resizeEvent(event)

        # لا نحتاج لتعديل حجم القوائم المنسدلة لأننا نستخدم عرض ثابت (250 بكسل)

    def show_help_dialog(self):
        """عرض نافذة التعليمات"""
        try:
            # عرض نافذة التعليمات
            self.help_dialog.exec_()
        except Exception as e:
            print(f"خطأ في عرض نافذة التعليمات: {str(e)}")


# --- تشغيل التطبيق ---
if __name__ == '__main__':
    import datetime
    now_str = "2025-03-26 18:22:33" # Updated time
    now = datetime.datetime.strptime(now_str, '%Y-%m-%d %H:%M:%S')
    print(f"Current date and time: {now.strftime('%Y-%m-%d %H:%M:%S')}")
    print("Location: Tangier, Tangier-Tétouan-Al Hoceima, Morocco") # Updated location

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft) # RTL Application Layout

    # إنشاء نافذة مستقلة
    mainWin = Sub7Window()

    # تعيين عنوان النافذة
    mainWin.setWindowTitle("إعدادات الطابعة")

    # تعيين حجم النافذة
    mainWin.setMinimumSize(800, 600)

    # استخدام exec_() بدلاً من show() لضمان اتساق السلوك
    result = mainWin.exec_()

    # الخروج من التطبيق بعد إغلاق النافذة
    sys.exit(result)