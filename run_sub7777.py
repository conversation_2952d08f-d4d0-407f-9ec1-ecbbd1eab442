#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل نافذة البحث الحديثة
تشغيل مبسط لـ sub7777_window.py
"""

import os
import sys
from pathlib import Path

def check_requirements():
    """فحص المتطلبات الأساسية"""
    required_modules = ['flask', 'pandas', 'openpyxl']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("❌ المكتبات التالية غير مثبتة:")
        for module in missing_modules:
            print(f"   - {module}")
        print("\n💡 لتثبيت المكتبات المطلوبة:")
        print(f"   pip install {' '.join(missing_modules)}")
        return False
    
    return True

def check_database():
    """فحص وجود قاعدة البيانات"""
    script_dir = Path(__file__).parent
    db_path = script_dir / "data.db"
    
    if not db_path.exists():
        print("⚠️  تحذير: لم يتم العثور على قاعدة البيانات")
        print(f"📁 المسار المتوقع: {db_path}")
        print("💡 سيتم تشغيل التطبيق بدون بيانات حقيقية")
        return False
    
    print(f"✅ تم العثور على قاعدة البيانات: {db_path}")
    return True

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🚀 تشغيل نافذة البحث الحديثة")
    print("   برنامج المعين في الحراسة العامة")
    print("=" * 60)
    
    # فحص المتطلبات
    print("🔍 فحص المتطلبات...")
    if not check_requirements():
        print("\n❌ فشل في فحص المتطلبات")
        input("اضغط Enter للخروج...")
        return
    
    print("✅ جميع المكتبات المطلوبة متوفرة")
    
    # فحص قاعدة البيانات
    print("\n📊 فحص قاعدة البيانات...")
    db_exists = check_database()
    
    # استيراد وتشغيل التطبيق
    try:
        print("\n📦 تحميل التطبيق...")
        from sub7777_window import Sub7777Window
        
        print("🌐 إنشاء خادم الويب...")
        window = Sub7777Window()
        
        print("\n" + "=" * 60)
        print("🎉 التطبيق جاهز للتشغيل!")
        print("🔗 الرابط: http://127.0.0.1:5000")
        print("🌐 سيتم فتح المتصفح تلقائياً...")
        print("⏹️  للإيقاف: اضغط Ctrl+C")
        print("=" * 60)
        
        # تشغيل التطبيق
        window.run(host='127.0.0.1', port=5000, debug=False)
        
    except ImportError as e:
        print(f"\n❌ خطأ في استيراد التطبيق: {e}")
        print("💡 تأكد من وجود ملف sub7777_window.py في نفس المجلد")
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف التطبيق بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التطبيق: {e}")
        print("\n💡 نصائح لحل المشكلة:")
        print("   - تأكد من عدم استخدام المنفذ 5000 من قبل تطبيق آخر")
        print("   - تأكد من صحة ملف قاعدة البيانات")
        print("   - أعد تشغيل البرنامج كمدير")
    finally:
        print("\n🔚 انتهى التطبيق")
        input("اضغط Enter للخروج...")

if __name__ == '__main__':
    main()
