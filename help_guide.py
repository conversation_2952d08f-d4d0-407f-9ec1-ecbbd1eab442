"""
دليل المساعدة لنظام إدارة البيانات
Help Guide for Data Management System
"""

from PyQt5.QtWidgets import QMessageBox, QTextEdit, QVBoxLayout, QDialog, QPushButton
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont


def show_help_guide(parent_widget):
    """
    عرض دليل المساعدة لاستخدام نظام استيراد البيانات
    Display help guide for using the data import system
    """
    help_dialog = QDialog(parent_widget)
    help_dialog.setWindowTitle("دليل استخدام النظام - System Help Guide")
    help_dialog.setMinimumSize(600, 500)
    help_dialog.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
    
    layout = QVBoxLayout()
    
    # إنشاء منطقة النص
    help_text = QTextEdit()
    help_text.setReadOnly(True)
    help_text.setFont(QFont("Arial", 11))
    
    # نص المساعدة
    help_content = """
    <h2 style="color: #2c3e50; text-align: center;">دليل استخدام نظام إدارة البيانات</h2>
    <h3 style="color: #34495e;">Data Management System Help Guide</h3>
    
    <hr>
    
    <h3 style="color: #2980b9;">📁 استيراد البيانات - Data Import</h3>
    <ul>
        <li><b>استيراد بيانات الأساتذة:</b> اختر ملف Excel يحتوي على بيانات الأساتذة</li>
        <li><b>استيراد بيانات المسار:</b> اختر ملف Excel يحتوي على بيانات المسارات</li>
        <li><b>استيراد الرموز السرية:</b> يمكنك اختيار عدة ملفات Excel للرموز السرية</li>
        <li><b>Teachers Import:</b> Select Excel file containing teachers data</li>
        <li><b>Path Import:</b> Select Excel file containing paths data</li>
        <li><b>Secret Codes:</b> You can select multiple Excel files for secret codes</li>
    </ul>
    
    <h3 style="color: #27ae60;">⚙️ الإعدادات - Settings</h3>
    <ul>
        <li><b>إعدادات المؤسسة:</b> تحديث معلومات المؤسسة</li>
        <li><b>صيانة قاعدة البيانات:</b> أدوات الصيانة والنسخ الاحتياطي</li>
        <li><b>Institution Settings:</b> Update institution information</li>
        <li><b>Database Maintenance:</b> Maintenance and backup tools</li>
    </ul>
    
    <h3 style="color: #e74c3c;">📋 نصائح مهمة - Important Tips</h3>
    <ul>
        <li>تأكد من تنسيق ملفات Excel قبل الاستيراد</li>
        <li>قم بإنشاء نسخة احتياطية قبل استيراد البيانات</li>
        <li>راقب شريط التقدم أثناء عملية الاستيراد</li>
        <li>Make sure Excel files are properly formatted before import</li>
        <li>Create backup before importing data</li>
        <li>Monitor progress bar during import process</li>
    </ul>
    
    <h3 style="color: #9b59b6;">🔧 حل المشاكل - Troubleshooting</h3>
    <ul>
        <li>إذا فشل الاستيراد، تحقق من تنسيق الملف</li>
        <li>تأكد من وجود الأعمدة المطلوبة في ملف Excel</li>
        <li>راجع رسائل الخطأ في منطقة السجلات</li>
        <li>If import fails, check file format</li>
        <li>Make sure required columns exist in Excel file</li>
        <li>Check error messages in log area</li>
    </ul>
    
    <hr>
    <p style="text-align: center; color: #7f8c8d; font-style: italic;">
        نظام إدارة البيانات - Data Management System<br>
        للمساعدة التقنية، راجع السجلات أو اتصل بالدعم الفني
    </p>
    """
    
    help_text.setHtml(help_content)
    layout.addWidget(help_text)
    
    # زr إغلاق
    close_button = QPushButton("إغلاق - Close")
    close_button.clicked.connect(help_dialog.accept)
    close_button.setStyleSheet("""
        QPushButton {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #2980b9;
        }
    """)
    layout.addWidget(close_button)
    
    help_dialog.setLayout(layout)
    help_dialog.exec_()


if __name__ == "__main__":
    # اختبار الدالة
    from PyQt5.QtWidgets import QApplication
    import sys
    
    app = QApplication(sys.argv)
    show_help_guide(None)
    sys.exit(app.exec_())
