#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة isOpen() في sub4_window
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon

def test_isopen_fix():
    """اختبار إصلاح مشكلة isOpen()"""
    print("🔧 اختبار إصلاح مشكلة isOpen() في sub4_window")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    
    try:
        from main_window import MainWindow
        print("✅ تم استيراد MainWindow")
        
        main_window = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # فحص إصلاح مشكلة isOpen
        def check_isopen_fix():
            print("\n🔍 فحص إصلاح مشكلة isOpen():")
            
            # التحقق من تبويب اللوائح والأقسام
            if "lists_sections" in main_window.windows:
                window = main_window.windows["lists_sections"]
                print(f"   📄 نوع النافذة: {type(window).__name__}")
                
                # التحقق من أنها ليست PlaceholderWindow
                if "PlaceholderWindow" in type(window).__name__:
                    print("   ❌ النافذة هي PlaceholderWindow - لم يتم تحميل sub4_window بشكل صحيح")
                    
                    # البحث عن رسائل الخطأ
                    print("\n🔍 البحث عن رسائل الخطأ المحتملة:")
                    print("   • تحقق من وحدة التحكم للبحث عن رسائل خطأ")
                    print("   • ابحث عن 'isOpen' في رسائل الخطأ")
                    print("   • تأكد من عدم وجود أخطاء في sub4_window.py")
                    
                else:
                    print("   ✅ النافذة من النوع الصحيح (Sub4Window)")
                    
                    # اختبار وظائف النافذة
                    print("\n🧪 اختبار وظائف النافذة:")
                    
                    # التحقق من وجود قاعدة البيانات
                    if hasattr(window, 'db'):
                        print("   ✅ النافذة تحتوي على اتصال قاعدة البيانات")
                        print(f"   📄 نوع قاعدة البيانات: {type(window.db)}")
                        
                        # اختبار الاتصال
                        try:
                            cursor = window.db.cursor()
                            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' LIMIT 1;")
                            result = cursor.fetchone()
                            if result:
                                print("   ✅ اتصال قاعدة البيانات يعمل بشكل صحيح")
                            else:
                                print("   ⚠️ قاعدة البيانات فارغة أو لا تحتوي على جداول")
                        except Exception as e:
                            print(f"   ❌ خطأ في اختبار قاعدة البيانات: {e}")
                    else:
                        print("   ❌ النافذة لا تحتوي على اتصال قاعدة البيانات")
                    
                    # التحقق من الوظائف الأساسية
                    if hasattr(window, 'table_lists'):
                        print("   ✅ تم العثور على table_lists")
                    else:
                        print("   ❌ لم يتم العثور على table_lists")
                    
                    if hasattr(window, 'load_initial_data'):
                        print("   ✅ تم العثور على load_initial_data")
                        
                        # اختبار تحميل البيانات
                        try:
                            print("   🧪 اختبار تحميل البيانات...")
                            window.load_initial_data()
                            print("   ✅ تم تحميل البيانات بنجاح (لا توجد أخطاء isOpen)")
                        except Exception as e:
                            if "isOpen" in str(e):
                                print(f"   ❌ لا يزال هناك خطأ isOpen: {e}")
                            else:
                                print(f"   ⚠️ خطأ آخر في تحميل البيانات: {e}")
                    else:
                        print("   ❌ لم يتم العثور على load_initial_data")
                        
            else:
                print("   ❌ النافذة غير موجودة في قاموس النوافذ")
            
            # اختبار النقر على التبويب
            print("\n🎯 اختبار النقر على تبويب اللوائح والأقسام:")
            
            # البحث عن التبويب
            lists_sections_index = -1
            tab_count = main_window.tabWidget.count()
            
            for i in range(tab_count):
                tab_text = main_window.tabWidget.tabText(i)
                tab_data = main_window.tabWidget.tabData(i)
                
                if tab_text == "اللوائح والأقسام" or tab_data == "lists_sections":
                    lists_sections_index = i
                    print(f"   ✅ تم العثور على التبويب في الفهرس {i}")
                    break
            
            if lists_sections_index >= 0:
                print("   🎯 محاولة النقر على التبويب...")
                
                # محاكاة النقر على التبويب
                main_window.tabWidget.setCurrentIndex(lists_sections_index)
                current_index = main_window.tabWidget.currentIndex()
                
                if current_index == lists_sections_index:
                    print("   ✅ تم التنقل للتبويب بنجاح!")
                    print("   📋 راقب وحدة التحكم للتأكد من عدم ظهور أخطاء isOpen")
                else:
                    print("   ❌ فشل في التنقل للتبويب")
            else:
                print("   ❌ لم يتم العثور على التبويب")
        
        # فحص الإصلاح بعد ثانية واحدة
        QTimer.singleShot(1000, check_isopen_fix)
        
        main_window.show()
        main_window.setWindowTitle("اختبار إصلاح isOpen() - sub4_window")
        
        print("\n📋 تعليمات الاختبار:")
        print("1. راقب رسائل وحدة التحكم")
        print("2. ابحث عن أي رسائل خطأ تحتوي على 'isOpen'")
        print("3. انقر على تبويب 'اللوائح والأقسام'")
        print("4. تحقق من عدم ظهور أخطاء جديدة")
        print("5. جرب استخدام وظائف النافذة")
        
        print("\n🔧 الإصلاحات المطبقة:")
        print("   ✅ إزالة جميع استدعاءات db.isOpen()")
        print("   ✅ استبدالها بـ التحقق من وجود db فقط")
        print("   ✅ إزالة محاولات فتح/إغلاق sqlite3.Connection")
        print("   ✅ الاحتفاظ بـ isOpen() للـ QSqlDatabase فقط")
        
        print("\n📊 النتائج المتوقعة:")
        print("   ✅ لا توجد رسائل خطأ عن 'isOpen'")
        print("   ✅ النافذة من نوع Sub4Window وليس PlaceholderWindow")
        print("   ✅ تحميل البيانات يعمل بدون أخطاء")
        print("   ✅ جميع وظائف النافذة تعمل بشكل طبيعي")
        
        print("\n⚠️ ملاحظات:")
        print("   • sqlite3.Connection لا يحتوي على دالة isOpen()")
        print("   • QSqlDatabase يحتوي على isOpen() ولم يتم تعديله")
        print("   • التحقق من وجود الاتصال كافي لـ sqlite3")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return 1

def show_fix_summary():
    """عرض ملخص الإصلاحات"""
    print("\n" + "=" * 60)
    print("🔧 ملخص إصلاحات مشكلة isOpen()")
    print("=" * 60)
    
    fixes = [
        ("المشكلة الأصلية", "'sqlite3.Connection' object has no attribute 'isOpen'"),
        ("السبب", "استخدام isOpen() مع sqlite3.Connection بدلاً من QSqlDatabase"),
        ("الحل", "إزالة isOpen() واستبدالها بالتحقق من وجود الاتصال"),
        ("الأماكن المصلحة", "11 مكان في sub4_window.py"),
        ("النتيجة", "sub4_window يعمل بدون أخطاء isOpen")
    ]
    
    print("\n🔧 تفاصيل الإصلاحات:")
    for title, detail in fixes:
        print(f"   {title}: {detail}")
    
    print("\n📝 التغييرات المطبقة:")
    print("   قبل: if not self.db.isOpen():")
    print("   بعد: if not self.db:")
    print()
    print("   قبل: if not self.db or not self.db.isOpen():")
    print("   بعد: if not self.db:")

if __name__ == "__main__":
    print("🔧 مرحباً بك في اختبار إصلاح مشكلة isOpen()!")
    
    # عرض ملخص الإصلاحات
    show_fix_summary()
    
    print("\n" + "=" * 60)
    print("🚀 بدء تشغيل الاختبار...")
    print("=" * 60)
    
    # تشغيل الاختبار
    exit_code = test_isopen_fix()
    
    print(f"\n🔚 انتهى الاختبار بكود الخروج: {exit_code}")
    sys.exit(exit_code)
