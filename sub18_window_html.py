"""
نافذة إدارة الأخبار والنشاطات - Python + HTML
تحويل من sub18_window.py إلى منهجية حديثة باستخدام HTML/CSS/JavaScript مع PyQt5

الميزات:
- واجهة HTML جميلة ومتجاوبة
- إدارة الأخبار والنشاطات
- طباعة النشاطات المحددة
- تكامل كامل مع قاعدة البيانات
- تصميم عصري ومرن
"""

import sys
import os
import json
from datetime import datetime
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal, Qt, QLoggingCategory
from PyQt5.QtGui import QIcon
import sqlite3

# قمع رسائل QWebChannel التحذيرية
QLoggingCategory.setFilterRules("qt.webengine.debug=false")
QLoggingCategory.setFilterRules("qt.webchannel.debug=false")

class NewsManagementEngine(QObject):
    """محرك إدارة الأخبار والنشاطات"""

    # إشارات للتواصل مع واجهة HTML
    logUpdated = pyqtSignal(str, str, str)  # message, status, timestamp
    dataUpdated = pyqtSignal(str)  # data JSON

    def __init__(self):
        super().__init__()
        self.db_path = os.path.join(os.path.dirname(__file__), "data.db")

    def emit_log(self, message, status="info"):
        """إرسال رسالة إلى واجهة HTML"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.logUpdated.emit(message, status, timestamp)

    def test_database_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            print(f"🔍 اختبار الاتصال بقاعدة البيانات: {self.db_path}")
            
            # التحقق من وجود ملف قاعدة البيانات
            if not os.path.exists(self.db_path):
                print(f"⚠️ ملف قاعدة البيانات غير موجود: {self.db_path}")
                return False
            
            # محاولة الاتصال
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # عرض جميع الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"📋 الجداول الموجودة: {[table[0] for table in tables]}")
            
            # التحقق من جدول الأخبار
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='اخبار_بنشاط'")
            news_table = cursor.fetchone()
            
            if news_table:
                # عرض هيكل الجدول
                cursor.execute("PRAGMA table_info(اخبار_بنشاط)")
                columns = cursor.fetchall()
                print(f"📊 أعمدة جدول الأخبار: {[(col[1], col[2]) for col in columns]}")
                
                # عدد السجلات
                cursor.execute("SELECT COUNT(*) FROM اخبار_بنشاط")
                count = cursor.fetchone()[0]
                print(f"📈 عدد السجلات: {count}")
            else:
                print("⚠️ جدول الأخبار غير موجود")
            
            conn.close()
            print("✅ اختبار قاعدة البيانات مكتمل")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
            return False

    @pyqtSlot(result=str)
    def getAllNews(self):
        """استرجاع جميع الأخبار من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التأكد من وجود الجدول
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS اخبار_بنشاط (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    العنوان TEXT NOT NULL,
                    المهمة TEXT
                )
            """)
            
            # التحقق من وجود البيانات أولاً
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='اخبار_بنشاط'")
            table_exists = cursor.fetchone()
            
            if not table_exists:
                # إنشاء الجدول إذا لم يكن موجوداً
                cursor.execute("""
                    CREATE TABLE اخبار_بنشاط (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        العنوان TEXT NOT NULL,
                        المهمة TEXT
                    )
                """)
                conn.commit()
            
            # محاولة استرجاع البيانات مع معالجة أخطاء الأعمدة
            try:
                cursor.execute("SELECT id, العنوان, المهمة FROM اخبار_بنشاط ORDER BY id DESC")
            except sqlite3.OperationalError as col_error:
                # إذا كان العمود القديم "النشاط" موجود، تحديث اسم العمود
                if "no such column: المهمة" in str(col_error):
                    try:
                        # التحقق من وجود العمود القديم
                        cursor.execute("SELECT id, العنوان, النشاط FROM اخبار_بنشاط LIMIT 1")
                        # إذا نجح، فالعمود القديم موجود - قم بالتحديث
                        cursor.execute("ALTER TABLE اخبار_بنشاط RENAME COLUMN النشاط TO المهمة")
                        conn.commit()
                        cursor.execute("SELECT id, العنوان, المهمة FROM اخبار_بنشاط ORDER BY id DESC")
                    except sqlite3.OperationalError:
                        # إنشاء جدول جديد بالأعمدة الصحيحة
                        cursor.execute("DROP TABLE IF EXISTS اخبار_بنشاط")
                        cursor.execute("""
                            CREATE TABLE اخبار_بنشاط (
                                id INTEGER PRIMARY KEY AUTOINCREMENT,
                                العنوان TEXT NOT NULL,
                                المهمة TEXT
                            )
                        """)
                        conn.commit()
                        cursor.execute("SELECT id, العنوان, المهمة FROM اخبار_بنشاط ORDER BY id DESC")
                else:
                    raise col_error
            
            results = cursor.fetchall()
            conn.close()

            # تحويل النتائج إلى قائمة من القواميس
            news_list = []
            for row in results:
                news_list.append({
                    'id': row[0],
                    'title': row[1] if row[1] else '',
                    'task': row[2] if row[2] else ''
                })

            self.emit_log(f"✅ تم استرجاع {len(news_list)} من الأخبار والمهام", "success")
            return json.dumps(news_list, ensure_ascii=False)

        except Exception as e:
            error_msg = f"❌ خطأ في استرجاع الأخبار: {str(e)}"
            print(f"DATABASE ERROR: {error_msg}")  # طباعة تشخيصية
            self.emit_log(error_msg, "error")
            return json.dumps([], ensure_ascii=False)

    @pyqtSlot(str, str)
    def addNews(self, title, task):
        """إضافة خبر جديد"""
        try:
            if not title.strip():
                self.emit_log("⚠️ الرجاء إدخال العنوان", "warning")
                return

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التأكد من وجود الجدول بالهيكل الصحيح
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS اخبار_بنشاط (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    العنوان TEXT NOT NULL,
                    المهمة TEXT
                )
            """)
            
            cursor.execute("""
                INSERT INTO اخبار_بنشاط (العنوان, المهمة)
                VALUES (?, ?)
            """, (title.strip(), task.strip()))
            
            conn.commit()
            conn.close()

            self.emit_log(f"✅ تمت إضافة الخبر '{title}' بنجاح", "success")
            self.dataUpdated.emit("news_added")

        except sqlite3.Error as db_error:
            error_msg = f"❌ خطأ في قاعدة البيانات عند إضافة الخبر: {str(db_error)}"
            print(f"DATABASE ERROR: {error_msg}")
            self.emit_log(error_msg, "error")
        except Exception as e:
            error_msg = f"❌ خطأ غير متوقع في إضافة الخبر: {str(e)}"
            print(f"GENERAL ERROR: {error_msg}")
            self.emit_log(error_msg, "error")

    @pyqtSlot(int, str, str)
    def updateNews(self, news_id, title, task):
        """تحديث خبر موجود"""
        try:
            if not title.strip():
                self.emit_log("⚠️ الرجاء إدخال العنوان", "warning")
                return

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود السجل قبل التحديث
            cursor.execute("SELECT id FROM اخبار_بنشاط WHERE id = ?", (news_id,))
            if not cursor.fetchone():
                self.emit_log("⚠️ السجل المطلوب غير موجود", "warning")
                conn.close()
                return
            
            cursor.execute("""
                UPDATE اخبار_بنشاط
                SET العنوان = ?, المهمة = ?
                WHERE id = ?
            """, (title.strip(), task.strip(), news_id))
            
            conn.commit()
            conn.close()

            self.emit_log(f"✅ تم تحديث الخبر '{title}' بنجاح", "success")
            self.dataUpdated.emit("news_updated")

        except sqlite3.Error as db_error:
            error_msg = f"❌ خطأ في قاعدة البيانات عند تحديث الخبر: {str(db_error)}"
            print(f"DATABASE ERROR: {error_msg}")
            self.emit_log(error_msg, "error")
        except Exception as e:
            error_msg = f"❌ خطأ غير متوقع في تحديث الخبر: {str(e)}"
            print(f"GENERAL ERROR: {error_msg}")
            self.emit_log(error_msg, "error")

    @pyqtSlot(int, str)
    def deleteNews(self, news_id, title):
        """حذف خبر"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود السجل قبل الحذف
            cursor.execute("SELECT id FROM اخبار_بنشاط WHERE id = ?", (news_id,))
            if not cursor.fetchone():
                self.emit_log("⚠️ السجل المطلوب غير موجود", "warning")
                conn.close()
                return
            
            cursor.execute("DELETE FROM اخبار_بنشاط WHERE id = ?", (news_id,))
            
            affected_rows = cursor.rowcount
            conn.commit()
            conn.close()

            if affected_rows > 0:
                self.emit_log(f"✅ تم حذف الخبر '{title}' بنجاح", "success")
                self.dataUpdated.emit("news_deleted")
            else:
                self.emit_log("⚠️ لم يتم العثور على السجل للحذف", "warning")

        except sqlite3.Error as db_error:
            error_msg = f"❌ خطأ في قاعدة البيانات عند حذف الخبر: {str(db_error)}"
            print(f"DATABASE ERROR: {error_msg}")
            self.emit_log(error_msg, "error")
        except Exception as e:
            error_msg = f"❌ خطأ غير متوقع في حذف الخبر: {str(e)}"
            print(f"GENERAL ERROR: {error_msg}")
            self.emit_log(error_msg, "error")

    @pyqtSlot(str, str)
    def printActivity(self, title, task):
        """طباعة المهمة المحددة"""
        try:
            # محاولة استخدام وحدة الطباعة المخصصة أولاً
            try:
                import print18_window
                print("استخدام وحدة الطباعة المخصصة للمهام")

                # استدعاء دالة الطباعة المخصصة
                current_date = datetime.now().strftime("%Y/%m/%d")
                success = print18_window.print_single_activity(
                    title=title,
                    task=task,
                    date_str=current_date
                )

                if success:
                    self.emit_log(f"🖨️ تمت طباعة المهمة '{title}' بنجاح", "success")
                    return
                else:
                    print("فشلت الطباعة المخصصة، محاولة استخدام الطرق البديلة")
            except ImportError:
                print("وحدة الطباعة المخصصة غير متوفرة، استخدام الطريقة البديلة")
            except Exception as e:
                print(f"خطأ في استخدام وحدة الطباعة المخصصة: {e}")

            # استخدام الطريقة البديلة كاحتياط
            try:
                from print_test import print_activity_list
                activities = [{
                    'title': title,
                    'task': task
                }]
                current_date = datetime.now().strftime("%Y/%m/%d")
                
                if print_activity_list(activities, current_date):
                    self.emit_log(f"🖨️ تمت طباعة المهمة '{title}' بنجاح", "success")
                else:
                    self.emit_log("⚠️ حدث خطأ أثناء طباعة المهمة", "warning")
            except ImportError:
                self.emit_log("⚠️ وحدة الطباعة غير متوفرة", "warning")

        except Exception as e:
            self.emit_log(f"❌ خطأ في طباعة المهمة: {str(e)}", "error")


class NewsWindow(QMainWindow):
    """نافذة إدارة الأخبار والنشاطات"""
    
    def __init__(self, parent=None, db=None):
        super().__init__(parent)
        self.setWindowTitle("📰 إدارة الأخبار والمهام")
        
        # إعداد النافذة لتفتح في كامل الشاشة مع زر الإغلاق فقط
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint | Qt.WindowTitleHint)
        
        # فتح النافذة في كامل الشاشة
        self.showMaximized()
        
        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # إنشاء محرك إدارة الأخبار
        self.news_engine = NewsManagementEngine()

        # اختبار قاعدة البيانات عند بدء النافذة
        print("🔧 تشخيص قاعدة البيانات...")
        self.news_engine.test_database_connection()

        # إعداد الواجهة
        self.setup_ui()
        self.setup_web_channel()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء عرض الويب
        self.web_view = QWebEngineView()
        layout.addWidget(self.web_view)

        # تحميل واجهة HTML
        html_content = self.get_complete_html()
        self.web_view.setHtml(html_content)

    def setup_web_channel(self):
        """إعداد قناة التواصل"""
        self.channel = QWebChannel()
        self.channel.registerObject("newsEngine", self.news_engine)
        self.web_view.page().setWebChannel(self.channel)
        self.web_view.loadFinished.connect(self.on_page_loaded)

    def on_page_loaded(self):
        """استدعاء عند انتهاء تحميل الصفحة"""
        self.channel.registerObject("newsEngine", self.news_engine)

    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        event.accept()

    def get_complete_html(self):
        """HTML كامل مع CSS و JavaScript"""
        return """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8">
    <title>إدارة الأخبار والنشاطات</title>
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Calibri', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        /* العنوان الرئيسي */
        .header {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            text-align: center;
        }

        .header h1 {
            font-family: 'Calibri', sans-serif;
            font-size: 28pt;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header p {
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            color: #666;
        }

        /* منطقة الإدخال */
        .input-section {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-family: 'Calibri', sans-serif;
            font-size: 13pt;
            background: white;
            color: #333;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        textarea.form-control {
            min-height: 100px;
            resize: vertical;
        }

        /* الأزرار */
        .buttons-row {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 20px;
        }

        .btn {
            font-family: 'Calibri', sans-serif;
            font-size: 13pt;
            font-weight: bold;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
            min-width: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-add {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .btn-add:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.5);
        }

        .btn-update {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }

        .btn-update:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.5);
        }

        .btn-delete {
            background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
        }

        .btn-delete:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.5);
        }

        .btn-print {
            background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
        }

        .btn-print:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(111, 66, 193, 0.5);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        /* منطقة الجدول */
        .table-section {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            margin-bottom: 20px;
        }

        .table-container {
            overflow: auto;
            max-height: 450px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 2px solid #dee2e6;
            scroll-behavior: smooth;
        }

        .table-container::-webkit-scrollbar {
            width: 14px;
            height: 14px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            border: 2px solid #f8f9fa;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }

        .table-container::-webkit-scrollbar-corner {
            background: #f8f9fa;
        }

        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            font-family: 'Calibri', sans-serif;
            background: white;
            border-radius: 10px;
            overflow: hidden;
        }

        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 10px;
            text-align: center;
            font-weight: bold;
            border: none;
            font-size: 14pt;
            position: sticky;
            top: 0;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        th:first-child {
            border-top-right-radius: 10px;
        }

        th:last-child {
            border-top-left-radius: 10px;
        }

        td {
            padding: 15px 10px;
            border-bottom: 1px solid #e9ecef;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Calibri', sans-serif;
            font-size: 17pt;
            font-weight: bold;
            color: #000000;
            background: white;
        }

        td:first-child {
            border-right: 1px solid #e9ecef;
        }

        td:last-child {
            border-left: 1px solid #e9ecef;
        }

        tr:nth-child(even) td {
            background: #f8f9fa;
        }

        tr:hover td {
            background: #e3f2fd !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        tr.selected td {
            background: #bbdefb !important;
            border-color: #2196f3;
            box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.3);
        }

        /* رسائل الحالة */
        .message-box {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            font-family: 'Calibri', sans-serif;
            font-size: 13pt;
            font-weight: bold;
            color: white;
            display: none;
            z-index: 1000;
            box-shadow: 0 5px 20px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            max-width: 400px;
        }

        .message-box.success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        }

        .message-box.error {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        .message-box.warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }

        .message-box.info {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        }

        .message-box.show {
            display: block;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* حالة فارغة */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #999;
            font-family: 'Calibri', sans-serif;
            font-size: 16pt;
        }

        .empty-state .icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        /* مؤشر التحميل */
        .loading {
            text-align: center;
            padding: 20px;
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            color: #666;
        }

        /* نافذة التأكيد */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            backdrop-filter: blur(5px);
        }

        .modal-overlay.show {
            display: flex;
            animation: fadeIn 0.3s ease-out;
        }

        .confirmation-modal {
            background: white;
            border-radius: 20px;
            padding: 30px;
            max-width: 450px;
            width: 90%;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            text-align: center;
            position: relative;
            transform: scale(0.7);
            animation: modalSlideIn 0.3s ease-out forwards;
        }

        .modal-icon {
            font-size: 64px;
            margin-bottom: 20px;
            color: #e74c3c;
        }

        .modal-title {
            font-family: 'Calibri', sans-serif;
            font-size: 18pt;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .modal-message {
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            color: #666;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .modal-btn {
            font-family: 'Calibri', sans-serif;
            font-size: 13pt;
            font-weight: bold;
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 100px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .modal-btn-confirm {
            background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
            color: white;
        }

        .modal-btn-confirm:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(220, 53, 69, 0.5);
        }

        .modal-btn-cancel {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
        }

        .modal-btn-cancel:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.5);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes modalSlideIn {
            from {
                transform: scale(0.7) translateY(-50px);
                opacity: 0;
            }
            to {
                transform: scale(1) translateY(0);
                opacity: 1;
            }
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .buttons-row {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
                margin-bottom: 10px;
            }
            
            th, td {
                padding: 8px 5px;
                font-size: 12pt;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- منطقة الإدخال -->
        <div class="input-section">
            <div class="form-group">
                <label for="titleInput">📝 العنوان:</label>
                <input type="text" id="titleInput" class="form-control" placeholder="أدخل عنوان الخبر أو المهمة...">
            </div>

            <div class="form-group">
                <label for="taskInput">📄 المهمة:</label>
                <textarea id="taskInput" class="form-control" placeholder="أدخل تفاصيل المهمة..."></textarea>
            </div>

            <div class="buttons-row">
                <button class="btn btn-add" onclick="addNews()">
                    ➕ إضافة
                </button>
                <button class="btn btn-update" onclick="updateNews()" id="updateBtn" disabled>
                    ✏️ تحديث
                </button>
                <button class="btn btn-delete" onclick="confirmDelete()" id="deleteBtn" disabled>
                    🗑️ حذف
                </button>
                <button class="btn btn-print" onclick="printActivity()" id="printBtn" disabled>
                    🖨️ طباعة المهمة
                </button>
            </div>
        </div>

        <!-- منطقة الجدول -->
        <div class="table-section">
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th style="width: 10%;">الرقم</th>
                            <th style="width: 30%;">العنوان</th>
                            <th style="width: 60%;">المهمة</th>
                        </tr>
                    </thead>
                    <tbody id="newsTable">
                        <tr>
                            <td colspan="3" class="loading">🔄 جاري تحميل البيانات...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- رسائل الحالة -->
        <div class="message-box" id="messageBox"></div>

        <!-- نافذة التأكيد -->
        <div class="modal-overlay" id="confirmationModal">
            <div class="confirmation-modal">
                <div class="modal-icon">⚠️</div>
                <div class="modal-title" id="modalTitle">تأكيد الحذف</div>
                <div class="modal-message" id="modalMessage">هل أنت متأكد من حذف هذا الخبر؟</div>
                <div class="modal-buttons">
                    <button class="modal-btn modal-btn-confirm" id="modalConfirm">🗑️ نعم، احذف</button>
                    <button class="modal-btn modal-btn-cancel" id="modalCancel">❌ إلغاء</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let newsEngine = null;
        let isChannelReady = false;
        let currentSelectedRow = null;
        let newsData = [];

        // إعداد قناة التواصل مع Python
        function initializeChannel() {
            if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                new QWebChannel(qt.webChannelTransport, function(channel) {
                    newsEngine = channel.objects.newsEngine;
                    isChannelReady = true;

                    console.log('🚀 QWebChannel initialized successfully');

                    // ربط الإشارات
                    if (newsEngine) {
                        newsEngine.logUpdated.connect(handleLogUpdate);
                        newsEngine.dataUpdated.connect(handleDataUpdate);

                        // تحميل البيانات الأولية
                        loadNewsData();

                        console.log('✅ تم تهيئة نظام إدارة الأخبار بنجاح');
                    }
                });
            } else {
                console.log('⚠️ QWebChannel not available, retrying...');
                setTimeout(initializeChannel, 100);
            }
        }

        // تحميل بيانات الأخبار
        function loadNewsData() {
            if (newsEngine) {
                newsEngine.getAllNews(function(result) {
                    try {
                        if (typeof result === 'string') {
                            newsData = JSON.parse(result);
                        } else {
                            newsData = result;
                        }
                        
                        displayNewsData(newsData);
                        
                    } catch (error) {
                        console.error('خطأ في تحليل بيانات الأخبار:', error);
                        showMessage('❌ خطأ في تحميل البيانات', 'error');
                    }
                });
            }
        }

        // عرض بيانات الأخبار في الجدول
        function displayNewsData(data) {
            const tableBody = document.getElementById('newsTable');
            
            if (!data || data.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="3" class="empty-state">
                            <div class="icon">📰</div>
                            <div>لا توجد أخبار أو مهام مسجلة</div>
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            data.forEach((news, index) => {
                html += `
                    <tr onclick="selectRow(${index})" data-id="${news.id}">
                        <td>${news.id}</td>
                        <td>${news.title}</td>
                        <td>${news.task}</td>
                    </tr>
                `;
            });
            
            tableBody.innerHTML = html;
        }

        // تحديد صف في الجدول
        function selectRow(index) {
            // إزالة التحديد السابق
            document.querySelectorAll('#newsTable tr').forEach(row => {
                row.classList.remove('selected');
            });

            // تحديد الصف الجديد
            const rows = document.querySelectorAll('#newsTable tr');
            if (rows[index]) {
                rows[index].classList.add('selected');
                currentSelectedRow = index;

                // ملء الحقول بالبيانات
                const news = newsData[index];
                document.getElementById('titleInput').value = news.title;
                document.getElementById('taskInput').value = news.task;

                // تفعيل الأزرار
                document.getElementById('updateBtn').disabled = false;
                document.getElementById('deleteBtn').disabled = false;
                document.getElementById('printBtn').disabled = false;
            }
        }

        // إضافة خبر جديد
        function addNews() {
            const title = document.getElementById('titleInput').value.trim();
            const task = document.getElementById('taskInput').value.trim();

            if (!title) {
                showMessage('⚠️ الرجاء إدخال العنوان', 'warning');
                return;
            }

            if (newsEngine) {
                newsEngine.addNews(title, task);
            }
        }

        // تحديث خبر
        function updateNews() {
            if (currentSelectedRow === null) {
                showMessage('⚠️ الرجاء تحديد خبر للتحديث', 'warning');
                return;
            }

            const title = document.getElementById('titleInput').value.trim();
            const task = document.getElementById('taskInput').value.trim();

            if (!title) {
                showMessage('⚠️ الرجاء إدخال العنوان', 'warning');
                return;
            }

            const newsId = newsData[currentSelectedRow].id;
            if (newsEngine) {
                newsEngine.updateNews(newsId, title, task);
            }
        }

        // تأكيد الحذف
        function confirmDelete() {
            if (currentSelectedRow === null) {
                showMessage('⚠️ الرجاء تحديد خبر للحذف', 'warning');
                return;
            }

            const news = newsData[currentSelectedRow];
            showConfirmationModal(
                'تأكيد الحذف',
                `هل أنت متأكد من حذف الخبر: "${news.title}"؟`,
                function() {
                    deleteNews();
                }
            );
        }

        // حذف خبر
        function deleteNews() {
            if (currentSelectedRow === null) return;

            const news = newsData[currentSelectedRow];
            if (newsEngine) {
                newsEngine.deleteNews(news.id, news.title);
            }
        }

        // طباعة المهمة
        function printActivity() {
            if (currentSelectedRow === null) {
                showMessage('⚠️ الرجاء تحديد مهمة للطباعة', 'warning');
                return;
            }

            const news = newsData[currentSelectedRow];
            if (newsEngine) {
                newsEngine.printActivity(news.title, news.task);
            }
        }

        // مسح الحقول
        function clearForm() {
            document.getElementById('titleInput').value = '';
            document.getElementById('taskInput').value = '';
            currentSelectedRow = null;

            // إزالة التحديد من الجدول
            document.querySelectorAll('#newsTable tr').forEach(row => {
                row.classList.remove('selected');
            });

            // تعطيل الأزرار
            document.getElementById('updateBtn').disabled = true;
            document.getElementById('deleteBtn').disabled = true;
            document.getElementById('printBtn').disabled = true;
        }

        // عرض نافذة التأكيد
        function showConfirmationModal(title, message, onConfirm) {
            const modal = document.getElementById('confirmationModal');
            const modalTitle = document.getElementById('modalTitle');
            const modalMessage = document.getElementById('modalMessage');
            const confirmBtn = document.getElementById('modalConfirm');
            const cancelBtn = document.getElementById('modalCancel');

            modalTitle.textContent = title;
            modalMessage.textContent = message;
            
            modal.classList.add('show');

            // معالج زر التأكيد
            confirmBtn.onclick = function() {
                hideConfirmationModal();
                if (onConfirm) onConfirm();
            };

            // معالج زر الإلغاء
            cancelBtn.onclick = function() {
                hideConfirmationModal();
            };

            // إغلاق النافذة عند النقر خارجها
            modal.onclick = function(e) {
                if (e.target === modal) {
                    hideConfirmationModal();
                }
            };
        }

        // إخفاء نافذة التأكيد
        function hideConfirmationModal() {
            const modal = document.getElementById('confirmationModal');
            modal.classList.remove('show');
        }

        // معالجة تحديث السجل
        function handleLogUpdate(message, status, timestamp) {
            showMessage(message, status);
        }

        // معالجة تحديث البيانات
        function handleDataUpdate(action) {
            if (action === 'news_added' || action === 'news_updated' || action === 'news_deleted') {
                loadNewsData();
                clearForm();
            }
        }

        // عرض الرسائل
        function showMessage(message, type) {
            const messageBox = document.getElementById('messageBox');
            messageBox.textContent = message;
            messageBox.className = `message-box ${type} show`;
            
            setTimeout(() => {
                messageBox.classList.remove('show');
            }, 4000);
        }

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeChannel();
        });

        // إغلاق النافذة المنبثقة بالضغط على Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const modal = document.getElementById('confirmationModal');
                if (modal.classList.contains('show')) {
                    hideConfirmationModal();
                }
            }
        });

        // مسح الحقول عند الضغط على Ctrl+N
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                clearForm();
            }
        });
    </script>
</body>
</html>"""


# كود التشغيل للاختبار
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة
    window = NewsWindow()
    window.show()
    
    sys.exit(app.exec_())
