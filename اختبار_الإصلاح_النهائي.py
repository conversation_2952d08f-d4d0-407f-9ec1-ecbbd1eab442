#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الإصلاح النهائي للقائمة المنسدلة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon

def test_final_fix():
    """اختبار الإصلاح النهائي"""
    print("🔧 اختبار الإصلاح النهائي للقائمة المنسدلة")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    
    try:
        from main_window import MainWindow
        print("✅ تم استيراد MainWindow")
        
        main_window = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # انتظار قليل للتأكد من اكتمال التهيئة
        def check_dropdown():
            print("\n🔍 فحص القائمة المنسدلة بعد التهيئة:")
            if hasattr(main_window, 'dropdown_widget'):
                dropdown = main_window.dropdown_widget
                print(f"✅ القائمة موجودة")
                print(f"   الحجم: {dropdown.size()}")
                print(f"   الارتفاع: {dropdown.height()}")
                print(f"   مرئية: {dropdown.isVisible()}")
                
                parent = dropdown.parent()
                if parent:
                    print(f"   الوالد: {parent.objectName()}")
                else:
                    print("   ❌ لا يوجد والد")
                
                # محاولة إظهار القائمة
                print("\n🎯 محاولة إظهار القائمة...")
                if hasattr(main_window, '_show_dropdown'):
                    main_window._show_dropdown()
                    print(f"   بعد الإظهار - الارتفاع: {dropdown.height()}")
                    print(f"   بعد الإظهار - مرئية: {dropdown.isVisible()}")
                    
                    # إخفاء بعد 3 ثوان
                    QTimer.singleShot(3000, lambda: main_window._hide_dropdown())
                    
                else:
                    print("❌ دالة _show_dropdown غير موجودة")
            else:
                print("❌ القائمة المنسدلة غير موجودة")
        
        # فحص القائمة بعد ثانية واحدة
        QTimer.singleShot(1000, check_dropdown)
        
        main_window.show()
        main_window.setWindowTitle("اختبار الإصلاح النهائي - القائمة المنسدلة")
        
        print("\n📋 تعليمات الاختبار:")
        print("1. انتظر رسائل الفحص التلقائي")
        print("2. ابحث عن القائمة المنسدلة في أعلى النافذة")
        print("3. انقر على تبويب 'بيانات المؤسسة'")
        print("4. راقب ظهور/اختفاء القائمة")
        print("5. جرب النقر على الأزرار في القائمة")
        
        print("\n🎯 التحسينات المطبقة:")
        print("   ✅ إصلاح مشكلة navbar_frame")
        print("   ✅ إضافة القائمة للتخطيط الرئيسي مباشرة")
        print("   ✅ تحسين دوال الإظهار/الإخفاء")
        print("   ✅ إضافة اختبار تلقائي")
        print("   ✅ رسائل تشخيصية مفصلة")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = test_final_fix()
    print(f"\nانتهى الاختبار: {exit_code}")
    sys.exit(exit_code)
