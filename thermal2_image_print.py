"""
وحدة للطباعة الحرارية باستخدام تقنية تحويل النص إلى صورة
تستخدم مكتبة PIL لإنشاء صورة من النص ثم طباعتها مباشرة على الطابعة الحرارية
مما يحل مشكلات الطباعة باستخدام الملف النصي
"""

import os
import sys
import datetime
import sqlite3
import traceback
from PyQt5.QtPrintSupport import QPrinterInfo
import win32print, win32ui
from PIL import Image, ImageDraw, ImageFont, ImageWin, ImageEnhance, ImageFilter
import arabic_reshaper
from bidi.algorithm import get_display

def get_thermal_printer_name():
    """الحصول على اسم الطابعة الحرارية من قاعدة البيانات فقط"""
    try:
        # تحديد مسار قاعدة البيانات
        script_dir = os.path.dirname(os.path.abspath(__file__))
        db_path = os.path.join(script_dir, "data.db")

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT الطابعة_الحرارية FROM إعدادات_الطابعة LIMIT 1")
        result = cursor.fetchone()
        conn.close()

        if result and result[0]:
            # التحقق من وجود الطابعة في النظام
            available_printers = [printer[2] for printer in win32print.EnumPrinters(2)]
            if result[0] in available_printers:
                return result[0]
            else:
                print(f"تحذير: الطابعة الحرارية المحددة '{result[0]}' غير متوفرة في النظام")
                print(f"الطابعات المتوفرة: {', '.join(available_printers)}")
        else:
            print("لم يتم تحديد طابعة حرارية في قاعدة البيانات")
    except Exception as e:
        print(f"خطأ في الحصول على اسم الطابعة الحرارية: {e}")

    # لا نستخدم الطابعة الافتراضية، بل نرجع None ليتم إخبار المستخدم بضرورة تحديد الطابعة
    return None

def get_thermal_printer_settings():
    """الحصول على إعدادات الطابعة الحرارية من قاعدة البيانات"""
    settings = {
        'paper_width': 540,    # عرض الورق بالنقاط
        'paper_height': 800,   # ارتفاع الورق بالنقاط
        'scale': 8,            # مضاعف الدقة (للحصول على جودة عالية)
        'font_path': r"C:\Windows\Fonts\calibri.ttf",  # مسار الخط (Calibri العادي) لجميع النصوص
        'title_font_path': r"C:\Windows\Fonts\calibri.ttf",  # مسار الخط (Calibri العادي) للعنوان
        'font_size': 36,       # حجم الخط الأساسي (12 نقطة)
        'row_height': 48,      # ارتفاع الصف
        'margin_left': 0,      # الهامش الأيسر (0)
        'margin_right': 0,     # الهامش الأيمن (0)
        'margin_bottom': 15,   # الهامش السفلي (0.5 سم تقريباً = 15 نقطة)
        'margin_top': 12,      # الهامش العلوي
    }

    try:
        # تحديد مسار قاعدة البيانات
        script_dir = os.path.dirname(os.path.abspath(__file__))
        db_path = os.path.join(script_dir, "data.db")

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # محاولة استخراج إعدادات الطابعة الحرارية
        try:
            cursor.execute("""
                SELECT عرض_الورق, عدد_الأحرف_في_السطر, اسم_الخط, حجم_الخط, خط_عريض
                FROM إعدادات_الطابعة LIMIT 1
            """)
            result = cursor.fetchone()

            if result:
                # تعديل الإعدادات حسب قاعدة البيانات
                if result[2]:  # اسم الخط
                    font_name = result[2]
                    # تحديد مسار الخط بناءً على اسمه
                    if font_name.lower() == "calibri":
                        if result[4]:  # خط عريض
                            settings['font_path'] = r"C:\Windows\Fonts\calibrib.ttf"
                        else:
                            settings['font_path'] = r"C:\Windows\Fonts\calibri.ttf"
                    elif font_name.lower() == "arial":
                        if result[4]:  # خط عريض
                            settings['font_path'] = r"C:\Windows\Fonts\arialbd.ttf"
                        else:
                            settings['font_path'] = r"C:\Windows\Fonts\arial.ttf"
                    elif font_name.lower() == "amiri":
                        if result[4]:  # خط عريض
                            settings['font_path'] = os.path.join(script_dir, "Amiri-Bold.ttf")
                        else:
                            settings['font_path'] = os.path.join(script_dir, "Amiri-Regular.ttf")

                if result[3]:  # حجم الخط
                    settings['font_size'] = int(result[3]) * 2.5  # تحويل حجم الخط إلى مقياس مناسب للصورة

        except Exception as e:
            print(f"خطأ في استخراج إعدادات الطابعة الحرارية المتقدمة: {e}")

        conn.close()
    except Exception as e:
        print(f"خطأ في الحصول على إعدادات الطابعة الحرارية: {e}")

    return settings

def get_institution_info():
    """الحصول على معلومات المؤسسة من قاعدة البيانات"""
    info = {
        'institution_name': "المؤسسة التعليمية",
        'school_year': "2024/2025",
    }

    try:
        # تحديد مسار قاعدة البيانات
        script_dir = os.path.dirname(os.path.abspath(__file__))
        db_path = os.path.join(script_dir, "data.db")

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # استخراج اسم المؤسسة والسنة الدراسية
        cursor.execute("SELECT المؤسسة, السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
        result = cursor.fetchone()
        if result:
            info['institution_name'] = result[0] or info['institution_name']
            info['school_year'] = result[1] or info['school_year']

        conn.close()
    except Exception as e:
        print(f"خطأ في استخراج معلومات المؤسسة: {e}")

    return info

def get_form_info(form_id):
    """الحصول على معلومات النموذج من قاعدة البيانات"""
    # تعيين معرفات النماذج بما يتوافق مع جدول تعديل_المسميات
    form_titles = {
        1: "ورقة الدخول",
        2: "ورقة التأخر",
        3: "ورقة التوجيه",
        4: "ورقة الاستئذان",
        6: "الرمز السري"  # تغيير من 5 إلى 6 لتجنب التعارض مع نموذج زيارة الطبيب
    }

    # تعيين معرفات جدول تعديل_المسميات المقابلة
    form_db_ids = {
        1: 1,  # ورقة الدخول
        2: 2,  # ورقة التأخر
        3: 3,  # ورقة التوجيه
        4: 4,  # ورقة الاستئذان
        6: 6   # الرمز السري (تجنب استخدام 5 لأنه مخصص لزيارة الطبيب)
    }

    form_info = {
        'title': form_titles.get(form_id, "نموذج"),
        'description': "",
    }

    try:
        # تحديد مسار قاعدة البيانات
        script_dir = os.path.dirname(os.path.abspath(__file__))
        db_path = os.path.join(script_dir, "data.db")

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # استخراج عنوان النموذج وملاحظاته
        # استخدام معرف النموذج المقابل في جدول تعديل_المسميات
        db_id = form_db_ids.get(form_id, form_id)
        cursor.execute("""
            SELECT العنوان, ملاحظات
            FROM تعديل_المسميات
            WHERE id = ?
        """, (db_id,))
        result = cursor.fetchone()
        if result:
            form_info['title'] = result[0] or form_info['title']
            form_info['description'] = result[1] or form_info['description']

        conn.close()
    except Exception as e:
        print(f"خطأ في استخراج معلومات النموذج: {e}")

    return form_info

def print_thermal_image(students, section, form_id=1, date_str=None, time_str=None):
    """
    طباعة نموذج على الطابعة الحرارية باستخدام تقنية تحويل النص إلى صورة
    form_id: 1 لورقة الدخول، 2 لورقة التأخر، 3 لورقة التوجيه، 4 لورقة الاستئذان، 5 للرمز السري
    """
    # تحديد التاريخ والوقت إذا لم يتم تمريرهما
    if not date_str:
        date_str = datetime.datetime.now().strftime("%Y/%m/%d")
    if not time_str:
        time_str = datetime.datetime.now().strftime("%H:%M")

    # الحصول على معلومات المؤسسة والنموذج
    institution_info = get_institution_info()
    form_info = get_form_info(form_id)

    # الحصول على إعدادات الطابعة
    settings = get_thermal_printer_settings()

    try:
        # 1) إعداد الـ supersampling والدقة الحقيقية
        width, height = settings['paper_width'], settings['paper_height']
        SCALE = settings['scale']
        hr_w, hr_h = width * SCALE, height * SCALE

        # نرسم على صورة رمادية عالية الدقة
        hr_img = Image.new("L", (hr_w, hr_h), 255)  # خلفية بيضاء
        draw = ImageDraw.Draw(hr_img)

        # تحميل الخط
        hr_font = ImageFont.truetype(settings['font_path'], settings['font_size'] * SCALE)
        # تحميل خط أكبر للعنوان الرئيسي (استخدام Calibri العادي بدلاً من Bold)
        hr_title_font = ImageFont.truetype(settings['title_font_path'], int(settings['font_size'] * 1.2) * SCALE)

        hr_margin_left = settings['margin_left'] * SCALE
        hr_margin_right = settings['margin_right'] * SCALE
        hr_margin_top = settings['margin_top'] * SCALE
        hr_margin_bottom = settings['margin_bottom'] * SCALE
        hr_row_h = settings['row_height'] * SCALE
        border_w = 3 * SCALE

        y = hr_margin_top

        # 2) رسم الرأس
        header = [
            institution_info['institution_name'],
            f"السنة الدراسية: {institution_info['school_year']}",
            form_info['title'],
        ]

        # إضافة وصف النموذج إذا كان موجودًا وتقسيمه إلى سطرين
        if form_info['description']:
            # تقسيم النص إلى سطرين تقريبًا بنفس الطول
            description = form_info['description']
            if len(description) > 20:  # إذا كان النص طويلًا بما يكفي للتقسيم
                # تقسيم النص إلى كلمات
                words = description.split()
                # حساب عدد الكلمات التي ستكون في السطر الأول (تقريبًا نصف الكلمات)
                half_words = len(words) // 2
                # إنشاء السطرين
                first_line = ' '.join(words[:half_words])
                second_line = ' '.join(words[half_words:])
                # إضافة السطرين إلى الرأس
                header.append(first_line)
                header.append(second_line)
            else:
                # إذا كان النص قصيرًا، أضفه كما هو
                header.append(description)

        # رسم العناوين مع استخدام خط عادي للعنوان الرئيسي
        for i, line in enumerate(header):
            text = get_display(arabic_reshaper.reshape(line))

            # استخدام خط عادي للعنوان الرئيسي (اسم المؤسسة)
            if i == 0:  # العنوان الأول (اسم المؤسسة)
                font_to_use = hr_title_font  # خط Calibri العادي
            else:
                font_to_use = hr_font  # خط Calibri Bold

            bbox = draw.textbbox((0, 0), text, font=font_to_use)
            w, h = bbox[2]-bbox[0], bbox[3]-bbox[1]
            x = (hr_w - w)//2

            # رسم النص
            draw.text((x, y), text, font=font_to_use, fill=0)

            # زيادة المسافة بعد العنوان الرئيسي
            if i == 0:
                y += h + 15 * SCALE
            else:
                y += h + 10 * SCALE

        # 3) رسم الجدول
        x0, x1 = hr_margin_left, hr_w - hr_margin_right
        table_w = x1 - x0
        c1 = int(table_w * 0.2)
        cols = [x0, x0+c1, x1]

        # عناوين الجدول
        table_header = ["ت.ر", "الاسم الكامل"]

        # عدد الصفوف (العنوان + الطلاب)
        rows = 1 + len(students)

        # خطوط أفقية
        for i in range(rows+1):
            yy = y + i*hr_row_h
            draw.line([(x0, yy),(x1, yy)], fill=0, width=border_w)
        # الخط العمودي
        draw.line([(cols[1], y),(cols[1], y+rows*hr_row_h)], fill=0, width=border_w)
        # الحدود الخارجية
        draw.rectangle([(x0, y),(x1, y+rows*hr_row_h)], outline=0, width=border_w)

        # رأس الجدول
        for j, txt in enumerate(table_header):
            t = txt if j==0 else get_display(arabic_reshaper.reshape(txt))
            bx = draw.textbbox((0,0), t, font=hr_font)
            w, h = bx[2]-bx[0], bx[3]-bx[1]
            cx0, cw = cols[j], cols[j+1]-cols[j]
            cx = cx0 + (cw-w)//2
            cy = y + (hr_row_h-h)//2
            draw.text((cx, cy), t, font=hr_font, fill=0)

        # بيانات الطلاب
        for i, student in enumerate(students, 1):
            # استخدام الترتيب التسلسلي بدلاً من الرقم الترتيبي المخزن
            rt = str(i)
            row = [rt, student['name']]

            for j, txt in enumerate(row):
                # معالجة النص العربي فقط للاسم (العمود الثاني)، وليس للرقم الترتيبي
                t = txt if j==0 else get_display(arabic_reshaper.reshape(txt))
                bx = draw.textbbox((0,0), t, font=hr_font)
                w, h = bx[2]-bx[0], bx[3]-bx[1]
                cx0, cw = cols[j], cols[j+1]-cols[j]

                # توسيط الرقم الترتيبي في العمود الأول
                if j==0:
                    cx = cx0 + (cw-w)//2
                # محاذاة الاسم إلى اليمين في العمود الثاني
                else:
                    cx = cx0 + cw - w - 5 * SCALE

                cy = y + (i)*hr_row_h + (hr_row_h-h)//2
                draw.text((cx, cy), t, font=hr_font, fill=0)

        y += rows*hr_row_h + 10* SCALE

        # 4) رسم التذييل
        footer = [
            f"من قســـــم : {section}",
            f"التاريخ: {date_str}  الوقت: {time_str}"
        ]

        # ترك مسافة من الأسفل
        available_height = hr_h - hr_margin_bottom

        for line in footer:
            t = get_display(arabic_reshaper.reshape(line))
            bx = draw.textbbox((0,0), t, font=hr_font)
            w, h = bx[2]-bx[0], bx[3]-bx[1]
            x = (hr_w - w)//2

            # التأكد من أن التذييل لا يتجاوز الهامش السفلي
            if y + h > available_height:
                y = available_height - h

            draw.text((x, y), t, font=hr_font, fill=0)
            y += h + 5* SCALE

        # 5) تصغير الصورة ثم تحويلها ثنائية اللون مع dithering محسن
        # تصغير الصورة بمقدار 3% إضافي لضمان ظهور النص كاملاً داخل الورقة
        scaled_width = int(width * 0.97)  # تصغير العرض بنسبة 3%
        scaled_height = int(height * 0.97)  # تصغير الارتفاع بنسبة 3%

        # إنشاء صورة بيضاء بالحجم الأصلي
        final_img = Image.new("L", (width, height), 255)

        # استخدام LANCZOS للحصول على أفضل جودة عند تصغير الصورة
        small_img = hr_img.resize((scaled_width, scaled_height), resample=Image.LANCZOS)

        # لصق الصورة المصغرة في وسط الصورة النهائية (لتوسيط المحتوى)
        paste_x = (width - scaled_width) // 2
        paste_y = (height - scaled_height) // 2
        final_img.paste(small_img, (paste_x, paste_y))

        # استخدام الصورة النهائية
        img = final_img

        # تحسين الصورة قبل التحويل إلى ثنائية اللون
        # 1. تحسين التباين
        enhancer = ImageEnhance.Contrast(img)
        img = enhancer.enhance(1.3)  # زيادة التباين بنسبة 30%

        # 2. تحسين الحدة
        enhancer = ImageEnhance.Sharpness(img)
        img = enhancer.enhance(1.5)  # زيادة الحدة بنسبة 50%

        # 3. تحسين السطوع
        enhancer = ImageEnhance.Brightness(img)
        img = enhancer.enhance(1.1)  # زيادة السطوع بنسبة 10%

        # تحويل إلى ثنائية اللون مع dithering محسن
        img = img.convert("1", dither=Image.FLOYDSTEINBERG)  # استخدام خوارزمية Floyd-Steinberg للحصول على أفضل نتيجة

        # تحويل إلى RGB للطباعة
        img = img.convert("RGB")

        # 6) إرسال للطابعة
        printer_name = get_thermal_printer_name()
        if not printer_name:
            print("خطأ: لم يتم العثور على طابعة حرارية محددة في قاعدة البيانات")
            print("يرجى تحديد الطابعة الحرارية في إعدادات البرنامج")

            # عرض قائمة بالطابعات المتوفرة في النظام
            try:
                available_printers = [printer[2] for printer in win32print.EnumPrinters(2)]
                if available_printers:
                    print("الطابعات المتوفرة في النظام:")
                    for i, printer in enumerate(available_printers, 1):
                        print(f"  {i}. {printer}")
                    print("يمكنك تحديد إحدى هذه الطابعات في إعدادات البرنامج")
                else:
                    print("لا توجد طابعات متوفرة في النظام")
            except Exception as e:
                print(f"خطأ في الحصول على قائمة الطابعات: {e}")

            return False

        hPrinter = win32print.OpenPrinter(printer_name)
        pdc = win32ui.CreateDC()
        pdc.CreatePrinterDC(printer_name)
        pdc.StartDoc("ThermalPrint")
        pdc.StartPage()
        dib = ImageWin.Dib(img)
        dib.draw(pdc.GetSafeHdc(), (0,0,width,height))
        pdc.EndPage()
        pdc.EndDoc()
        pdc.DeleteDC()
        win32print.ClosePrinter(hPrinter)

        print(f"تمت الطباعة بنجاح على الطابعة: {printer_name}")
        return True

    except Exception as e:
        print(f"خطأ في الطباعة: {e}")
        traceback.print_exc()
        return False

def print_entry_form_direct(students, section, date_str=None, time_str=None):
    """طباعة نموذج الدخول مباشرة على الطابعة الحرارية"""
    return print_thermal_image(students, section, form_id=1, date_str=date_str, time_str=time_str)

def print_late_form_direct(students, section, date_str=None, time_str=None):
    """طباعة نموذج التأخر مباشرة على الطابعة الحرارية"""
    return print_thermal_image(students, section, form_id=2, date_str=date_str, time_str=time_str)

def print_guidance_form_direct(students, section, date_str=None, time_str=None):
    """طباعة نموذج التوجيه مباشرة على الطابعة الحرارية"""
    return print_thermal_image(students, section, form_id=3, date_str=date_str, time_str=time_str)

def print_permission_form_direct(students, section, date_str=None, time_str=None):
    """طباعة نموذج الاستئذان مباشرة على الطابعة الحرارية"""
    return print_thermal_image(students, section, form_id=4, date_str=date_str, time_str=time_str)

def print_secret_code_image(student, date_str=None):
    """
    طباعة الرمز السري لتلميذ واحد على الطابعة الحرارية
    بتنسيق مخصص يشبه النموذج المطلوب
    """
    # تحديد التاريخ إذا لم يتم تمريره
    if not date_str:
        date_str = datetime.datetime.now().strftime("%d-%m-%Y")

    # الحصول على معلومات المؤسسة والنموذج
    institution_info = get_institution_info()
    form_info = get_form_info(6)  # معرف نموذج الرمز السري

    # الحصول على إعدادات الطابعة
    settings = get_thermal_printer_settings()

    try:
        # 1) إعداد الـ supersampling والدقة الحقيقية
        width, height = settings['paper_width'], 600  # ارتفاع أقصر للرمز السري
        SCALE = settings['scale']
        hr_w, hr_h = width * SCALE, height * SCALE

        # نرسم على صورة رمادية عالية الدقة
        hr_img = Image.new("L", (hr_w, hr_h), 255)  # خلفية بيضاء
        draw = ImageDraw.Draw(hr_img)

        # تحميل الخط
        hr_font = ImageFont.truetype(settings['font_path'], settings['font_size'] * SCALE)
        # تحميل خط أكبر للعنوان الرئيسي (استخدام Calibri العادي بدلاً من Bold)
        hr_title_font = ImageFont.truetype(settings['title_font_path'], int(settings['font_size'] * 1.2) * SCALE)

        hr_margin_left = settings['margin_left'] * SCALE
        hr_margin_right = settings['margin_right'] * SCALE
        hr_margin_top = settings['margin_top'] * SCALE
        hr_margin_bottom = settings['margin_bottom'] * SCALE
        border_w = 2 * SCALE

        y = hr_margin_top

        # 2) رسم الرأس
        header = [
            institution_info['institution_name'],
            f"السنة الدراسية: {institution_info['school_year']}",
            form_info['title'],
        ]

        # رسم العناوين مع استخدام خط عادي للعنوان الرئيسي
        for i, line in enumerate(header):
            text = get_display(arabic_reshaper.reshape(line))

            # استخدام خط عادي للعنوان الرئيسي (اسم المؤسسة)
            if i == 0:  # العنوان الأول (اسم المؤسسة)
                font_to_use = hr_title_font  # خط Calibri العادي
            else:
                font_to_use = hr_font  # خط Calibri Bold

            bbox = draw.textbbox((0, 0), text, font=font_to_use)
            w, h = bbox[2]-bbox[0], bbox[3]-bbox[1]
            x = (hr_w - w)//2

            # رسم النص
            draw.text((x, y), text, font=font_to_use, fill=0)

            # زيادة المسافة بعد العنوان الرئيسي
            if i == 0:
                y += h + 10 * SCALE
            else:
                y += h + 5 * SCALE

        # 3) رسم خط فاصل
        y += 5 * SCALE
        draw.line([(hr_margin_left, y), (hr_w - hr_margin_right, y)], fill=0, width=border_w)
        y += 10 * SCALE

        # 4) رسم بيانات التلميذ
        rt = student.get('rt', '')
        code = student.get('code', '')
        name = student.get('name', '')
        email = f"{code}@taalim.ma" if code else ''
        secret = student.get('secret', 'غير متوفر')

        # تنسيق البيانات بشكل مشابه للنموذج المطلوب
        student_info = [
            f"رت: {rt}",
            f"رمز التلميذ: {code}",
            f"الاسم والنسب: {name}",
            f"اسم المستخدم: {email}",
            f"الرمز السري: {secret}",
        ]

        for line in student_info:
            text = get_display(arabic_reshaper.reshape(line))
            bbox = draw.textbbox((0, 0), text, font=hr_font)
            w, h = bbox[2]-bbox[0], bbox[3]-bbox[1]
            x = hr_w - w - hr_margin_right  # محاذاة إلى اليمين
            draw.text((x, y), text, font=hr_font, fill=0)
            y += h + 5 * SCALE

        # 5) رسم خط فاصل آخر
        y += 5 * SCALE
        draw.line([(hr_margin_left, y), (hr_w - hr_margin_right, y)], fill=0, width=border_w)
        y += 10 * SCALE

        # 6) رسم التاريخ
        date_text = f"التاريخ: {date_str}"
        text = get_display(arabic_reshaper.reshape(date_text))
        bbox = draw.textbbox((0, 0), text, font=hr_font)
        w, h = bbox[2]-bbox[0], bbox[3]-bbox[1]

        # التأكد من أن التاريخ لا يتجاوز الهامش السفلي
        available_height = hr_h - hr_margin_bottom
        if y + h > available_height:
            y = available_height - h

        x = hr_w - w - hr_margin_right  # محاذاة إلى اليمين
        draw.text((x, y), text, font=hr_font, fill=0)

        # 7) تصغير الصورة ثم تحويلها ثنائية اللون مع dithering محسن
        # تصغير الصورة بمقدار 3% إضافي لضمان ظهور النص كاملاً داخل الورقة
        scaled_width = int(width * 0.97)  # تصغير العرض بنسبة 3%
        scaled_height = int(height * 0.97)  # تصغير الارتفاع بنسبة 3%

        # إنشاء صورة بيضاء بالحجم الأصلي
        final_img = Image.new("L", (width, height), 255)

        # استخدام LANCZOS للحصول على أفضل جودة عند تصغير الصورة
        small_img = hr_img.resize((scaled_width, scaled_height), resample=Image.LANCZOS)

        # لصق الصورة المصغرة في وسط الصورة النهائية (لتوسيط المحتوى)
        paste_x = (width - scaled_width) // 2
        paste_y = (height - scaled_height) // 2
        final_img.paste(small_img, (paste_x, paste_y))

        # استخدام الصورة النهائية
        img = final_img

        # تحسين الصورة قبل التحويل إلى ثنائية اللون
        # 1. تحسين التباين
        enhancer = ImageEnhance.Contrast(img)
        img = enhancer.enhance(1.3)  # زيادة التباين بنسبة 30%

        # 2. تحسين الحدة
        enhancer = ImageEnhance.Sharpness(img)
        img = enhancer.enhance(1.5)  # زيادة الحدة بنسبة 50%

        # 3. تحسين السطوع
        enhancer = ImageEnhance.Brightness(img)
        img = enhancer.enhance(1.1)  # زيادة السطوع بنسبة 10%

        # تحويل إلى ثنائية اللون مع dithering محسن
        img = img.convert("1", dither=Image.FLOYDSTEINBERG)  # استخدام خوارزمية Floyd-Steinberg للحصول على أفضل نتيجة

        # تحويل إلى RGB للطباعة
        img = img.convert("RGB")

        # 8) إرسال للطابعة
        printer_name = get_thermal_printer_name()
        if not printer_name:
            print("خطأ: لم يتم العثور على طابعة حرارية محددة في قاعدة البيانات")
            print("يرجى تحديد الطابعة الحرارية في إعدادات البرنامج")

            # عرض قائمة بالطابعات المتوفرة في النظام
            try:
                available_printers = [printer[2] for printer in win32print.EnumPrinters(2)]
                if available_printers:
                    print("الطابعات المتوفرة في النظام:")
                    for i, printer in enumerate(available_printers, 1):
                        print(f"  {i}. {printer}")
                    print("يمكنك تحديد إحدى هذه الطابعات في إعدادات البرنامج")
                else:
                    print("لا توجد طابعات متوفرة في النظام")
            except Exception as e:
                print(f"خطأ في الحصول على قائمة الطابعات: {e}")

            return False

        hPrinter = win32print.OpenPrinter(printer_name)
        pdc = win32ui.CreateDC()
        pdc.CreatePrinterDC(printer_name)
        pdc.StartDoc("ThermalPrint")
        pdc.StartPage()
        dib = ImageWin.Dib(img)
        dib.draw(pdc.GetSafeHdc(), (0,0,width,height))
        pdc.EndPage()
        pdc.EndDoc()
        pdc.DeleteDC()
        win32print.ClosePrinter(hPrinter)

        print(f"تمت طباعة الرمز السري للتلميذ {name} بنجاح")
        return True

    except Exception as e:
        print(f"خطأ في طباعة الرمز السري: {e}")
        traceback.print_exc()
        return False

def print_secret_codes_direct(students, date_str=None):
    """طباعة الرموز السرية مباشرة على الطابعة الحرارية"""
    if not date_str:
        date_str = datetime.datetime.now().strftime("%d-%m-%Y")

    success = True
    # طباعة كل تلميذ في ورقة منفصلة باستخدام التنسيق الجديد
    for student in students:
        result = print_secret_code_image(student, date_str)
        if not result:
            success = False
            print(f"فشلت طباعة الرمز السري للتلميذ: {student.get('name', '')}")

    return success

# اختبار الطباعة
if __name__ == "__main__":
    students = [
        {"rt": "1", "name": "محمد أحمد"},
        {"rt": "2", "name": "سعيد علي"}
    ]
    section = "3-2"

    print("\n=== بدء اختبار الطباعة باستخدام تحويل النص إلى صورة لورقة الدخول ===")
    result1 = print_entry_form_direct(students, section)
    print(f"نتيجة طباعة ورقة الدخول: {'نجاح' if result1 else 'فشل'}")

    print("\n=== بدء اختبار الطباعة باستخدام تحويل النص إلى صورة لورقة التأخر ===")
    result2 = print_late_form_direct(students, section)
    print(f"نتيجة طباعة ورقة التأخر: {'نجاح' if result2 else 'فشل'}")

    print("\n=== بدء اختبار الطباعة باستخدام تحويل النص إلى صورة لورقة التوجيه ===")
    result3 = print_guidance_form_direct(students, section)
    print(f"نتيجة طباعة ورقة التوجيه: {'نجاح' if result3 else 'فشل'}")

    print("\n=== بدء اختبار الطباعة باستخدام تحويل النص إلى صورة لورقة الاستئذان ===")
    result4 = print_permission_form_direct(students, section)
    print(f"نتيجة طباعة ورقة الاستئذان: {'نجاح' if result4 else 'فشل'}")

    print("\n=== بدء اختبار الطباعة باستخدام تحويل النص إلى صورة للرموز السرية ===")
    students_with_secrets = [
        {"rt": "1", "code": "P151056065", "name": "بشرى شيماء", "secret": "غير متوفر"},
        {"rt": "2", "code": "P151056066", "name": "سعيد علي", "secret": "654321"}
    ]
    result5 = print_secret_codes_direct(students_with_secrets)
    print(f"نتيجة طباعة الرموز السرية: {'نجاح' if result5 else 'فشل'}")
