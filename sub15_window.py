#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import sqlite3
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QComboBox, QPushButton, QFrame, QTableWidget, QTableWidgetItem,
                            QHeaderView, QTabWidget, QWidget, QMessageBox, QFileDialog)
from PyQt5.QtGui import QFont, QIcon, QColor
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtSql import QSqlQuery

class RegulationsCardWindow(QDialog):
    """نافذة بطاقة اللوائح والإحصائيات للقسم المحدد"""
    def __init__(self, parent=None, db=None, section=None, academic_year=None):
        super().__init__(parent)
        self.db = db
        self.section = section
        self.academic_year = academic_year
        self.institution_info = {}

        # إعداد النافذة
        self.setWindowTitle(f"بطاقة اللوائح - {section}")
        self.setGeometry(100, 100, 800, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        try:
            self.setWindowIcon(QIcon("01.ico"))
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تعيين نمط الخط
        self.font_title = QFont("Calibri", 16)
        self.font_title.setBold(True)
        self.font_subtitle = QFont("Calibri", 14)
        self.font_subtitle.setBold(True)
        self.font_normal = QFont("Calibri", 12)
        self.font_normal.setBold(True)

        # تعيين مؤشر اليد للنافذة الرئيسية
        self.setCursor(Qt.ArrowCursor)  # المؤشر الافتراضي للخلفية

        # إنشاء التخطيط الرئيسي
        self.init_ui()

        # تحميل البيانات
        self.load_data()

    def init_ui(self):
        """إنشاء واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # إطار العنوان
        title_frame = QFrame()
        title_frame.setFrameShape(QFrame.StyledPanel)
        title_frame.setStyleSheet("""
            QFrame {
                background-color: #e6f2ff;
                border: 2px solid #0066cc;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        title_layout = QVBoxLayout(title_frame)

        # عنوان النافذة
        title_label = QLabel(f"بطاقة اللوائح للقسم: {self.section}")
        title_label.setFont(self.font_title)
        title_label.setStyleSheet("color: #0066cc;")
        title_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(title_label)

        # السنة الدراسية
        year_label = QLabel(f"السنة الدراسية: {self.academic_year}")
        year_label.setFont(self.font_subtitle)
        year_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(year_label)

        main_layout.addWidget(title_frame)

        # إطار اختيار القسم
        section_frame = QFrame()
        section_frame.setFrameShape(QFrame.StyledPanel)
        section_frame.setStyleSheet("""
            QFrame {
                background-color: #f0f8ff;
                border: 1px solid #b0c4de;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        section_layout = QHBoxLayout(section_frame)

        # إضافة عنوان
        section_label = QLabel("اختر القسم:")
        section_label.setFont(self.font_normal)
        section_layout.addWidget(section_label)

        # إضافة قائمة منسدلة للأقسام
        self.section_combo = QComboBox()
        self.section_combo.setFont(self.font_normal)
        self.section_combo.setCursor(Qt.PointingHandCursor)
        self.section_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #b0c4de;
                border-radius: 5px;
                padding: 5px;
                min-width: 200px;
            }
            QComboBox::drop-down {
                border: 0px;
            }
            QComboBox::down-arrow {
                image: url(down_arrow.png);
                width: 12px;
                height: 12px;
            }
        """)
        section_layout.addWidget(self.section_combo)

        # زر تحديث
        load_btn = QPushButton("تحميل")
        load_btn.setFont(self.font_normal)
        load_btn.setCursor(Qt.PointingHandCursor)
        load_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border-radius: 5px;
                padding: 5px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        load_btn.clicked.connect(self.on_section_changed)
        section_layout.addWidget(load_btn)

        section_layout.addStretch()
        main_layout.addWidget(section_frame)

        # إطار المعلومات
        info_frame = QFrame()
        info_frame.setFrameShape(QFrame.StyledPanel)
        info_frame.setStyleSheet("""
            QFrame {
                background-color: #f9f9f9;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 15px;
            }
        """)
        info_layout = QVBoxLayout(info_frame)

        # إنشاء علامات التبويب
        self.tab_widget = QTabWidget()
        self.tab_widget.setFont(self.font_normal)

        # علامة تبويب المعلومات العامة
        general_tab = QWidget()
        general_layout = QVBoxLayout(general_tab)

        # جدول المعلومات العامة
        self.general_table = QTableWidget()
        self.general_table.setColumnCount(2)
        self.general_table.setRowCount(7)  # زيادة عدد الصفوف لإضافة معدل السماح
        self.general_table.setHorizontalHeaderLabels(["البيان", "القيمة"])
        self.general_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.general_table.verticalHeader().setVisible(False)
        self.general_table.setFont(self.font_normal)
        self.general_table.setEditTriggers(QTableWidget.NoEditTriggers)
        # تعيين مدة ظهور التلميحات إلى 5 ثواني (5000 مللي ثانية)
        self.general_table.setToolTipDuration(5000)
        # تغيير مؤشر الفأرة إلى يد عند المرور فوق الجدول
        self.general_table.setCursor(Qt.PointingHandCursor)
        self.general_table.horizontalHeader().setCursor(Qt.PointingHandCursor)
        self.general_table.verticalHeader().setCursor(Qt.PointingHandCursor)

        # تعيين خط التلميحات لكل النافذة
        self.setStyleSheet("""
            QToolTip {
                font-family: 'Calibri';
                font-size: 12pt;
                font-weight: bold;
                color: black;
                background-color: #f9f9f9;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 4px;
            }
        """)

        # إعداد صفوف الجدول مع إضافة تلميحات
        item_students = QTableWidgetItem("عدد التلاميذ")
        item_students.setToolTip("العدد الإجمالي للتلاميذ في القسم")
        self.general_table.setItem(0, 0, item_students)

        item_males = QTableWidgetItem("عدد الذكور")
        item_males.setToolTip("عدد التلاميذ الذكور في القسم")
        self.general_table.setItem(1, 0, item_males)

        item_females = QTableWidgetItem("عدد الإناث")
        item_females.setToolTip("عدد التلميذات الإناث في القسم")
        self.general_table.setItem(2, 0, item_females)

        item_absences = QTableWidgetItem("معدل الغياب")
        item_absences.setToolTip("مجموع ساعات غياب التلاميذ داخل الأسدس مقسوماً على عدد التلاميذ")
        self.general_table.setItem(3, 0, item_absences)

        item_lates = QTableWidgetItem("معدل التأخر")
        item_lates.setToolTip("مجموع حالات تأخر التلاميذ عن بداية الحصة الدراسية مقسوماً على عدد التلاميذ")
        self.general_table.setItem(4, 0, item_lates)

        item_date = QTableWidgetItem("تاريخ آخر تحديث")
        item_date.setToolTip("تاريخ آخر تحديث للبيانات")
        self.general_table.setItem(5, 0, item_date)

        item_permissions = QTableWidgetItem("معدل السماح")
        item_permissions.setToolTip("مجموع حالات السماح للتلاميذ بالدخول إلى القسم مقسوماً على عدد التلاميذ")
        self.general_table.setItem(6, 0, item_permissions)

        general_layout.addWidget(self.general_table)
        self.tab_widget.addTab(general_tab, "معلومات عامة")

        # علامة تبويب قائمة التلاميذ حسب السن
        students_tab = QWidget()
        students_layout = QVBoxLayout(students_tab)

        # جدول التلاميذ المجمع حسب السن
        self.students_by_age_table = QTableWidget()
        self.students_by_age_table.setColumnCount(2)
        self.students_by_age_table.setHorizontalHeaderLabels(["السن", "عدد التلاميذ"])
        self.students_by_age_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.students_by_age_table.verticalHeader().setVisible(False)
        self.students_by_age_table.setFont(self.font_normal)
        self.students_by_age_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.students_by_age_table.setToolTipDuration(5000)
        self.students_by_age_table.setToolTip("انقر على أي صف لعرض قائمة التلاميذ ذوي هذا السن")
        self.students_by_age_table.setCursor(Qt.PointingHandCursor)
        self.students_by_age_table.horizontalHeader().setCursor(Qt.PointingHandCursor)
        self.students_by_age_table.verticalHeader().setCursor(Qt.PointingHandCursor)
        self.students_by_age_table.cellClicked.connect(self.show_students_by_age)

        students_layout.addWidget(self.students_by_age_table)
        self.tab_widget.addTab(students_tab, "قائمة التلاميذ حسب السن")

        # علامة تبويب الإحصائيات
        stats_tab = QWidget()
        stats_layout = QVBoxLayout(stats_tab)

        # جدول الإحصائيات
        self.stats_table = QTableWidget()
        self.stats_table.setColumnCount(4)
        self.stats_table.setRowCount(5)
        self.stats_table.setHorizontalHeaderLabels(["البيان", "العدد", "المجموع", "النسبة المئوية"])
        self.stats_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.stats_table.verticalHeader().setVisible(False)
        self.stats_table.setFont(self.font_normal)
        self.stats_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.stats_table.setToolTipDuration(5000)
        self.stats_table.setCursor(Qt.PointingHandCursor)
        self.stats_table.horizontalHeader().setCursor(Qt.PointingHandCursor)
        self.stats_table.verticalHeader().setCursor(Qt.PointingHandCursor)

        # إعداد صفوف الجدول مع إضافة تلميحات
        item_regular = QTableWidgetItem("الحضور المنتظم")
        item_regular.setToolTip("عدد التلاميذ الذين ليس لديهم أي حالات غياب أو تأخر")
        self.stats_table.setItem(0, 0, item_regular)

        item_absence = QTableWidgetItem("الغياب المتكرر")
        item_absence.setToolTip("عدد التلاميذ الذين لديهم حالة غياب واحدة أو أكثر")
        self.stats_table.setItem(1, 0, item_absence)

        item_late = QTableWidgetItem("التأخر المتكرر")
        item_late.setToolTip("عدد التلاميذ الذين لديهم حالة تأخر واحدة أو أكثر عن بداية الحصة")
        self.stats_table.setItem(2, 0, item_late)

        item_violations = QTableWidgetItem("المخالفات")
        item_violations.setToolTip("عدد التلاميذ الذين لديهم مخالفات مسجلة")
        self.stats_table.setItem(3, 0, item_violations)

        item_permissions = QTableWidgetItem("السماح المتكرر")
        item_permissions.setToolTip("عدد التلاميذ الذين لديهم حالة سماح واحدة أو أكثر بالدخول")
        self.stats_table.setItem(4, 0, item_permissions)

        stats_layout.addWidget(self.stats_table)
        self.tab_widget.addTab(stats_tab, "إحصاءات التأخر والغياب")

        # علامة تبويب قائمة التلاميذ حسب مجموع الغياب
        absences_tab = QWidget()
        absences_layout = QVBoxLayout(absences_tab)

        # جدول التلاميذ المجمع حسب مجموع الغياب
        self.students_by_absences_table = QTableWidget()
        self.students_by_absences_table.setColumnCount(2)
        self.students_by_absences_table.setHorizontalHeaderLabels(["مجموع الغياب", "عدد التلاميذ"])
        self.students_by_absences_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.students_by_absences_table.verticalHeader().setVisible(False)
        self.students_by_absences_table.setFont(self.font_normal)
        self.students_by_absences_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.students_by_absences_table.setToolTipDuration(5000)
        self.students_by_absences_table.setToolTip("انقر على أي صف لعرض قائمة التلاميذ حسب مجموع الغياب")
        self.students_by_absences_table.setCursor(Qt.PointingHandCursor)
        self.students_by_absences_table.horizontalHeader().setCursor(Qt.PointingHandCursor)
        self.students_by_absences_table.verticalHeader().setCursor(Qt.PointingHandCursor)
        self.students_by_absences_table.cellClicked.connect(self.show_students_by_absences)

        absences_layout.addWidget(self.students_by_absences_table)
        self.tab_widget.addTab(absences_tab, "قائمة التلاميذ حسب مجموع الغياب")

        info_layout.addWidget(self.tab_widget)
        main_layout.addWidget(info_frame)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        # زر الطباعة
        print_btn = QPushButton("طباعة")
        print_btn.setFont(self.font_normal)
        print_btn.setCursor(Qt.PointingHandCursor)
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        print_btn.clicked.connect(self.print_card)
        buttons_layout.addWidget(print_btn)

        # زر التحديث
        refresh_btn = QPushButton("تحديث")
        refresh_btn.setFont(self.font_normal)
        refresh_btn.setCursor(Qt.PointingHandCursor)
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #0b7dda;
            }
        """)
        refresh_btn.clicked.connect(self.load_data)
        buttons_layout.addWidget(refresh_btn)

        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setFont(self.font_normal)
        close_btn.setCursor(Qt.PointingHandCursor)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(close_btn)
        buttons_layout.addStretch()

        main_layout.addLayout(buttons_layout)

    def on_section_changed(self):
        """معالجة تغيير القسم المحدد"""
        selected_section = self.section_combo.currentText()
        if selected_section:
            self.section = selected_section
            self.setWindowTitle(f"بطاقة اللوائح - {self.section}")
            self.load_data()

    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        if not self.section:
            print("لا يمكن تحميل البيانات. تأكد من تحديد القسم.")
            return

        try:
            # تحميل قائمة الأقسام
            self.load_sections()

            # الحصول على بيانات التلاميذ في القسم
            students = self.get_students_data()

            # حساب الإحصائيات
            total_students = len(students)
            male_count = sum(1 for student in students if student.get('gender') in ['ذكر', 'ذ'])
            female_count = total_students - male_count

            # تحديث جدول المعلومات العامة
            self.general_table.setItem(0, 1, QTableWidgetItem(str(total_students)))
            self.general_table.setItem(1, 1, QTableWidgetItem(str(male_count)))
            self.general_table.setItem(2, 1, QTableWidgetItem(str(female_count)))

            # حساب معدلات الغياب والتأخر والسماح
            total_absences = sum(int(student.get('absences', 0)) for student in students)
            total_lates = sum(int(student.get('lates', 0)) for student in students)
            total_permissions = sum(int(student.get('permissions', 0)) for student in students)

            absence_rate = total_absences / total_students if total_students > 0 else 0
            late_rate = total_lates / total_students if total_students > 0 else 0
            permission_rate = total_permissions / total_students if total_students > 0 else 0

            # إضافة القيم إلى الجدول
            self.general_table.setItem(3, 1, QTableWidgetItem(f"{absence_rate:.2f}"))
            self.general_table.setItem(4, 1, QTableWidgetItem(f"{late_rate:.2f}"))
            self.general_table.setItem(6, 1, QTableWidgetItem(f"{permission_rate:.2f}"))

            # تاريخ آخر تحديث
            current_date = QDate.currentDate().toString("yyyy-MM-dd")
            self.general_table.setItem(5, 1, QTableWidgetItem(current_date))

            # تحديث الجداول الأخرى
            self.update_age_table(students)
            self.update_stats_table(students)
            self.update_absences_table(students)

            # تنسيق الجداول
            self.format_tables()

            print(f"تم تحميل بيانات القسم {self.section} بنجاح.")

        except Exception as e:
            print(f"خطأ أثناء تحميل البيانات: {str(e)}")

    def load_sections(self):
        """تحميل قائمة الأقسام من قاعدة البيانات"""
        try:
            # حفظ القسم المحدد حالياً
            current_section = self.section_combo.currentText()

            # مسح القائمة الحالية
            self.section_combo.clear()

            # استخدام sqlite3
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()

            # التحقق من وجود جدول اللوائح
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='اللوائح'")
            has_regulations_table = cursor.fetchone() is not None

            if has_regulations_table:
                # الحصول على قائمة الأقسام الفريدة
                cursor.execute("""
                    SELECT DISTINCT القسم
                    FROM اللوائح
                    WHERE السنة_الدراسية = ?
                    ORDER BY القسم
                """, (self.academic_year,))

                sections = [row[0] for row in cursor.fetchall()]

                # إضافة الأقسام إلى القائمة المنسدلة
                for section in sections:
                    self.section_combo.addItem(section)

                # إعادة تحديد القسم السابق إذا كان موجوداً
                index = self.section_combo.findText(current_section)
                if index >= 0:
                    self.section_combo.setCurrentIndex(index)
                elif self.section_combo.count() > 0:
                    # تحديد القسم الأول إذا لم يكن القسم السابق موجوداً
                    self.section_combo.setCurrentIndex(0)
                    self.section = self.section_combo.currentText()

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل قائمة الأقسام: {str(e)}")

    def get_students_data(self):
        """الحصول على بيانات التلاميذ من قاعدة البيانات"""
        students = []

        try:
            # استخدام sqlite3
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()

            # استعلام للحصول على بيانات التلاميذ
            query = """
                SELECT s.الرمز, s.الاسم_والنسب, s.رت, s.النوع,
                       COALESCE(s.الغياب, 0) as absences,
                       COALESCE(s.التأخر, 0) as lates,
                       COALESCE(s.السماح, 0) as permissions,
                       COALESCE(s.الهاتف_الأول, '') as phone,
                       COALESCE(s.ملاحظات, '') as notes,
                       COALESCE(s.السن, 0) as age
                FROM جدول_عام s
                WHERE s.القسم = ? AND s.السنة_الدراسية = ?
                ORDER BY CAST(COALESCE(s.رت, '0') AS INTEGER)
            """

            # تنفيذ الاستعلام
            cursor.execute(query, (self.section, self.academic_year))
            results = cursor.fetchall()

            # تحويل النتائج إلى قائمة من القواميس
            for row in results:
                students.append({
                    'code': row[0],
                    'name': row[1],
                    'rt': row[2],
                    'gender': row[3],
                    'absences': row[4],
                    'lates': row[5],
                    'permissions': row[6],
                    'phone': row[7],
                    'notes': row[8],
                    'age': row[9] if len(row) > 9 else 0
                })

            conn.close()

            # إضافة بيانات تجريبية إذا كانت القائمة فارغة
            if not students:
                print("لم يتم العثور على بيانات. إضافة بيانات تجريبية...")
                for i in range(1, 11):
                    students.append({
                        'code': f"S{i:03d}",
                        'name': f"تلميذ تجريبي {i}",
                        'rt': i,
                        'gender': "ذكر" if i % 2 == 0 else "أنثى",
                        'absences': i % 5,
                        'lates': i % 3,
                        'permissions': i % 4,
                        'age': 14 + (i % 4)  # أعمار تجريبية بين 14 و17
                    })

        except Exception as e:
            print(f"خطأ في الحصول على بيانات التلاميذ: {str(e)}")

        return students

    def update_age_table(self, students):
        """تحديث جدول التلاميذ حسب السن"""
        # تجميع التلاميذ حسب السن
        students_by_age = {}
        for student in students:
            age = int(student.get('age', 0)) if student.get('age') is not None else 0
            if age not in students_by_age:
                students_by_age[age] = []
            students_by_age[age].append(student)

        # تحديث جدول التلاميذ حسب السن
        self.students_by_age_table.setRowCount(len(students_by_age))
        self.age_groups = {}  # قاموس لتخزين مجموعات التلاميذ حسب السن

        for row, (age, age_students) in enumerate(sorted(students_by_age.items())):
            # تخزين مجموعة التلاميذ لهذا السن
            self.age_groups[row] = {
                'age': age,
                'students': age_students
            }

            # إضافة البيانات إلى جدول التلاميذ حسب السن
            self.students_by_age_table.setItem(row, 0, QTableWidgetItem(str(age)))
            self.students_by_age_table.setItem(row, 1, QTableWidgetItem(str(len(age_students))))

    def update_stats_table(self, students):
        """تحديث جدول الإحصائيات"""
        total_students = len(students)

        # حساب الإحصائيات
        regular_attendance = sum(1 for student in students if int(student.get('absences', 0)) == 0 and int(student.get('lates', 0)) == 0)
        frequent_absence = sum(1 for student in students if int(student.get('absences', 0)) >= 1)
        frequent_late = sum(1 for student in students if int(student.get('lates', 0)) >= 1)
        frequent_permission = sum(1 for student in students if int(student.get('permissions', 0)) >= 1)
        violations = sum(1 for student in students if int(student.get('violations', 0)) > 0)

        # حساب مجموع حالات الغياب والتأخر والسماح
        total_absences = sum(int(student.get('absences', 0)) for student in students)
        total_lates = sum(int(student.get('lates', 0)) for student in students)
        total_permissions = sum(int(student.get('permissions', 0)) for student in students)
        total_violations = sum(int(student.get('violations', 0)) for student in students)

        # تحديث جدول الإحصائيات
        # الحضور المنتظم
        self.stats_table.setItem(0, 1, QTableWidgetItem(str(regular_attendance)))
        self.stats_table.setItem(0, 2, QTableWidgetItem("-"))

        # الغياب المتكرر
        self.stats_table.setItem(1, 1, QTableWidgetItem(str(frequent_absence)))
        self.stats_table.setItem(1, 2, QTableWidgetItem(str(total_absences)))

        # التأخر المتكرر
        self.stats_table.setItem(2, 1, QTableWidgetItem(str(frequent_late)))
        self.stats_table.setItem(2, 2, QTableWidgetItem(str(total_lates)))

        # المخالفات
        self.stats_table.setItem(3, 1, QTableWidgetItem(str(violations)))
        self.stats_table.setItem(3, 2, QTableWidgetItem(str(total_violations)))

        # السماح المتكرر
        self.stats_table.setItem(4, 1, QTableWidgetItem(str(frequent_permission)))
        self.stats_table.setItem(4, 2, QTableWidgetItem(str(total_permissions)))

        # حساب النسب المئوية
        if total_students > 0:
            # الحضور المنتظم
            self.stats_table.setItem(0, 3, QTableWidgetItem(f"{regular_attendance / total_students * 100:.2f}%"))

            # الغياب المتكرر
            self.stats_table.setItem(1, 3, QTableWidgetItem(f"{frequent_absence / total_students * 100:.2f}%"))

            # التأخر المتكرر
            self.stats_table.setItem(2, 3, QTableWidgetItem(f"{frequent_late / total_students * 100:.2f}%"))

            # المخالفات
            self.stats_table.setItem(3, 3, QTableWidgetItem(f"{violations / total_students * 100:.2f}%"))

            # السماح المتكرر
            self.stats_table.setItem(4, 3, QTableWidgetItem(f"{frequent_permission / total_students * 100:.2f}%"))
        else:
            for row in range(5):
                self.stats_table.setItem(row, 3, QTableWidgetItem("0.00%"))

    def update_absences_table(self, students):
        """تحديث جدول التلاميذ حسب مجموع الغياب"""
        # تحديد فئات الغياب: أقل من 10، من 10 إلى 20، من 21 إلى 30، أكثر من 30
        absence_ranges = [
            {"min": 0, "max": 9, "label": "أقل من 10"},
            {"min": 10, "max": 20, "label": "من 10 إلى 20"},
            {"min": 21, "max": 30, "label": "من 21 إلى 30"},
            {"min": 31, "max": float('inf'), "label": "أكثر من 30"}
        ]

        # تجميع التلاميذ حسب فئات الغياب
        students_by_absences = {range_info["label"]: [] for range_info in absence_ranges}

        for student in students:
            absences = int(student.get('absences', 0))
            for range_info in absence_ranges:
                if range_info["min"] <= absences <= range_info["max"]:
                    students_by_absences[range_info["label"]].append(student)
                    break

        # تحديث جدول التلاميذ حسب مجموع الغياب
        self.students_by_absences_table.setRowCount(len(absence_ranges))
        self.absence_groups = {}  # قاموس لتخزين مجموعات التلاميذ حسب فئات الغياب

        for row, range_info in enumerate(absence_ranges):
            label = range_info["label"]
            absence_students = students_by_absences[label]

            # تخزين مجموعة التلاميذ لهذه الفئة
            self.absence_groups[row] = {
                'label': label,
                'students': absence_students
            }

            # إضافة البيانات إلى جدول التلاميذ حسب مجموع الغياب
            self.students_by_absences_table.setItem(row, 0, QTableWidgetItem(label))
            self.students_by_absences_table.setItem(row, 1, QTableWidgetItem(str(len(absence_students))))

    def format_tables(self):
        """تنسيق الجداول لتحسين المظهر"""
        # تنسيق جدول المعلومات العامة
        for row in range(self.general_table.rowCount()):
            for col in range(self.general_table.columnCount()):
                item = self.general_table.item(row, col)
                if item:
                    item.setTextAlignment(Qt.AlignCenter)

        # تنسيق جدول التلاميذ حسب السن
        for row in range(self.students_by_age_table.rowCount()):
            for col in range(self.students_by_age_table.columnCount()):
                item = self.students_by_age_table.item(row, col)
                if item:
                    item.setTextAlignment(Qt.AlignCenter)
                    if row % 2 == 0:
                        item.setBackground(QColor(240, 240, 240))
                    if col == 0:
                        item.setBackground(QColor(230, 242, 255))

        # تنسيق جدول التلاميذ حسب مجموع الغياب
        for row in range(self.students_by_absences_table.rowCount()):
            for col in range(self.students_by_absences_table.columnCount()):
                item = self.students_by_absences_table.item(row, col)
                if item:
                    item.setTextAlignment(Qt.AlignCenter)
                    if row % 2 == 0:
                        item.setBackground(QColor(240, 240, 240))
                    if col == 0:
                        item.setBackground(QColor(255, 240, 230))

        # تنسيق جدول الإحصائيات
        for row in range(self.stats_table.rowCount()):
            for col in range(self.stats_table.columnCount()):
                item = self.stats_table.item(row, col)
                if item:
                    item.setTextAlignment(Qt.AlignCenter)
                    if col == 3:  # عمود النسبة المئوية
                        item.setBackground(QColor(240, 248, 255))

    def show_students_by_age(self, row, _):
        """عرض تفاصيل التلاميذ حسب السن المحدد في نافذة جديدة"""
        if row < 0 or row >= len(self.age_groups):
            return

        # الحصول على السن المحدد ومجموعة التلاميذ المرتبطة به
        age_group = self.age_groups[row]
        age = age_group['age']
        students_list = age_group['students']

        # إنشاء نافذة جديدة لعرض التلاميذ حسب السن
        students_dialog = QDialog(self)
        students_dialog.setWindowTitle(f"قائمة التلاميذ ذوي السن {age} سنة")
        students_dialog.setGeometry(0, 0, 700, 500)
        students_dialog.setLayoutDirection(Qt.RightToLeft)

        # إنشاء تخطيط للنافذة
        layout = QVBoxLayout(students_dialog)

        # إضافة عنوان
        title_label = QLabel(f"قائمة التلاميذ ذوي السن {age} سنة - عدد التلاميذ: {len(students_list)}")
        title_label.setFont(self.font_subtitle)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #0066cc; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # إنشاء جدول لعرض التلاميذ
        students_table = QTableWidget()
        students_table.setColumnCount(4)
        students_table.setHorizontalHeaderLabels(["الرمز", "الرقم الترتيبي", "الاسم والنسب", "تاريخ الازدياد"])
        students_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        students_table.verticalHeader().setVisible(False)
        students_table.setFont(self.font_normal)
        students_table.setEditTriggers(QTableWidget.NoEditTriggers)

        # ملء الجدول بالتلاميذ من الفئة العمرية المحددة
        students_table.setRowCount(len(students_list))
        for i, student in enumerate(students_list):
            code = str(student.get('code', '')) if student.get('code') is not None else ''
            rt = str(student.get('rt', '')) if student.get('rt') is not None else ''
            name = str(student.get('name', '')) if student.get('name') is not None else ''

            students_table.setItem(i, 0, QTableWidgetItem(code))
            students_table.setItem(i, 1, QTableWidgetItem(rt))
            students_table.setItem(i, 2, QTableWidgetItem(name))
            students_table.setItem(i, 3, QTableWidgetItem(""))

        layout.addWidget(students_table)

        # إضافة زر إغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setFont(self.font_normal)
        close_btn.setCursor(Qt.PointingHandCursor)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        close_btn.clicked.connect(students_dialog.close)

        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_btn)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        students_dialog.exec_()

    def show_students_by_absences(self, row, _):
        """عرض تفاصيل التلاميذ حسب مجموع الغياب المحدد في نافذة جديدة"""
        if row < 0 or row >= len(self.absence_groups):
            return

        # الحصول على فئة الغياب المحددة ومجموعة التلاميذ المرتبطة بها
        absence_group = self.absence_groups[row]
        label = absence_group['label']
        students_list = absence_group['students']

        # إنشاء نافذة جديدة لعرض التلاميذ حسب مجموع الغياب
        students_dialog = QDialog(self)
        students_dialog.setWindowTitle(f"قائمة التلاميذ ذوي مجموع الغياب {label}")
        students_dialog.setGeometry(0, 0, 700, 500)
        students_dialog.setLayoutDirection(Qt.RightToLeft)

        # إنشاء تخطيط للنافذة
        layout = QVBoxLayout(students_dialog)

        # إضافة عنوان
        title_label = QLabel(f"قائمة التلاميذ ذوي مجموع الغياب {label} - عدد التلاميذ: {len(students_list)}")
        title_label.setFont(self.font_subtitle)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #0066cc; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # إنشاء جدول لعرض التلاميذ
        students_table = QTableWidget()
        students_table.setColumnCount(4)
        students_table.setHorizontalHeaderLabels(["الرمز", "الرقم الترتيبي", "الاسم والنسب", "مجموع الغياب"])
        students_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        students_table.verticalHeader().setVisible(False)
        students_table.setFont(self.font_normal)
        students_table.setEditTriggers(QTableWidget.NoEditTriggers)

        # ملء الجدول بالتلاميذ من فئة الغياب المحددة
        students_table.setRowCount(len(students_list))
        for i, student in enumerate(students_list):
            code = str(student.get('code', '')) if student.get('code') is not None else ''
            rt = str(student.get('rt', '')) if student.get('rt') is not None else ''
            name = str(student.get('name', '')) if student.get('name') is not None else ''
            absences = str(student.get('absences', '')) if student.get('absences') is not None else ''

            students_table.setItem(i, 0, QTableWidgetItem(code))
            students_table.setItem(i, 1, QTableWidgetItem(rt))
            students_table.setItem(i, 2, QTableWidgetItem(name))
            students_table.setItem(i, 3, QTableWidgetItem(absences))

        layout.addWidget(students_table)

        # إضافة زر إغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setFont(self.font_normal)
        close_btn.setCursor(Qt.PointingHandCursor)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        close_btn.clicked.connect(students_dialog.close)

        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_btn)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        students_dialog.exec_()

    def print_card(self):
        """طباعة بطاقة اللوائح"""
        print("سيتم تنفيذ وظيفة طباعة بطاقة اللوائح قريباً.")
