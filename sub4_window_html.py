"""
نافذة الحراسة العامة - نسخة HTML حديثة
نافذة متقدمة لإدارة الحراسة العامة مع واجهة HTML تفاعلية

الميزات:
- واجهة HTML حديثة ومتجاوبة
- إدارة الحراسة مع أزرار تفاعلية
- طباعة النماذج المختلفة
- إدارة التأخير والدخول
- تفتح على كامل الشاشة
"""

import sys
import os
import json
from datetime import datetime
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal, QTimer, QDate, QTime
from PyQt5.QtGui import QIcon
from PyQt5.QtSql import QSqlDatabase, QSqlQuery

# استيراد ملف الوظائف المساعد
try:
    from sub44_window_html import GuardWindowFunctions
    FUNCTIONS_IMPORTED = True
except ImportError as e:
    print(f"تحذير: فشل في استيراد ملف الوظائف المساعد: {e}")
    FUNCTIONS_IMPORTED = False

class GuardEngine(QObject):
    """محرك نافذة الحراسة العامة"""
    
    # إشارات للتواصل مع واجهة HTML
    dataUpdated = pyqtSignal(str)  # data JSON
    studentsUpdated = pyqtSignal(str)  # students data JSON
    
    def __init__(self):
        super().__init__()
        self.db_path = "data.db"
        self.db = None
        self.current_academic_year = ""
        self.selected_level = ""
        self.selected_class = ""
        self.selected_guard = "حراسة رقم 1"
        self.selected_students = []
        self.all_students = []
        
        # إعدادات التاريخ والوقت
        self.current_date = QDate.currentDate()
        self.current_time = QTime.currentTime()
        self.use_custom_datetime = False
        
        # فتح قاعدة البيانات
        self.init_database()
        self.load_academic_year()
        
        # تهيئة الوظائف المساعدة
        if FUNCTIONS_IMPORTED:
            self.functions = GuardWindowFunctions(self)
            print("تم تحميل ملف الوظائف المساعد بنجاح")
        else:
            self.functions = None
            print("تحذير: ملف الوظائف المساعد غير متوفر")
    
    def init_database(self):
        """تهيئة قاعدة البيانات"""
        try:
            if not os.path.exists(self.db_path):
                self.emit_log("خطأ: ملف قاعدة البيانات غير موجود", "error")
                return False
            
            # استخدام QSqlDatabase بدلاً من sqlite3
            self.db = QSqlDatabase.addDatabase("QSQLITE")
            self.db.setDatabaseName(self.db_path)
            
            if self.db.open():
                self.emit_log("تم الاتصال بقاعدة البيانات بنجاح", "success")
                return True
            else:
                self.emit_log("خطأ: فشل في فتح قاعدة البيانات", "error")
                return False
                
        except Exception as e:
            self.emit_log(f"خطأ في تهيئة قاعدة البيانات: {str(e)}", "error")
            return False
    
    def load_academic_year(self):
        """تحميل السنة الدراسية"""
        try:
            query = QSqlQuery(self.db)
            query.exec_("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            
            if query.next():
                self.current_academic_year = query.value(0) or "2024-2025"
            else:
                self.current_academic_year = "2024-2025"
                
            self.emit_log(f"السنة الدراسية: {self.current_academic_year}", "info")
        except Exception as e:
            self.current_academic_year = "2024-2025"
            self.emit_log(f"خطأ في تحميل السنة الدراسية: {str(e)}", "error")
    
    def emit_log(self, message, status="info"):
        """إرسال رسالة للواجهة (معطلة حالياً)"""
        # تم إزالة سجل العمليات من الواجهة
        pass
    
    @pyqtSlot(result=str)
    def getSystemInfo(self):
        """الحصول على معلومات النظام"""
        try:
            info = {
                "academic_year": self.current_academic_year,
                "selected_guard": self.selected_guard,
                "selected_level": self.selected_level,
                "selected_class": self.selected_class,
                "custom_datetime": self.use_custom_datetime,
                "current_date": self.current_date.toString("yyyy-MM-dd"),
                "current_time": self.current_time.toString("hh:mm"),
                "database_connected": self.db is not None,
                "total_students": len(self.all_students),
                "selected_students_count": len(self.selected_students)
            }
            return json.dumps(info, ensure_ascii=False)
        except Exception as e:
            self.emit_log(f"خطأ في جمع معلومات النظام: {str(e)}", "error")
            return json.dumps({"error": str(e)}, ensure_ascii=False)
    
    @pyqtSlot(result=str)
    def getLevels(self):
        """الحصول على قائمة المستويات"""
        try:
            print(f"🔍 البحث عن المستويات للسنة الدراسية: {self.current_academic_year}")
            
            levels = []
            
            # تجربة استعلامات مختلفة
            possible_queries = [
                # من جدول البنية التربوية
                """
                SELECT DISTINCT المستوى
                FROM البنية_التربوية 
                WHERE السنة_الدراسية = '{}'
                ORDER BY المستوى
                """.format(self.current_academic_year),
                
                # من جدول اللوائح
                """
                SELECT DISTINCT المستوى
                FROM اللوائح 
                WHERE السنة_الدراسية = '{}'
                ORDER BY المستوى
                """.format(self.current_academic_year),
                
                # من جدول البنية التربوية بدون السنة
                """
                SELECT DISTINCT المستوى
                FROM البنية_التربوية 
                ORDER BY المستوى
                """,
                
                # من جدول اللوائح بدون السنة
                """
                SELECT DISTINCT المستوى
                FROM اللوائح 
                ORDER BY المستوى
                """
            ]
            
            for i, query_str in enumerate(possible_queries):
                print(f"🔄 تجربة استعلام المستويات رقم {i+1}")
                try:
                    query = QSqlQuery(self.db)
                    if query.exec_(query_str):
                        while query.next():
                            level = query.value(0)
                            if level and level not in levels:
                                levels.append(level)
                                print(f"   ✅ تم العثور على المستوى: {level}")
                        
                        if levels:
                            print(f"✅ تم العثور على {len(levels)} مستوى في الاستعلام رقم {i+1}")
                            break
                    else:
                        print(f"❌ خطأ في استعلام المستويات رقم {i+1}: {query.lastError().text()}")
                        
                except Exception as e:
                    print(f"❌ خطأ في استعلام المستويات رقم {i+1}: {str(e)}")
            
            self.emit_log(f"تم تحميل {len(levels)} مستوى", "success" if levels else "warning")
            return json.dumps(levels, ensure_ascii=False)
            
        except Exception as e:
            error_msg = f"خطأ في تحميل المستويات: {str(e)}"
            print(f"❌ {error_msg}")
            self.emit_log(error_msg, "error")
            return json.dumps([], ensure_ascii=False)
    
    @pyqtSlot(str, result=str)
    def getClasses(self, level):
        """الحصول على قائمة الأقسام للمستوى المحدد"""
        try:
            self.selected_level = level
            
            print(f"🔍 البحث عن الأقسام للمستوى: {level}")
            
            classes = []
            
            # تجربة استعلامات مختلفة
            possible_queries = [
                # من جدول البنية التربوية مع السنة الدراسية
                """
                SELECT DISTINCT القسم
                FROM البنية_التربوية
                WHERE السنة_الدراسية = '{}' AND المستوى = '{}'
                ORDER BY القسم
                """.format(self.current_academic_year, level),
                
                # من جدول البنية التربوية بدون السنة الدراسية
                """
                SELECT DISTINCT القسم
                FROM البنية_التربوية
                WHERE المستوى = '{}'
                ORDER BY القسم
                """.format(level),
                
                # من جدول اللوائح مع السنة الدراسية
                """
                SELECT DISTINCT القسم
                FROM اللوائح
                WHERE السنة_الدراسية = '{}' AND المستوى = '{}'
                ORDER BY القسم
                """.format(self.current_academic_year, level),
                
                # من جدول اللوائح بدون السنة الدراسية
                """
                SELECT DISTINCT القسم
                FROM اللوائح
                WHERE المستوى = '{}'
                ORDER BY القسم
                """.format(level)
            ]
            
            for i, query_str in enumerate(possible_queries):
                print(f"🔄 تجربة استعلام الأقسام رقم {i+1}")
                try:
                    query = QSqlQuery(self.db)
                    if query.exec_(query_str):
                        while query.next():
                            class_name = query.value(0)
                            if class_name and class_name not in classes:
                                classes.append(class_name)
                                print(f"   ✅ تم العثور على القسم: {class_name}")
                        
                        if classes:
                            print(f"✅ تم العثور على {len(classes)} قسم في الاستعلام رقم {i+1}")
                            break
                    else:
                        print(f"❌ خطأ في استعلام الأقسام رقم {i+1}: {query.lastError().text()}")
                        
                except Exception as e:
                    print(f"❌ خطأ في استعلام الأقسام رقم {i+1}: {str(e)}")
            
            print(f"✅ إجمالي الأقسام الموجودة: {len(classes)}")
            self.emit_log(f"تم تحميل {len(classes)} قسم للمستوى {level}", "success" if classes else "warning")
            return json.dumps(classes, ensure_ascii=False)
            
        except Exception as e:
            error_msg = f"خطأ في تحميل الأقسام: {str(e)}"
            print(f"❌ {error_msg}")
            self.emit_log(error_msg, "error")
            return json.dumps([], ensure_ascii=False)
    
    @pyqtSlot(str, result=str)
    def getStudents(self, class_name):
        """الحصول على قائمة التلاميذ للقسم المحدد
        
        مصدر البيانات:
        - الجدول الرئيسي: 'اللوائح' - يحتوي على بيانات التلاميذ الأساسية
        - الجدول الثانوي: 'السجل_العام' - يحتوي على بيانات السماح والتأخر والمخالفات
        
        آلية التحديث:
        1. يتم استدعاء هذه الدالة عند اختيار قسم جديد من واجهة المستخدم
        2. تقوم بالبحث في جدول 'اللوائح' بناءً على السنة الدراسية والقسم
        3. تربط البيانات مع جدول 'السجل_العام' للحصول على معلومات إضافية
        4. ترسل النتائج للواجهة عبر إشارة studentsUpdated
        5. تقوم الواجهة بتحديث قائمة التلاميذ وشريط المعلومات تلقائياً
        """
        try:
            self.selected_class = class_name
            
            query = QSqlQuery(self.db)
            
            # طباعة المعلومات للتتبع
            print(f"🔍 البحث عن التلاميذ في:")
            print(f"   السنة الدراسية: {self.current_academic_year}")
            print(f"   القسم: {class_name}")
            print(f"   مصدر البيانات: جدول 'اللوائح' + جدول 'السجل_العام'")
            
            # أولاً التحقق من وجود بيانات في جدول اللوائح (المصدر الرئيسي للتلاميذ)
            check_query = QSqlQuery(self.db)
            check_query.prepare("SELECT COUNT(*) FROM اللوائح WHERE السنة_الدراسية = ? AND القسم = ?")
            check_query.addBindValue(self.current_academic_year)
            check_query.addBindValue(class_name)
            
            if not check_query.exec_() or not check_query.next() or check_query.value(0) == 0:
                print("❌ لا توجد بيانات في جدول اللوائح للسنة الدراسية والقسم المحددين")
                # محاولة البحث بدون السنة الدراسية
                check_query2 = QSqlQuery(self.db)
                check_query2.prepare("SELECT COUNT(*) FROM اللوائح WHERE القسم = ?")
                check_query2.addBindValue(class_name)
                
                if not check_query2.exec_() or not check_query2.next() or check_query2.value(0) == 0:
                    print("❌ لا توجد بيانات في جدول اللوائح للقسم المحدد")
                    self.emit_log(f"لا توجد بيانات التلاميذ للقسم {class_name}", "warning")
                    return json.dumps([], ensure_ascii=False)
            
            # الاستعلام الرئيسي لجلب التلاميذ (محسّن ومُصحح بناءً على الملف الأصلي)
            # يتم ربط جدول اللوائح مع جدول السجل_العام للحصول على معلومات كاملة
            query_str = """
                SELECT
                    CAST(COALESCE(l.رت, '0') AS INTEGER) as الترتيب,
                    s.الرمز,
                    s.الاسم_والنسب,
                    s.السماح,
                    s.التأخر,
                    s.عدد_المخالفات,
                    s.الهاتف_الأول,
                    s.ملاحظات
                FROM اللوائح l
                JOIN السجل_العام s ON l.الرمز = s.الرمز
                WHERE l.السنة_الدراسية = ? AND l.القسم = ?
                ORDER BY CAST(COALESCE(l.رت, '0') AS INTEGER)
            """
            
            query.prepare(query_str)
            query.addBindValue(self.current_academic_year)
            query.addBindValue(class_name)
            
            students = []
            
            if query.exec_():
                print(f"✅ تم تنفيذ الاستعلام بنجاح")
                while query.next():
                    student = {
                        "rt": str(query.value(0) or ""),
                        "code": str(query.value(1) or ""),
                        "name": str(query.value(2) or ""),
                        "permission": str(query.value(3) or ""),
                        "late": str(query.value(4) or ""),
                        "violations": str(query.value(5) or ""),
                        "phone": str(query.value(6) or ""),
                        "notes": str(query.value(7) or ""),
                        "selected": False
                    }
                    students.append(student)
                    print(f"   ✅ تم العثور على: {student['name']} - {student['code']} (رت: {student['rt']})")
                
                print(f"✅ تم العثور على {len(students)} تلميذ")
            else:
                error = query.lastError().text()
                print(f"❌ خطأ في تنفيذ الاستعلام: {error}")
                
                # محاولة بدون السنة الدراسية (استعلام احتياطي)
                print("🔄 محاولة البحث بدون السنة الدراسية...")
                query_str_alt = """
                    SELECT
                        CAST(COALESCE(l.رت, '0') AS INTEGER) as الترتيب,
                        s.الرمز,
                        s.الاسم_والنسب,
                        s.السماح,
                        s.التأخر,
                        s.عدد_المخالفات,
                        s.الهاتف_الأول,
                        s.ملاحظات
                    FROM اللوائح l
                    JOIN السجل_العام s ON l.الرمز = s.الرمز
                    WHERE l.القسم = ?
                    ORDER BY CAST(COALESCE(l.رت, '0') AS INTEGER)
                """
                
                query.prepare(query_str_alt)
                query.addBindValue(class_name)
                
                if query.exec_():
                    while query.next():
                        student = {
                            "rt": str(query.value(0) or ""),
                            "code": str(query.value(1) or ""),
                            "name": str(query.value(2) or ""),
                            "permission": str(query.value(3) or ""),
                            "late": str(query.value(4) or ""),
                            "violations": str(query.value(5) or ""),
                            "phone": str(query.value(6) or ""),
                            "notes": str(query.value(7) or ""),
                            "selected": False
                        }
                        students.append(student)
                        print(f"   ✅ تم العثور على: {student['name']} - {student['code']}")
                else:
                    print(f"❌ خطأ في الاستعلام البديل: {query.lastError().text()}")
            
            # إذا لم نجد تلاميذ، نعرض تشخيصات إضافية لمساعدة المطور
            if not students:
                print("🔍 اختبار: فحص أسماء الأعمدة في جدول اللوائح...")
                
                # فحص بنية الجدول أولاً
                schema_query = QSqlQuery(self.db)
                if schema_query.exec_("PRAGMA table_info(اللوائح)"):
                    print("📋 أعمدة جدول اللوائح:")
                    while schema_query.next():
                        col_name = schema_query.value(1)  # اسم العمود
                        col_type = schema_query.value(2)  # نوع العمود
                        print(f"   - {col_name} ({col_type})")
                
                # اختبار جلب أول 3 تلاميذ مع جميع الأعمدة
                test_query = QSqlQuery(self.db)
                if test_query.exec_("SELECT * FROM اللوائح LIMIT 3"):
                    print("✅ نماذج من بيانات التلاميذ:")
                    row_count = 0
                    while test_query.next() and row_count < 3:
                        print(f"   === تلميذ {row_count + 1} ===")
                        # طباعة جميع القيم (أول 10 أعمدة)
                        for i in range(min(10, test_query.record().count())):
                            field_name = test_query.record().fieldName(i)
                            field_value = test_query.value(i)
                            print(f"     {field_name}: {field_value}")
                        row_count += 1
                else:
                    print(f"❌ فشل في جلب التلاميذ: {test_query.lastError().text()}")
                
                # فحص إجمالي التلاميذ
                count_query = QSqlQuery(self.db)
                if count_query.exec_("SELECT COUNT(*) FROM اللوائح") and count_query.next():
                    total_count = count_query.value(0)
                    print(f"📊 إجمالي التلاميذ في جدول اللوائح: {total_count}")
                else:
                    print("❌ فشل في عد التلاميذ")
                
                # فحص الأقسام الموجودة
                query.prepare("SELECT DISTINCT القسم FROM اللوائح LIMIT 20")
                if query.exec_():
                    print("📋 الأقسام الموجودة:")
                    while query.next():
                        section = query.value(0)
                        print(f"   - '{section}'")
                
                # فحص السنوات الدراسية
                query.prepare("SELECT DISTINCT السنة_الدراسية FROM اللوائح LIMIT 10")
                if query.exec_():
                    print("� السنوات الدراسية الموجودة:")
                    while query.next():
                        year = query.value(0)
                        print(f"   - '{year}'")
            
            self.all_students = students
            self.emit_log(f"تم تحميل {len(students)} تلميذ للقسم {class_name}", "success" if students else "warning")
            
            # إرسال إشارة تحديث التلاميذ للواجهة
            # هذه الإشارة تُرسل البيانات لدالة updateStudentsList في JavaScript
            self.studentsUpdated.emit(json.dumps(students, ensure_ascii=False))
            
            return json.dumps(students, ensure_ascii=False)
            
        except Exception as e:
            error_msg = f"خطأ في تحميل التلاميذ: {str(e)}"
            print(f"❌ {error_msg}")
            self.emit_log(error_msg, "error")
            return json.dumps([], ensure_ascii=False)
    
    @pyqtSlot(str)
    def setSelectedStudents(self, students_json):
        """تحديد التلاميذ المحددين"""
        try:
            students = json.loads(students_json)
            self.selected_students = [s for s in students if s.get('selected', False)]
            self.emit_log(f"تم تحديد {len(self.selected_students)} تلميذ", "info")
        except Exception as e:
            self.emit_log(f"خطأ في تحديد التلاميذ: {str(e)}", "error")
    
    @pyqtSlot(str)
    def setGuard(self, guard):
        """تحديد الحراسة"""
        self.selected_guard = guard
        self.emit_log(f"تم تحديد {guard}", "info")
    
    @pyqtSlot(str, str)
    def setCustomDateTime(self, date_str, time_str):
        """تعيين تاريخ ووقت مخصص"""
        try:
            self.current_date = QDate.fromString(date_str, "yyyy-MM-dd")
            self.current_time = QTime.fromString(time_str, "hh:mm")
            self.use_custom_datetime = True
            self.emit_log(f"تم تعيين التاريخ والوقت: {date_str} {time_str}", "info")
        except Exception as e:
            self.emit_log(f"خطأ في تعيين التاريخ والوقت: {str(e)}", "error")
    
    @pyqtSlot()
    def resetDateTime(self):
        """إعادة تعيين التاريخ والوقت للحالي"""
        self.current_date = QDate.currentDate()
        self.current_time = QTime.currentTime()
        self.use_custom_datetime = False
        self.emit_log("تم إعادة تعيين التاريخ والوقت للحالي", "info")
    
    @pyqtSlot()
    def addToEntrySheet(self):
        """إضافة للسماح بالدخول"""
        if not self.selected_students:
            self.emit_log("الرجاء تحديد طلاب أولاً", "warning")
            return
        
        try:
            from PyQt5.QtSql import QSqlQuery
            
            # الحصول على السنة الدراسية والفصل
            year, semester = self.get_current_school_year_and_semester()
            
            # تحديد التاريخ والوقت
            current_date = self.current_date.toString("yyyy-MM-dd")
            time_to_use = self.current_time.toString("hh:mm")
            
            query = QSqlQuery(self.db)
            query.prepare("""
                INSERT INTO ورقة_السماح_بالدخول(
                    السنة_الدراسية, الأسدس, الرقم_الترتيبي, التاريخ,
                    الاسم_والنسب, سماح, الرمز, الوقت, ورقة_السماح
                ) VALUES (?, ?, ?, ?, ?, 1, ?, ?, ' سماح ')
            """)
            
            success_count = 0
            for student in self.selected_students:
                query.addBindValue(year)
                query.addBindValue(semester)
                query.addBindValue(student.get('rt', ''))
                query.addBindValue(current_date)
                query.addBindValue(student.get('name', ''))
                query.addBindValue(student.get('code', ''))
                query.addBindValue(time_to_use)
                
                if query.exec_():
                    success_count += 1
                query.clear()
            
            if success_count > 0:
                self.emit_log(f"تم تسجيل السماح بالدخول لـ {success_count} طالب", "success")
                # مسح التحديدات
                self.selected_students = []
            else:
                self.emit_log("فشل في تسجيل السماح بالدخول", "error")
                
        except Exception as e:
            self.emit_log(f"خطأ في تسجيل السماح بالدخول: {str(e)}", "error")
    
    @pyqtSlot()
    def addToLateSheet(self):
        """إضافة لتسجيل التأخر"""
        if not self.selected_students:
            self.emit_log("الرجاء تحديد طلاب أولاً", "warning")
            return
        
        try:
            from PyQt5.QtSql import QSqlQuery
            
            # الحصول على السنة الدراسية والفصل
            year, semester = self.get_current_school_year_and_semester()
            
            # تحديد التاريخ والوقت
            current_date = self.current_date.toString("yyyy-MM-dd")
            time_to_use = self.current_time.toString("hh:mm")
            
            query = QSqlQuery(self.db)
            query.prepare("""
                INSERT INTO ورقة_السماح_بالدخول(
                    السنة_الدراسية, الأسدس, الرقم_الترتيبي, التاريخ,
                    الاسم_والنسب, سماح, الرمز, الوقت, ورقة_السماح
                ) VALUES (?, ?, ?, ?, ?, 0, ?, ?, ' تأخر ')
            """)
            
            success_count = 0
            for student in self.selected_students:
                query.addBindValue(year)
                query.addBindValue(semester)
                query.addBindValue(student.get('rt', ''))
                query.addBindValue(current_date)
                query.addBindValue(student.get('name', ''))
                query.addBindValue(student.get('code', ''))
                query.addBindValue(time_to_use)
                
                if query.exec_():
                    success_count += 1
                query.clear()
            
            if success_count > 0:
                self.emit_log(f"تم تسجيل التأخر لـ {success_count} طالب", "success")
                # مسح التحديدات
                self.selected_students = []
            else:
                self.emit_log("فشل في تسجيل التأخر", "error")
                
        except Exception as e:
            self.emit_log(f"خطأ في تسجيل التأخر: {str(e)}", "error")
    
    @pyqtSlot()
    def printStudentCard(self):
        """طباعة ورقة توجيه الطالب"""
        if not self.selected_students:
            self.emit_log("الرجاء تحديد طلاب أولاً", "warning")
            return
        
        try:
            # محاولة استخدام وحدة الطباعة
            self.emit_log(f"جاري طباعة ورقة توجيه لـ {len(self.selected_students)} طالب...", "info")
            
            # هنا يمكن إضافة كود الطباعة الفعلي
            # print_guidance_form(students=self.selected_students, section=self.selected_class)
            
            self.emit_log("تم طباعة ورقة توجيه الطالب بنجاح", "success")
            
        except Exception as e:
            self.emit_log(f"خطأ في طباعة ورقة توجيه الطالب: {str(e)}", "error")
    
    @pyqtSlot()
    def printSecretCode(self):
        """طباعة الرموز السرية"""
        if not self.selected_students:
            self.emit_log("الرجاء تحديد طلاب أولاً", "warning")
            return
        
        try:
            self.emit_log(f"جاري طباعة الرموز السرية لـ {len(self.selected_students)} طالب...", "info")
            
            # هنا يمكن إضافة كود الطباعة الفعلي
            
            self.emit_log("تم طباعة الرموز السرية بنجاح", "success")
            
        except Exception as e:
            self.emit_log(f"خطأ في طباعة الرموز السرية: {str(e)}", "error")
    
    @pyqtSlot()
    def printPermissionSlip(self):
        """طباعة ورقة استئذان"""
        if not self.selected_students:
            self.emit_log("الرجاء تحديد طلاب أولاً", "warning")
            return
        
        try:
            self.emit_log(f"جاري طباعة ورقة استئذان لـ {len(self.selected_students)} طالب...", "info")
            
            # هنا يمكن إضافة كود الطباعة الفعلي
            
            self.emit_log("تم طباعة ورقة الاستئذان بنجاح", "success")
            
        except Exception as e:
            self.emit_log(f"خطأ في طباعة ورقة الاستئذان: {str(e)}", "error")
    
    def get_current_school_year_and_semester(self):
        """الحصول على السنة الدراسية والفصل الحالي"""
        try:
            from PyQt5.QtSql import QSqlQuery
            query = QSqlQuery(self.db)
            query.exec_("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
            
            if query.next():
                year = query.value(0) or self.current_academic_year
                semester = query.value(1) or "الأول"
            else:
                year = self.current_academic_year
                semester = "الأول"
            
            return year, semester
        except:
            return self.current_academic_year, "الأول"
    
    # ===== وظائف الأزرار الجديدة مع ربط الملف المساعد =====
    
    @pyqtSlot()
    def buttonEntrySheet(self):
        """وظيفة زر ورقة الدخول - مع ربط الملف المساعد وتحديث تلقائي"""
        if not self.selected_students:
            self.emit_log("الرجاء تحديد طلاب أولاً", "warning")
            return
            
        if not self.selected_class:
            self.emit_log("الرجاء تحديد قسم أولاً", "warning")
            return
            
        if self.functions:
            try:
                # تحديث بيانات التلاميذ المحددين في الملف المساعد
                self.functions.selected_students = self.selected_students
                
                # تحديث بيانات التاريخ والوقت في الملف المساعد
                self.functions.current_date = self.current_date
                self.functions.current_time = self.current_time
                
                # تحديث القسم المحدد
                if hasattr(self.functions.parent, 'selected_section'):
                    self.functions.parent.selected_section = self.selected_class
                if hasattr(self.functions.parent, 'current_section'):
                    self.functions.parent.current_section = self.selected_class
                
                # استدعاء دالة إضافة ورقة الدخول
                result = self.functions.add_to_entry_sheet()
                
                if result and result.get('success', False):
                    # مسح التحديدات بعد النجاح
                    self.selected_students = []
                    self.emit_log(f"تم تسجيل السماح بالدخول لـ {result.get('processed_count', 0)} طالب بنجاح", "success")
                    print(f"✅ نجحت عملية ورقة الدخول لـ {result.get('processed_count', 0)} طالب")
                    
                    # إعادة تحميل بيانات التلاميذ لتحديث الإحصائيات فوراً
                    print("🔄 استدعاء reload_all_data لإرسال إشارة مسح التحديدات...")
                    self.reload_all_data()
                else:
                    self.emit_log(f"فشل في تسجيل ورقة الدخول: {result.get('message', 'خطأ غير محدد')}", "error")
                    
            except Exception as e:
                self.emit_log(f"خطأ في وظيفة ورقة الدخول: {str(e)}", "error")
        else:
            self.emit_log("ملف الوظائف المساعد غير متوفر", "error")
    
    @pyqtSlot()
    def buttonLateSheet(self):
        """وظيفة زر ورقة التأخر - مع ربط الملف المساعد والطباعة الصامتة"""
        if not self.selected_students:
            self.emit_log("الرجاء تحديد طلاب أولاً", "warning")
            return
            
        if not self.selected_class:
            self.emit_log("الرجاء تحديد القسم أولاً", "warning")
            return
            
        if self.functions:
            try:
                # تحديث بيانات التلاميذ المحددين في الملف المساعد
                self.functions.selected_students = self.selected_students
                
                # تحديث بيانات التاريخ والوقت في الملف المساعد
                self.functions.current_date = self.current_date
                self.functions.current_time = self.current_time
                
                # استدعاء دالة إضافة ورقة التأخر الجديدة المحسنة
                result = self.functions.add_to_late_sheet_new()
                
                if result and result.get('success', False):
                    success_count = result.get('success_count', 0)
                    self.emit_log(f"تم تسجيل التأخر لـ {success_count} طالب بنجاح", "success")
                    print(f"✅ نجحت عملية ورقة التأخر لـ {success_count} طالب")
                    # ملاحظة: reload_all_data يتم استدعاؤه تلقائياً من داخل add_to_late_sheet_new
                else:
                    self.emit_log(f"فشل في تسجيل ورقة التأخر: {result.get('message', 'خطأ غير محدد')}", "error")
                    
            except Exception as e:
                self.emit_log(f"خطأ في وظيفة ورقة التأخر: {str(e)}", "error")
        else:
            self.emit_log("ملف الوظائف المساعد غير متوفر", "error")
    
    @pyqtSlot()
    def buttonGuidanceSheet(self):
        """وظيفة زر ورقة التوجيه - مع ربط الملف المساعد"""
        if self.functions:
            self.functions.selected_students = self.selected_students
            self.functions.print_student_card()
        else:
            self.emit_log("ملف الوظائف المساعد غير متوفر", "error")
    
    @pyqtSlot()
    def buttonPermissionSheet(self):
        """وظيفة زر ورقة الاستئذان - مع ربط الملف المساعد"""
        if self.functions:
            self.functions.selected_students = self.selected_students
            self.functions.show_violations_permission_slip()
        else:
            self.emit_log("ملف الوظائف المساعد غير متوفر", "error")
    
    @pyqtSlot()
    def buttonSecretCode(self):
        """وظيفة زر الرمز السري - مع ربط الملف المساعد"""
        if self.functions:
            self.functions.selected_students = self.selected_students
            self.functions.print_secret_code()
        else:
            self.emit_log("ملف الوظائف المساعد غير متوفر", "error")
    
    @pyqtSlot()
    def buttonRequestsManagement(self):
        """وظيفة زر مسك الطلبات - مع ربط الملف المساعد"""
        if self.functions:
            self.functions.selected_students = self.selected_students
            self.functions.open_absence_justification()
        else:
            self.emit_log("ملف الوظائف المساعد غير متوفر", "error")
    
    @pyqtSlot()
    def buttonInstructions(self):
        """وظيفة زر التعليمات - مع ربط الملف المساعد"""
        if self.functions:
            self.functions.show_help()
        else:
            self.emit_log("ملف الوظائف المساعد غير متوفر", "error")
    
    @pyqtSlot()
    def buttonDoctorVisit(self):
        """وظيفة زر زيارة الطبيب - مع ربط الملف المساعد"""
        if self.functions:
            self.functions.selected_students = self.selected_students
            self.functions.print_doctor_visit()
        else:
            self.emit_log("ملف الوظائف المساعد غير متوفر", "error")
    
    @pyqtSlot()
    def buttonExport(self):
        """وظيفة زر التصدير - مع ربط الملف المساعد"""
        if self.functions:
            self.functions.selected_students = self.selected_students
            self.functions.export_selected_students()
        else:
            self.emit_log("ملف الوظائف المساعد غير متوفر", "error")
    
    @pyqtSlot()
    def buttonRegulationsCard(self):
        """وظيفة زر بطاقة اللوائح - مع ربط الملف المساعد"""
        if self.functions:
            self.functions.selected_students = self.selected_students
            self.functions.show_regulations_card()
        else:
            self.emit_log("ملف الوظائف المساعد غير متوفر", "error")
    
    @pyqtSlot()
    def buttonUpdate(self):
        """وظيفة زر تحديث البيانات - مع ربط الملف المساعد"""
        if self.functions:
            self.functions.update_data()
            # إعادة تحميل البيانات بعد التحديث
            self.reload_all_data()
        else:
            self.emit_log("ملف الوظائف المساعد غير متوفر", "error")
    
    def reload_all_data(self):
        """إعادة تحميل جميع البيانات وإرسال إشارة لإزالة التحديد"""
        try:
            if self.selected_class:
                print(f"🔄 إعادة تحميل بيانات القسم: {self.selected_class}")
                
                # إعادة تحميل بيانات التلاميذ للقسم المحدد
                students_data = self.getStudents(self.selected_class)
                
                # إرسال البيانات المحدثة إلى الواجهة أولاً
                print("📤 إرسال إشارة studentsUpdated...")
                self.studentsUpdated.emit(students_data)
                
                # انتظار قصير لضمان معالجة الإشارة الأولى
                QTimer.singleShot(100, lambda: self._send_clear_signal())
                
                self.emit_log("تم تحديث البيانات وإزالة التحديدات بنجاح", "success")
                print("✅ انتهت عملية reload_all_data بنجاح")
            else:
                self.emit_log("لا يوجد قسم محدد لإعادة التحميل", "warning")
        except Exception as e:
            self.emit_log(f"خطأ في إعادة تحميل البيانات: {str(e)}", "error")
            print(f"❌ خطأ في reload_all_data: {str(e)}")
    
    def _send_clear_signal(self):
        """إرسال إشارة مسح التحديدات مع تأخير قصير"""
        try:
            # إشارة لإزالة التحديدات في JavaScript
            clear_data = {"action": "clearSelections", "timestamp": QTime.currentTime().toString()}
            signal_json = json.dumps(clear_data, ensure_ascii=False)
            print(f'📤 إرسال إشارة dataUpdated: {signal_json}')
            self.dataUpdated.emit(signal_json)
            print("✅ تم إرسال إشارة مسح التحديدات بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إرسال إشارة مسح التحديدات: {str(e)}")

class GuardWindow(QMainWindow):
    """نافذة الحراسة العامة الحديثة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🎓 نافذة الحراسة العامة")
        
        # فتح النافذة على كامل الشاشة
        self.showMaximized()
        
        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        
        # إنشاء محرك النافذة
        self.guard_engine = GuardEngine()
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_web_channel()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # إنشاء عرض الويب
        self.web_view = QWebEngineView()
        layout.addWidget(self.web_view)
        
        # تحميل واجهة HTML
        html_content = self.get_complete_html()
        self.web_view.setHtml(html_content)
    
    def setup_web_channel(self):
        """إعداد قناة التواصل مع JavaScript"""
        self.channel = QWebChannel()
        self.channel.registerObject("guardEngine", self.guard_engine)
        self.web_view.page().setWebChannel(self.channel)
        
        # انتظار تحميل الصفحة
        self.web_view.loadFinished.connect(self.on_page_loaded)
    
    def on_page_loaded(self):
        """استدعاء عند انتهاء تحميل الصفحة"""
        self.channel.registerObject("guardEngine", self.guard_engine)
        QTimer.singleShot(500, self.ensure_object_registration)
    
    def ensure_object_registration(self):
        """ضمان تسجيل الكائن بشكل صحيح"""
        try:
            self.channel.deregisterObject(self.guard_engine)
        except:
            pass
        self.channel.registerObject("guardEngine", self.guard_engine)
    
    def get_complete_html(self):
        """HTML كامل للواجهة"""
        return """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8">
    <title>نافذة الحراسة العامة</title>
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Calibri', 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }
        
        .header h1 {
            font-size: 2.5em;
            color: #667eea;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .header .subtitle {
            font-size: 1.2em;
            color: #666;
            font-weight: bold;
        }
        
        .info-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }
        
        .info-label {
            font-family: 'Calibri', 'Arial', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: white;
            opacity: 1;
            margin-bottom: 5px;
        }
        
        .info-value {
            font-family: 'Calibri', 'Arial', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 3fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .controls-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .controls-panel h3 {
            font-family: 'Calibri', 'Arial', sans-serif;
            font-size: 22px;
            font-weight: bold;
            color: #003366;
            margin-bottom: 20px;
            border-bottom: 2px solid #003366;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-family: 'Calibri', 'Arial', sans-serif;
            font-size: 18px;
            font-weight: bold;
            color: #000000;
        }
        
        .form-group select,
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-family: 'Calibri', 'Arial', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #000000;
            transition: border-color 0.3s;
        }
        
        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .datetime-controls {
            display: grid;
            grid-template-columns: 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        /* تنسيق قسم الأزرار */
        .buttons-section {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }
        
        .buttons-row {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            justify-content: space-between;
        }
        
        .buttons-row:last-child {
            margin-bottom: 0;
            justify-content: flex-start;
        }
        
        .action-btn {
            flex: 1;
            padding: 10px 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-family: 'Calibri', sans-serif;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            min-height: 40px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .action-btn:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }
        
        .action-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        /* للزر الأخير في الصف الأخير */
        .buttons-row:last-child .action-btn {
            flex: 0 0 calc(50% - 5px);
        }
        
        .students-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .students-panel h3 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.3em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .students-list {
            max-height: 800px;
            overflow-y: auto;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        
        /* تنسيق الجدول الجديد */
        .students-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            font-family: 'Calibri', 'Arial', sans-serif;
        }
        
        .students-table thead {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .students-table thead th {
            padding: 12px 8px;
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            border: 1px solid rgba(255,255,255,0.2);
            white-space: nowrap;
        }
        
        .students-table tbody tr {
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s;
        }
        
        .students-table tbody tr:hover {
            background-color: #f8f9ff;
            cursor: pointer;
        }
        
        .students-table tbody tr.selected {
            background-color: #e3f2fd;
        }
        
        .students-table tbody td {
            padding: 10px 8px;
            text-align: center;
            font-family: 'Calibri', 'Arial', sans-serif;
            font-size: 17px;
            font-weight: bold;
            border: 1px solid #eee;
            vertical-align: middle;
        }
        
        .student-checkbox {
            width: 16px;
            height: 16px;
            cursor: pointer;
            transform: scale(1.1);
        }
        
        .student-rt {
            font-weight: bold;
            color: #333;
        }
        
        .student-code {
            font-weight: bold;
            color: #667eea;
            font-family: 'Courier New', monospace;
        }
        
        .student-name {
            font-weight: bold;
            color: #333;
            text-align: right;
            padding-right: 10px;
        }
        
        .student-stat {
            font-size: 17px;
            font-weight: bold;
            padding: 3px 6px;
            border-radius: 3px;
            display: inline-block;
            min-width: 25px;
        }
        
        .stat-permission {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .stat-late {
            background: #fff3e0;
            color: #f57c00;
        }
        
        .stat-violations {
            background: #ffebee;
            color: #d32f2f;
        }
        
        .student-phone {
            font-family: 'Courier New', monospace;
            color: #666;
            font-size: 17px;
            font-weight: bold;
        }
        
        .student-notes {
            font-size: 11px;
            color: #666;
            text-align: right;
            max-width: 140px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .student-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s;
        }
        
        .student-item:hover {
            background-color: #f0f8ff;
        }
        
        .student-item:last-child {
            border-bottom: none;
        }
        
        .student-checkbox {
            margin-left: 10px;
            transform: scale(1.2);
        }
        
        .student-info {
            flex: 1;
            display: grid;
            grid-template-columns: 50px 70px 1fr 40px 40px 40px;
            gap: 8px;
            align-items: center;
        }
        
        .student-rt {
            font-weight: bold;
            color: #667eea;
            text-align: center;
            background: #e3f2fd;
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        .student-code {
            font-weight: bold;
            color: #333;
            text-align: center;
            background: #f0f0f0;
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        .student-name {
            font-weight: bold;
            color: #333;
        }
        
        .stat-entry {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .stat-late {
            background: #fff3e0;
            color: #f57c00;
        }
        
        .stat-violations {
            background: #ffebee;
            color: #d32f2f;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #667eea;
            font-size: 1.1em;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #999;
            font-size: 1.1em;
        }
        
        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .info-bar {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15px;
                text-align: center;
            }
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .info-bar {
                grid-template-columns: 1fr;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- شريط المعلومات -->
        <div class="info-bar">
            <div class="info-item">
                <span class="info-label">السنة الدراسية:</span>
                <span class="info-value" id="academicYearHeader">-</span>
            </div>
            <div class="info-item">
                <span class="info-label">المستوى:</span>
                <span class="info-value" id="currentLevelHeader">-</span>
            </div>
            <div class="info-item">
                <span class="info-label">القسم:</span>
                <span class="info-value" id="currentClassHeader">-</span>
            </div>
            <div class="info-item">
                <span class="info-label">عدد التلاميذ:</span>
                <span class="info-value" id="totalStudentsHeader">0</span>
            </div>
        </div>
        
        <!-- المحتوى الرئيسي -->
        <div class="main-content">
            <!-- لوحة التحكم -->
            <div class="controls-panel">
                <h3>🎯 التحكم والإعدادات</h3>
                
                <div class="form-group">
                    <select id="guardSelect" onchange="setGuard()">
                        <option value="حراسة رقم 1">حراسة رقم 1</option>
                        <option value="حراسة رقم 2">حراسة رقم 2</option>
                        <option value="حراسة رقم 3">حراسة رقم 3</option>
                        <option value="حراسة رقم 4">حراسة رقم 4</option>
                        <option value="حراسة رقم 5">حراسة رقم 5</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <select id="levelSelect" onchange="loadClasses()">
                        <option value="">اختر المستوى</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <select id="classSelect" onchange="loadStudents()">
                        <option value="">اختر القسم</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <div class="datetime-controls">
                        <input type="date" id="dateInput" onchange="updateDateTime()">
                        <input type="time" id="timeInput" onchange="updateDateTime()">
                    </div>
                </div>
                
                <!-- الأزرار -->
                <div class="buttons-section">
                    <div class="buttons-row">
                        <button class="action-btn" onclick="handleButtonClick('ورقة الدخول')">ورقة الدخول</button>
                        <button class="action-btn" onclick="handleButtonClick('ورقة التأخر')">ورقة التأخر</button>
                    </div>
                    <div class="buttons-row">
                        <button class="action-btn" onclick="handleButtonClick('ورقة توجيه')">ورقة توجيه</button>
                        <button class="action-btn" onclick="handleButtonClick('ورقة استئذان')">ورقة استئذان</button>
                    </div>
                    <div class="buttons-row">
                        <button class="action-btn" onclick="handleButtonClick('الرمز السري')">الرمز السري</button>
                        <button class="action-btn" onclick="handleButtonClick('مسك الطلبات')">مسك الطلبات</button>
                    </div>
                    <div class="buttons-row">
                        <button class="action-btn" onclick="handleButtonClick('تعليمات')">تعليمات</button>
                        <button class="action-btn" onclick="handleButtonClick('زيارة الطبيب')">زيارة الطبيب</button>
                    </div>
                    <div class="buttons-row">
                        <button class="action-btn" onclick="handleButtonClick('تصدير')">تصدير</button>
                        <button class="action-btn" onclick="handleButtonClick('بطاقة اللوائح')">بطاقة اللوائح</button>
                    </div>
                    <div class="buttons-row">
                        <button class="action-btn" onclick="handleButtonClick('تحديث')">تحديث</button>
                        <button class="action-btn" onclick="testClearSelections()" style="background: orange;">🧪 اختبار مسح</button>
                    </div>
                    <div class="buttons-row">
                        <button class="action-btn" onclick="showSystemStatus()" style="background: purple; color: white;">📊 حالة النظام</button>
                    </div>
                </div>
            </div>
            
            <!-- لوحة التلاميذ -->
            <div class="students-panel">
                <h3>👥 قائمة التلاميذ</h3>
                <div class="students-list" id="studentsList">
                    <div class="loading">جاري تحميل البيانات...</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let guardEngine = null;
        let isChannelReady = false;
        let currentStudents = [];
        let selectedStudents = [];
        
        // تهيئة القناة
        function initializeChannel() {
            if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                new QWebChannel(qt.webChannelTransport, function(channel) {
                    guardEngine = channel.objects.guardEngine;
                    isChannelReady = true;
                    
                    console.log('🚀 QWebChannel initialized successfully');
                    
                    // ربط الإشارات
                    if (guardEngine) {
                        console.log('🔗 بدء ربط الإشارات...');
                        
                        // ربط إشارة تحديث التلاميذ
                        guardEngine.studentsUpdated.connect(updateStudentsList);
                        console.log('✅ تم ربط studentsUpdated بـ updateStudentsList');
                        
                        // ربط إشارة تحديث البيانات
                        guardEngine.dataUpdated.connect(handleDataUpdate);
                        console.log('✅ تم ربط dataUpdated بـ handleDataUpdate');
                        
                        // اختبار الاتصال
                        console.log('🧪 اختبار وجود الإشارات:');
                        console.log('  - guardEngine.studentsUpdated:', typeof guardEngine.studentsUpdated);
                        console.log('  - guardEngine.dataUpdated:', typeof guardEngine.dataUpdated);
                        console.log('  - updateStudentsList:', typeof updateStudentsList);
                        console.log('  - handleDataUpdate:', typeof handleDataUpdate);
                        
                        console.log('🔗 تم ربط الإشارات بنجاح:');
                        console.log('  - studentsUpdated ← updateStudentsList');
                        console.log('  - dataUpdated ← handleDataUpdate');
                        
                        // تحميل البيانات الأولية
                        loadSystemInfo();
                        loadLevels();
                        
                        console.log('✅ تم تهيئة النظام بنجاح');
                    } else {
                        console.error('❌ guardEngine غير متوفر لربط الإشارات');
                    }
                });
            } else {
                console.log('⚠️ QWebChannel not available, retrying...');
                setTimeout(initializeChannel, 100);
            }
        }
        
        // تحميل معلومات النظام
        function loadSystemInfo() {
            if (guardEngine) {
                guardEngine.getSystemInfo(function(result) {
                    try {
                        const info = JSON.parse(result);
                        
                        // تحديث شريط المعلومات العلوي
                        document.getElementById('academicYearHeader').textContent = info.academic_year || '-';
                        
                        // تعيين التاريخ والوقت الحالي
                        const now = new Date();
                        document.getElementById('dateInput').value = now.toISOString().split('T')[0];
                        document.getElementById('timeInput').value = now.toTimeString().split(' ')[0].substr(0,5);
                        
                    } catch (error) {
                        console.error('Error parsing system info:', error);
                    }
                });
            }
        }
        
        // تحميل المستويات
        function loadLevels() {
            if (guardEngine) {
                guardEngine.getLevels(function(result) {
                    try {
                        const levels = JSON.parse(result);
                        const levelSelect = document.getElementById('levelSelect');
                        
                        levelSelect.innerHTML = '<option value="">اختر المستوى</option>';
                        
                        levels.forEach(level => {
                            const option = document.createElement('option');
                            option.value = level;
                            option.textContent = level;
                            levelSelect.appendChild(option);
                        });
                        
                    } catch (error) {
                        console.error('Error loading levels:', error);
                    }
                });
            }
        }
        
        // تحميل الأقسام
        function loadClasses() {
            const level = document.getElementById('levelSelect').value;
            if (!level || !guardEngine) return;
            
            guardEngine.getClasses(level, function(result) {
                try {
                    const classes = JSON.parse(result);
                    const classSelect = document.getElementById('classSelect');
                    
                    classSelect.innerHTML = '<option value="">اختر القسم</option>';
                    
                    classes.forEach(className => {
                        const option = document.createElement('option');
                        option.value = className;
                        option.textContent = className;
                        classSelect.appendChild(option);
                    });
                    
                    // تحديث شريط المعلومات العلوي
                    document.getElementById('currentLevelHeader').textContent = level;
                    document.getElementById('currentClassHeader').textContent = '-';
                    document.getElementById('totalStudentsHeader').textContent = '0';
                    
                    // مسح قائمة الطلاب
                    document.getElementById('studentsList').innerHTML = '<div class="loading">اختر قسماً لعرض الطلاب</div>';
                    
                } catch (error) {
                    console.error('Error loading classes:', error);
                }
            });
        }
        
        // تحميل التلاميذ (يتم استدعاؤها عند اختيار قسم جديد)
        function loadStudents() {
            const className = document.getElementById('classSelect').value;
            if (!className || !guardEngine) return;
            
            document.getElementById('studentsList').innerHTML = '<div class="loading">جاري تحميل التلاميذ...</div>';
            
            // استدعاء دالة getStudents في GuardEngine
            // التي تبحث في جدول اللوائح وترسل النتائج عبر إشارة studentsUpdated
            guardEngine.getStudents(className, function(result) {
                try {
                    const students = JSON.parse(result);
                    currentStudents = students;
                    selectedStudents = [];
                    
                    // تحديث واجهة المستخدم بقائمة التلاميذ الجديدة
                    updateStudentsList(result);
                    
                    // تحديث شريط المعلومات العلوي
                    document.getElementById('currentClassHeader').textContent = className;
                    document.getElementById('totalStudentsHeader').textContent = students.length.toString();
                    
                    console.log(`✅ تم تحميل ${students.length} تلميذ للقسم ${className}`);
                    
                } catch (error) {
                    console.error('Error loading students:', error);
                    document.getElementById('studentsList').innerHTML = '<div class="empty-state">خطأ في تحميل التلاميذ</div>';
                }
            });
        }
        
        // تحديث قائمة التلاميذ (يتم استدعاؤها تلقائياً عبر إشارة studentsUpdated)
        function updateStudentsList(studentsJson) {
            try {
                console.log('📋 بدء تحديث قائمة التلاميذ...');
                console.log('📋 البيانات المستقبلة:', studentsJson ? studentsJson.substring(0, 100) + '...' : 'فارغ');
                
                const students = JSON.parse(studentsJson);
                const container = document.getElementById('studentsList');
                
                currentStudents = students; // حفظ قائمة التلاميذ الحالية
                console.log(`📋 تم حفظ ${students.length} تلميذ في currentStudents`);
                
                // مسح التحديدات عند تحديث القائمة (احتياطي)
                const oldSelectedCount = selectedStudents.length;
                selectedStudents = [];
                console.log(`🔄 تم مسح التحديدات السابقة (كان: ${oldSelectedCount}, أصبح: ${selectedStudents.length})`);
                
                if (students.length === 0) {
                    container.innerHTML = '<div class="empty-state">لا يوجد تلاميذ في هذا القسم</div>';
                    return;
                }
                
                // إنشاء جدول HTML لعرض التلاميذ
                let html = `
                    <table class="students-table">
                        <thead>
                            <tr>
                                <th style="width: 60px;">تحديد</th>
                                <th style="width: 60px;">الترتيب</th>
                                <th style="width: 100px;">الرمز</th>
                                <th style="width: 200px;">الاسم والنسب</th>
                                <th style="width: 80px;">السماح</th>
                                <th style="width: 80px;">التأخر</th>
                                <th style="width: 80px;">المخالفات</th>
                                <th style="width: 120px;">الهاتف</th>
                                <th style="width: 150px;">ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                students.forEach((student, index) => {
                    // تأكد من عدم وجود تحديد (force unselected)
                    html += `
                        <tr onclick="toggleStudentSelection(${index})">
                            <td>
                                <input type="checkbox" class="student-checkbox" 
                                       onclick="event.stopPropagation(); toggleStudentSelection(${index})">
                            </td>
                            <td class="student-rt">${student.rt || '-'}</td>
                            <td class="student-code">${student.code || '-'}</td>
                            <td class="student-name">${student.name || '-'}</td>
                            <td><span class="student-stat stat-permission">${student.permission || '0'}</span></td>
                            <td><span class="student-stat stat-late">${student.late || '0'}</span></td>
                            <td><span class="student-stat stat-violations">${student.violations || '0'}</span></td>
                            <td class="student-phone">${student.phone || '-'}</td>
                            <td class="student-notes" title="${student.notes || ''}">${student.notes || '-'}</td>
                        </tr>
                    `;
                });
                
                html += `
                        </tbody>
                    </table>
                `;
                
                container.innerHTML = html;
                updateSelectedCount();
                
                // تحديث شريط المعلومات
                document.getElementById('totalStudentsHeader').textContent = students.length;
                
                console.log(`✅ تم عرض ${students.length} تلميذ في جدول منظم بدون تحديدات`);
                
            } catch (error) {
                console.error('❌ Error updating students list:', error);
                document.getElementById('studentsList').innerHTML = 
                    '<div class="empty-state">خطأ في تحميل قائمة التلاميذ</div>';
            }
        }
        
        // تبديل تحديد التلميذ
        function toggleStudentSelection(index) {
            if (!currentStudents || index >= currentStudents.length) return;
            
            const student = currentStudents[index];
            const isSelected = selectedStudents.some(s => s.code === student.code);
            
            if (isSelected) {
                selectedStudents = selectedStudents.filter(s => s.code !== student.code);
            } else {
                selectedStudents.push(student);
            }
            
            // تحديث مظهر الصف في الجدول
            const row = document.querySelector(`tbody tr:nth-child(${index + 1})`);
            const checkbox = row.querySelector('input[type="checkbox"]');
            
            if (!isSelected) {
                row.classList.add('selected');
                checkbox.checked = true;
            } else {
                row.classList.remove('selected');
                checkbox.checked = false;
            }
            
            updateSelectedCount();
            
            // إرسال التحديث للمحرك (GuardEngine)
            if (guardEngine) {
                const studentsWithSelection = currentStudents.map(s => ({
                    ...s,
                    selected: selectedStudents.some(sel => sel.code === s.code)
                }));
                guardEngine.setSelectedStudents(JSON.stringify(studentsWithSelection));
            }
        }
        
        // تعيين الحراسة
        function setGuard() {
            const guard = document.getElementById('guardSelect').value;
            if (guardEngine) {
                guardEngine.setGuard(guard);
            }
        }
        
        // مسح التحديدات وإعادة تحميل البيانات (يتم استدعاؤها بعد العمليات الناجحة)
        function clearSelectionsAndReload() {
            console.log('� === بدء عملية مسح التحديدات وإعادة التحميل ===');
            console.log('🔄 الحالة الحالية:');
            console.log('  - selectedStudents:', selectedStudents.length);
            console.log('  - currentStudents:', currentStudents.length);
            
            // مسح التحديدات
            const oldSelectedCount = selectedStudents.length;
            selectedStudents = [];
            console.log(`✅ تم مسح قائمة selectedStudents (كان: ${oldSelectedCount}، الآن: ${selectedStudents.length})`);
            
            // إزالة تحديد جميع المربعات
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            let uncheckedCount = 0;
            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    checkbox.checked = false;
                    uncheckedCount++;
                }
            });
            console.log(`✅ تم إلغاء تحديد ${uncheckedCount} من ${checkboxes.length} مربع تحديد`);
            
            // إزالة تأثير التحديد من جميع الصفوف
            const rows = document.querySelectorAll('tbody tr');
            let unselectedRows = 0;
            rows.forEach(row => {
                if (row.classList.contains('selected')) {
                    row.classList.remove('selected');
                    unselectedRows++;
                }
            });
            console.log(`✅ تم إزالة تأثير التحديد من ${unselectedRows} من ${rows.length} صف`);
            
            // تحديث العداد
            updateSelectedCount();
            
            // إعادة تحميل البيانات من الخادم
            console.log('📡 إعادة تحميل البيانات من الخادم...');
            const className = document.getElementById('classSelect').value;
            if (className && guardEngine) {
                console.log(`📡 استدعاء getStudents للقسم: ${className}`);
                guardEngine.getStudents(className, function(result) {
                    console.log('📡 تم استقبال النتائج من getStudents');
                    updateStudentsList(result);
                    console.log('🎉 === انتهت عملية مسح التحديدات وإعادة التحميل بنجاح ===');
                });
            } else {
                console.error('⚠️ لا يوجد قسم محدد أو المحرك غير متوفر');
                console.error(`  - className: '${className}'`);
                console.error(`  - guardEngine: ${guardEngine ? 'متوفر' : 'غير متوفر'}`);
            }
        }
        
        // تحديث عداد الطلاب المحددين
        function updateSelectedCount() {
            // لا حاجة لتحديث العداد المرئي بعد إزالة المربع
        }
        
        // التعامل مع تحديثات البيانات من الخادم
        function handleDataUpdate(data) {
            console.log('📨 تم استقبال تحديث البيانات:', data);
            console.log('📨 نوع البيانات:', typeof data);
            
            try {
                const updateData = typeof data === 'string' ? JSON.parse(data) : data;
                console.log('📨 البيانات المحولة:', updateData);
                
                if (updateData && updateData.action === 'clearSelections') {
                    console.log('🎯 تم تأكيد إشارة clearSelections - بدء المسح...');
                    clearSelectionsAndReload();
                } else {
                    console.log('⚠️ إشارة غير متوقعة أو لا تحتوي على action=clearSelections');
                    console.log('⚠️ المحتوى:', updateData);
                }
            } catch (error) {
                console.error('❌ خطأ في معالجة تحديث البيانات:', error);
                console.error('❌ البيانات الخام:', data);
            }
        }
        
        // التعامل مع النقر على الأزرار
        function handleButtonClick(buttonName) {
            console.log('تم النقر على الزر:', buttonName);
            
            if (!guardEngine) {
                showAlert('⚠️ خطأ في النظام', 'لم يتم تحميل النظام بعد، يرجى المحاولة مرة أخرى', 'warning');
                return;
            }
            
            // فحص خاص لأزرار ورقة الدخول والتأخر
            if ((buttonName === 'ورقة الدخول' || buttonName === 'ورقة التأخر') && selectedStudents.length === 0) {
                showAlert(
                    '📋 لم يتم تحديد تلاميذ', 
                    'يرجى تحديد التلاميذ المطلوبين أولاً قبل إنشاء ورقة ' + (buttonName === 'ورقة الدخول' ? 'الدخول' : 'التأخر'),
                    'info'
                );
                return;
            }
            
            // ربط كل زر بالوظيفة المناسبة
            switch(buttonName) {
                case 'ورقة الدخول':
                    console.log('🎯 تنفيذ عملية ورقة الدخول...');
                    showAlert('⏳ جاري المعالجة', 'يتم الآن تسجيل ورقة الدخول للتلاميذ المحددين...', 'info');
                    guardEngine.buttonEntrySheet();
                    console.log('⏳ انتظار انتهاء العملية ثم مسح التحديدات...');
                    // حل مؤقت: مسح التحديدات بعد 2 ثانية
                    setTimeout(() => {
                        console.log('🔧 تنفيذ مسح التحديدات (حل مؤقت)...');
                        clearSelectionsAndReload();
                    }, 2000);
                    break;
                case 'ورقة التأخر':
                    console.log('🎯 تنفيذ عملية ورقة التأخر...');
                    showAlert('⏳ جاري المعالجة', 'يتم الآن تسجيل ورقة التأخر للتلاميذ المحددين...', 'info');
                    guardEngine.buttonLateSheet();
                    console.log('⏳ انتظار انتهاء العملية ثم مسح التحديدات...');
                    // حل مؤقت: مسح التحديدات بعد 2 ثانية
                    setTimeout(() => {
                        console.log('🔧 تنفيذ مسح التحديدات (حل مؤقت)...');
                        clearSelectionsAndReload();
                    }, 2000);
                    break;
                case 'ورقة توجيه':
                    if (selectedStudents.length === 0) {
                        showAlert('📋 لم يتم تحديد تلاميذ', 'يرجى تحديد التلاميذ المطلوبين أولاً لطباعة ورقة التوجيه', 'info');
                        return;
                    }
                    guardEngine.buttonGuidanceSheet();
                    showAlert('🖨️ جاري الطباعة', 'يتم الآن طباعة ورقة التوجيه...', 'info');
                    break;
                case 'ورقة استئذان':
                    if (selectedStudents.length === 0) {
                        showAlert('📋 لم يتم تحديد تلاميذ', 'يرجى تحديد التلاميذ المطلوبين أولاً لطباعة ورقة الاستئذان', 'info');
                        return;
                    }
                    guardEngine.buttonPermissionSheet();
                    showAlert('🖨️ جاري الطباعة', 'يتم الآن طباعة ورقة الاستئذان...', 'info');
                    break;
                case 'الرمز السري':
                    if (selectedStudents.length === 0) {
                        showAlert('📋 لم يتم تحديد تلاميذ', 'يرجى تحديد التلاميذ المطلوبين أولاً لطباعة الرموز السرية', 'info');
                        return;
                    }
                    guardEngine.buttonSecretCode();
                    showAlert('🖨️ جاري الطباعة', 'يتم الآن طباعة الرموز السرية...', 'info');
                    break;
                case 'مسك الطلبات':
                    guardEngine.buttonRequestsManagement();
                    showAlert('📋 فتح نافذة', 'يتم الآن فتح نافذة مسك الطلبات...', 'info');
                    break;
                case 'تعليمات':
                    guardEngine.buttonInstructions();
                    showAlert('📖 عرض التعليمات', 'يتم الآن عرض دليل الاستخدام...', 'info');
                    break;
                case 'زيارة الطبيب':
                    if (selectedStudents.length === 0) {
                        showAlert('📋 لم يتم تحديد تلاميذ', 'يرجى تحديد التلاميذ المطلوبين أولاً لطباعة نموذج زيارة الطبيب', 'info');
                        return;
                    }
                    guardEngine.buttonDoctorVisit();
                    showAlert('🖨️ جاري الطباعة', 'يتم الآن طباعة نموذج زيارة الطبيب...', 'info');
                    break;
                case 'تصدير':
                    if (selectedStudents.length === 0) {
                        showAlert('📋 لم يتم تحديد تلاميذ', 'يرجى تحديد التلاميذ المطلوبين أولاً للتصدير', 'info');
                        return;
                    }
                    guardEngine.buttonExport();
                    showAlert('💾 جاري التصدير', 'يتم الآن تصدير بيانات التلاميذ المحددين...', 'info');
                    break;
                case 'بطاقة اللوائح':
                    if (selectedStudents.length === 0) {
                        showAlert('📋 لم يتم تحديد تلاميذ', 'يرجى تحديد التلاميذ المطلوبين أولاً لعرض بطاقة اللوائح', 'info');
                        return;
                    }
                    guardEngine.buttonRegulationsCard();
                    showAlert('📋 عرض البطاقة', 'يتم الآن عرض بطاقة اللوائح للتلاميذ المحددين...', 'info');
                    break;
                case 'تحديث':
                    guardEngine.buttonUpdate();
                    showAlert('🔄 جاري التحديث', 'يتم الآن تحديث البيانات...', 'info');
                    break;
                default:
                    console.log('زر غير معروف:', buttonName);
                    showAlert('⚠️ وظيفة غير متوفرة', 'وظيفة هذا الزر غير متوفرة حالياً', 'warning');
                    break;
            }
        }
        
        // تحديث التاريخ والوقت
        function updateDateTime() {
            const date = document.getElementById('dateInput').value;
            const time = document.getElementById('timeInput').value;
            
            if (date && time && guardEngine) {
                guardEngine.setCustomDateTime(date, time);
            }
        }
        
        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 بدء تهيئة النظام...');
            setTimeout(initializeChannel, 100);
        });
        
        // دالة اختبار لتجربة إشارة dataUpdated يدوياً
        function testDataUpdate() {
            console.log('🧪 اختبار إشارة dataUpdated...');
            if (guardEngine && guardEngine.dataUpdated) {
                // محاولة ربط مؤقت لاختبار الإشارة
                const testHandler = function(data) {
                    console.log('🧪 تم استقبال إشارة اختبار:', data);
                };
                guardEngine.dataUpdated.connect(testHandler);
                console.log('🧪 تم ربط معالج الاختبار');
            } else {
                console.error('🧪 لا يمكن اختبار الإشارة - guardEngine غير متوفر');
            }
        }
        
        // دالة اختبار لمسح التحديدات يدوياً
        function testClearSelections() {
            console.log('🧪 اختبار مسح التحديدات يدوياً...');
            
            if (selectedStudents.length === 0) {
                showAlert('ℹ️ لا توجد تحديدات', 'لا يوجد تلاميذ محددين حالياً للمسح', 'info');
                return;
            }
            
            showAlert('🧪 اختبار المسح', `يتم الآن مسح تحديدات ${selectedStudents.length} تلميذ كاختبار...`, 'info');
            clearSelectionsAndReload();
        }
        
        // دالة لعرض تنبيهات جميلة وأنيقة
        function showAlert(title, message, type = 'info') {
            // إزالة أي تنبيه سابق
            const existingAlert = document.getElementById('customAlert');
            if (existingAlert) {
                existingAlert.remove();
            }
            
            // تحديد الألوان والأيقونات حسب النوع
            let colors = {
                'info': { bg: '#e3f2fd', border: '#2196f3', icon: '📋', iconColor: '#2196f3' },
                'success': { bg: '#e8f5e8', border: '#4caf50', icon: '✅', iconColor: '#4caf50' },
                'warning': { bg: '#fff3e0', border: '#ff9800', icon: '⚠️', iconColor: '#ff9800' },
                'error': { bg: '#ffebee', border: '#f44336', icon: '❌', iconColor: '#f44336' }
            };
            
            const color = colors[type] || colors['info'];
            
            // إنشاء عنصر التنبيه
            const alertDiv = document.createElement('div');
            alertDiv.id = 'customAlert';
            alertDiv.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: ${color.bg};
                border: 3px solid ${color.border};
                border-radius: 15px;
                padding: 25px 30px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                z-index: 10000;
                max-width: 450px;
                min-width: 350px;
                text-align: center;
                font-family: 'Calibri', Arial, sans-serif;
                animation: alertSlideIn 0.3s ease-out;
                backdrop-filter: blur(5px);
            `;
            
            alertDiv.innerHTML = `
                <div style="font-size: 48px; margin-bottom: 15px;">${color.icon}</div>
                <h3 style="
                    color: ${color.iconColor}; 
                    font-size: 22px; 
                    font-weight: bold; 
                    margin-bottom: 15px;
                    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
                ">${title}</h3>
                <p style="
                    color: #333; 
                    font-size: 16px; 
                    line-height: 1.5; 
                    margin-bottom: 20px;
                    font-weight: 500;
                ">${message}</p>
                <button onclick="closeAlert()" style="
                    background: linear-gradient(135deg, ${color.border} 0%, ${color.iconColor} 100%);
                    color: white;
                    border: none;
                    padding: 12px 25px;
                    border-radius: 8px;
                    font-size: 16px;
                    font-weight: bold;
                    cursor: pointer;
                    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
                    transition: all 0.3s ease;
                    font-family: 'Calibri', Arial, sans-serif;
                " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 15px rgba(0, 0, 0, 0.3)';" 
                   onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 10px rgba(0, 0, 0, 0.2)';">
                    موافق
                </button>
            `;
            
            // إضافة خلفية شفافة
            const overlay = document.createElement('div');
            overlay.id = 'alertOverlay';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 9999;
                animation: fadeIn 0.3s ease-out;
            `;
            overlay.onclick = closeAlert;
            
            // إضافة الأنماط للحركة
            if (!document.getElementById('alertStyles')) {
                const style = document.createElement('style');
                style.id = 'alertStyles';
                style.textContent = `
                    @keyframes alertSlideIn {
                        from {
                            opacity: 0;
                            transform: translate(-50%, -60%);
                        }
                        to {
                            opacity: 1;
                            transform: translate(-50%, -50%);
                        }
                    }
                    @keyframes fadeIn {
                        from { opacity: 0; }
                        to { opacity: 1; }
                    }
                `;
                document.head.appendChild(style);
            }
            
            // إضافة العناصر للصفحة
            document.body.appendChild(overlay);
            document.body.appendChild(alertDiv);
            
            // إغلاق تلقائي بعد 5 ثوانِ للتنبيهات العادية
            if (type === 'info' || type === 'warning') {
                setTimeout(closeAlert, 5000);
            }
        }
        
        // دالة إغلاق التنبيه
        function closeAlert() {
            const alert = document.getElementById('customAlert');
            const overlay = document.getElementById('alertOverlay');
            
            if (alert) alert.remove();
            if (overlay) overlay.remove();
        }
        
        // دالة عرض حالة النظام
        function showSystemStatus() {
            console.log('📊 === حالة النظام ===');
            console.log(`selectedStudents.length: ${selectedStudents.length}`);
            console.log(`currentStudents.length: ${currentStudents.length}`);
            console.log(`guardEngine متوفر: ${guardEngine ? 'نعم' : 'لا'}`);
            
            if (guardEngine) {
                console.log(`guardEngine.studentsUpdated متوفر: ${guardEngine.studentsUpdated ? 'نعم' : 'لا'}`);
                console.log(`guardEngine.dataUpdated متوفر: ${guardEngine.dataUpdated ? 'نعم' : 'لا'}`);
            }
            
            const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
            console.log(`مربعات محددة في DOM: ${checkboxes.length}`);
            
            const selectedRows = document.querySelectorAll('tbody tr.selected');
            console.log(`صفوف محددة في DOM: ${selectedRows.length}`);
            
            // عرض معلومات للمستخدم أيضاً
            const statusMessage = `
                التلاميذ المحددون: ${selectedStudents.length}
                إجمالي التلاميذ: ${currentStudents.length}
                مربعات محددة: ${checkboxes.length}
                صفوف محددة: ${selectedRows.length}
            `;
            
            showAlert('📊 حالة النظام', statusMessage, 'info');
        }
    </script>
</body>
</html>"""

def main():
    import sys
    import os
    
    app = QApplication(sys.argv)
    
    # إعداد الترميز
    app.setOrganizationName("SchoolSystem")
    app.setApplicationName("GuardWindow")
    
    window = GuardWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()