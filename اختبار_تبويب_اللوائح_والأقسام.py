#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تبويب اللوائح والأقسام مع sub4_window.py
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon

def test_lists_sections_tab():
    """اختبار تبويب اللوائح والأقسام"""
    print("📋 اختبار تبويب اللوائح والأقسام مع sub4_window.py")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    
    try:
        from main_window import MainWindow
        print("✅ تم استيراد MainWindow")
        
        main_window = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # فحص تبويب اللوائح والأقسام
        def check_lists_sections():
            print("\n🔍 فحص تبويب اللوائح والأقسام:")
            
            # البحث عن التبويب
            lists_sections_found = False
            lists_sections_index = -1
            
            tab_count = main_window.tabWidget.count()
            print(f"📋 عدد التبويبات: {tab_count}")
            
            for i in range(tab_count):
                tab_text = main_window.tabWidget.tabText(i)
                tab_data = main_window.tabWidget.tabData(i)
                print(f"   {i}: {tab_text} ({tab_data})")
                
                if tab_text == "اللوائح والأقسام" or tab_data == "lists_sections":
                    lists_sections_found = True
                    lists_sections_index = i
                    print(f"   ✅ تم العثور على تبويب اللوائح والأقسام في الفهرس {i}")
                    
                    # التحقق من حالة التبويب
                    is_enabled = main_window.tabWidget.isTabEnabled(i)
                    print(f"   📌 حالة التفعيل: {'مفعل' if is_enabled else 'غير مفعل'}")
            
            if not lists_sections_found:
                print("   ❌ لم يتم العثور على تبويب اللوائح والأقسام")
                return
            
            # التحقق من النافذة المرتبطة
            print("\n🔍 فحص النافذة المرتبطة:")
            if "lists_sections" in main_window.windows:
                print("   ✅ النافذة المرتبطة موجودة في قاموس النوافذ")
                window = main_window.windows["lists_sections"]
                print(f"   📄 نوع النافذة: {type(window).__name__}")
                
                # التحقق من خصائص النافذة
                if hasattr(window, 'table_lists'):
                    print("   ✅ تم العثور على table_lists")
                else:
                    print("   ❌ لم يتم العثور على table_lists")
                
                if hasattr(window, 'load_initial_data'):
                    print("   ✅ تم العثور على load_initial_data")
                else:
                    print("   ❌ لم يتم العثور على load_initial_data")
                    
            else:
                print("   ❌ النافذة المرتبطة غير موجودة في قاموس النوافذ")
            
            # اختبار النقر على التبويب
            print("\n🎯 اختبار النقر على التبويب:")
            if lists_sections_index >= 0:
                print(f"   محاولة النقر على التبويب {lists_sections_index}...")
                
                # محاكاة النقر على التبويب
                main_window.tabWidget.setCurrentIndex(lists_sections_index)
                current_index = main_window.tabWidget.currentIndex()
                print(f"   الفهرس الحالي بعد النقر: {current_index}")
                
                if current_index == lists_sections_index:
                    print("   ✅ تم التنقل للتبويب بنجاح!")
                else:
                    print("   ❌ فشل في التنقل للتبويب")
        
        # فحص التبويب بعد ثانية واحدة
        QTimer.singleShot(1000, check_lists_sections)
        
        main_window.show()
        main_window.setWindowTitle("اختبار تبويب اللوائح والأقسام - sub4_window.py")
        
        print("\n📋 تعليمات الاختبار:")
        print("1. ابحث عن تبويب 'اللوائح والأقسام'")
        print("2. تحقق من أنه مفعل (ليس رمادي)")
        print("3. انقر على التبويب")
        print("4. يجب أن تظهر نافذة sub4_window")
        print("5. تحقق من وجود الجداول والأزرار")
        
        print("\n🔧 المواصفات المتوقعة:")
        print("   ✅ تبويب 'اللوائح والأقسام' مفعل")
        print("   ✅ النافذة من نوع Sub4Window")
        print("   ✅ النافذة مدمجة (ليست منفصلة)")
        print("   ✅ تحتوي على table_lists")
        print("   ✅ تحتوي على load_initial_data")
        
        print("\n📁 الملفات المطلوبة:")
        print("   📄 sub4_window.py - الملف الأساسي")
        print("   🏗️ كلاس Sub4Window - الكلاس الرئيسي")
        print("   🗄️ قاعدة البيانات - للبيانات")
        
        print("\n⚠️ ملاحظات:")
        print("   • النافذة يجب أن تكون مدمجة وليست منفصلة")
        print("   • يجب أن تعمل مع قاعدة البيانات الحالية")
        print("   • يجب أن تدعم السنة الدراسية الحالية")
        print("   • يجب أن تكون متجاوبة مع التخطيط")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return 1

def show_integration_details():
    """عرض تفاصيل التكامل"""
    print("\n" + "=" * 60)
    print("🔧 تفاصيل تكامل sub4_window.py")
    print("=" * 60)
    
    integration_steps = [
        ("استيراد الكلاس", "from sub4_window import Sub4Window"),
        ("إنشاء النافذة", "Sub4Window(db=self.db, academic_year=self.current_academic_year, parent=self)"),
        ("تحويل لويدجيت", "setWindowFlags(Qt.Widget)"),
        ("تعيين السياسة", "setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)"),
        ("إخفاء القوائم", "menuBar().setVisible(False), statusBar().setVisible(False)"),
        ("إضافة للقاموس", "self.windows['lists_sections'] = lists_sections_window"),
        ("إضافة للمحتوى", "self.content_area.addWidget(lists_sections_window)")
    ]
    
    print("\n🔧 خطوات التكامل:")
    for step, code in integration_steps:
        print(f"   {step}:")
        print(f"      {code}")
        print()

def show_expected_features():
    """عرض الميزات المتوقعة"""
    print("\n📋 الميزات المتوقعة في sub4_window:")
    print("-" * 40)
    
    features = [
        "جدول البيانات (table_lists)",
        "دالة تحميل البيانات (load_initial_data)",
        "أزرار التحكم والتنقل",
        "دعم قاعدة البيانات",
        "دعم السنة الدراسية",
        "واجهة مستخدم متجاوبة",
        "تخطيط من اليمين لليسار",
        "تنسيق عربي مناسب"
    ]
    
    for feature in features:
        print(f"   • {feature}")

if __name__ == "__main__":
    print("📋 مرحباً بك في اختبار تبويب اللوائح والأقسام!")
    
    # عرض تفاصيل التكامل
    show_integration_details()
    
    # عرض الميزات المتوقعة
    show_expected_features()
    
    print("\n" + "=" * 60)
    print("🚀 بدء تشغيل الاختبار...")
    print("=" * 60)
    
    # تشغيل الاختبار
    exit_code = test_lists_sections_tab()
    
    print(f"\n🔚 انتهى الاختبار بكود الخروج: {exit_code}")
    sys.exit(exit_code)
