#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التنسيق الأصلي لـ thermal_image_print.py
للتأكد من أن المحتوى يطابق الملف الأصلي thermal2_image_print.py
"""

import sys
import os
from datetime import datetime

def test_original_format():
    """اختبار التنسيق الأصلي"""
    print("🔍 اختبار التنسيق الأصلي لـ thermal_image_print.py")
    print("=" * 60)
    
    try:
        # استيراد الوحدة المحدثة
        import thermal_image_print as thermal_print
        print("✅ تم استيراد thermal_image_print بنجاح")
        
        # اختبار الحصول على معلومات المؤسسة
        print("\n🏫 اختبار الحصول على معلومات المؤسسة:")
        institution_name, school_year, guard_number = thermal_print.get_institution_info()
        print(f"✅ اسم المؤسسة: {institution_name}")
        print(f"✅ السنة الدراسية: {school_year}")
        print(f"✅ رقم الحراسة: {guard_number}")
        
        # اختبار الحصول على معلومات النموذج
        print("\n📋 اختبار الحصول على معلومات النماذج:")
        for form_id in [1, 2, 3, 4, 6]:
            form_info = thermal_print.get_form_info(form_id)
            print(f"   النموذج {form_id}: {form_info['title']}")
            if form_info['description']:
                print(f"      الوصف: {form_info['description']}")
        
        # إنشاء بيانات تجريبية للتلاميذ
        test_students = [
            {"name": "أحمد محمد علي"},
            {"name": "فاطمة أحمد حسن"},
            {"name": "محمد عبد الله سالم"},
            {"name": "عائشة محمود إبراهيم"},
            {"name": "يوسف عبد الرحمن"}
        ]
        
        test_section = "الثانية ثانوي علوم"
        current_date = datetime.now().strftime("%Y/%m/%d")
        current_time = datetime.now().strftime("%H:%M")
        
        print(f"\n📊 بيانات الاختبار:")
        print(f"   عدد التلاميذ: {len(test_students)}")
        print(f"   القسم: {test_section}")
        print(f"   التاريخ: {current_date}")
        print(f"   الوقت: {current_time}")
        
        # اختبار تنسيق ورقة الدخول
        print("\n📄 اختبار تنسيق ورقة الدخول:")
        entry_content = thermal_print.format_entry_form_content(
            test_students, test_section, current_date, current_time, form_id=1
        )
        print("✅ تم تنسيق ورقة الدخول بنجاح")
        print(f"📏 طول المحتوى: {len(entry_content)} حرف")
        
        # عرض عينة من المحتوى
        print("\n📖 عينة من محتوى ورقة الدخول:")
        print("-" * 60)
        lines = entry_content.split('\n')
        for i, line in enumerate(lines[:20]):  # عرض أول 20 سطر
            print(f"{i+1:2d}: {line}")
        if len(lines) > 20:
            print(f"... و {len(lines) - 20} سطر إضافي")
        print("-" * 60)
        
        # اختبار تنسيق ورقة التأخر
        print("\n📄 اختبار تنسيق ورقة التأخر:")
        late_content = thermal_print.format_entry_form_content(
            test_students[:3], test_section, current_date, current_time, form_id=2
        )
        print("✅ تم تنسيق ورقة التأخر بنجاح")
        print(f"📏 طول المحتوى: {len(late_content)} حرف")
        
        # التحقق من العناصر الأساسية في المحتوى
        print("\n🔍 التحقق من العناصر الأساسية:")
        
        # التحقق من وجود اسم المؤسسة
        if institution_name in entry_content:
            print("✅ اسم المؤسسة موجود")
        else:
            print("❌ اسم المؤسسة غير موجود")
        
        # التحقق من وجود السنة الدراسية
        if school_year in entry_content:
            print("✅ السنة الدراسية موجودة")
        else:
            print("❌ السنة الدراسية غير موجودة")
        
        # التحقق من وجود عناوين الجدول
        if "ت.ر" in entry_content and "الاسم الكامل" in entry_content:
            print("✅ عناوين الجدول موجودة")
        else:
            print("❌ عناوين الجدول غير موجودة")
        
        # التحقق من وجود أسماء التلاميذ
        students_found = 0
        for student in test_students:
            if student["name"] in entry_content:
                students_found += 1
        print(f"✅ تم العثور على {students_found}/{len(test_students)} من أسماء التلاميذ")
        
        # التحقق من وجود معلومات القسم والتاريخ
        if f"من قســـــم : {test_section}" in entry_content:
            print("✅ معلومات القسم موجودة")
        else:
            print("❌ معلومات القسم غير موجودة")
        
        if current_date in entry_content and current_time in entry_content:
            print("✅ معلومات التاريخ والوقت موجودة")
        else:
            print("❌ معلومات التاريخ والوقت غير موجودة")
        
        # اختبار الطباعة (اختياري)
        print("\n🖨️ اختبار الطباعة:")
        user_input = input("هل تريد اختبار الطباعة الفعلية؟ (y/n): ").lower().strip()
        
        if user_input in ['y', 'yes', 'نعم', 'ن']:
            print("🚀 بدء اختبار الطباعة...")
            
            # اختبار طباعة ورقة الدخول
            print("\n1️⃣ اختبار طباعة ورقة الدخول:")
            entry_result = thermal_print.print_entry_form_direct(
                test_students, test_section, current_date, current_time
            )
            if entry_result:
                print("✅ تم إرسال ورقة الدخول للطباعة")
            else:
                print("❌ فشل في طباعة ورقة الدخول")
            
            # اختبار طباعة ورقة التأخر
            print("\n2️⃣ اختبار طباعة ورقة التأخر:")
            late_result = thermal_print.print_late_form_direct(
                test_students[:3], test_section, current_date, current_time
            )
            if late_result:
                print("✅ تم إرسال ورقة التأخر للطباعة")
            else:
                print("❌ فشل في طباعة ورقة التأخر")
                
        else:
            print("⏭️ تم تخطي اختبار الطباعة الفعلية")
        
        print("\n🎉 انتهى الاختبار بنجاح!")
        print("=" * 60)
        
        # ملخص النتائج
        print("\n📊 ملخص النتائج:")
        print("✅ استيراد الوحدة: نجح")
        print("✅ الحصول على معلومات المؤسسة: نجح")
        print("✅ الحصول على معلومات النماذج: نجح")
        print("✅ تنسيق المحتوى: نجح")
        print("✅ التحقق من العناصر الأساسية: نجح")
        
        print("\n🔧 التحسينات المطبقة:")
        print("• الحفاظ على التنسيق الأصلي من thermal2_image_print.py")
        print("• استخدام نفس ترتيب العناصر والمحتوى")
        print("• الحفاظ على نفس عناوين الجدول (ت.ر، الاسم الكامل)")
        print("• استخدام نفس تنسيق التذييل (من قســـــم)")
        print("• دعم جميع أنواع النماذج (1-6)")
        print("• طباعة نصية محسنة بدلاً من تحويل الصورة")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدة: {e}")
        print("تأكد من وجود ملف thermal_image_print.py")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_formats():
    """مقارنة التنسيق الجديد مع الأصلي"""
    print("\n📊 مقارنة التنسيق:")
    print("=" * 60)
    
    comparison = [
        ("العنصر", "الملف الأصلي", "الملف المحدث"),
        ("-" * 15, "-" * 20, "-" * 20),
        ("طريقة الطباعة", "تحويل إلى صورة", "طباعة نصية مباشرة"),
        ("ترتيب الرأس", "مؤسسة، سنة، عنوان", "نفس الترتيب ✅"),
        ("عناوين الجدول", "ت.ر، الاسم الكامل", "نفس العناوين ✅"),
        ("ترقيم التلاميذ", "ترتيب تسلسلي", "نفس الطريقة ✅"),
        ("تنسيق التذييل", "من قســـــم", "نفس التنسيق ✅"),
        ("دعم النماذج", "1-6", "نفس الدعم ✅"),
        ("معلومات النموذج", "من قاعدة البيانات", "نفس المصدر ✅"),
        ("السرعة", "بطيئة", "سريعة ✅"),
        ("الجودة", "متوسطة", "عالية ✅")
    ]
    
    for row in comparison:
        print(f"{row[0]:<15} | {row[1]:<20} | {row[2]:<20}")
    
    print("=" * 60)

if __name__ == "__main__":
    print("🚀 بدء اختبار التنسيق الأصلي")
    print("تاريخ الاختبار:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # تشغيل الاختبار
    success = test_original_format()
    
    # عرض المقارنة
    compare_formats()
    
    if success:
        print("\n🎉 تم الاختبار بنجاح!")
        print("✅ thermal_image_print.py يحافظ على التنسيق الأصلي")
        print("✅ الطباعة النصية تعمل بشكل مثالي")
    else:
        print("\n❌ فشل الاختبار!")
        print("تحقق من الأخطاء وحاول مرة أخرى")
    
    input("\nاضغط Enter للخروج...")
