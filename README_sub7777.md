# نافذة البحث الحديثة - sub7777_window.py

## نظرة عامة
تم تحويل ملف `sub4_window.py` من PyQt5 إلى منهجية Python + HTML الحديثة باستخدام Flask. هذا التحويل يوفر واجهة ويب حديثة ومتجاوبة تعمل في المتصفح.

## الميزات الجديدة

### 🎨 واجهة المستخدم
- **تصميم حديث**: واجهة ويب أنيقة باستخدام HTML5/CSS3
- **متجاوبة**: تعمل على جميع الأجهزة (كمبيوتر، تابلت، هاتف)
- **سهولة الاستخدام**: تفاعل سلس مع JavaScript

### 📊 إدارة البيانات
- **بحث متقدم**: تصفية حسب المستوى والقسم
- **عرض تفاصيل**: نوافذ منبثقة لعرض بيانات التلاميذ
- **تحديد متعدد**: اختيار عدة تلاميذ للعمليات المختلفة

### 📤 التصدير والطباعة
- **تصدير Excel**: تصدير البيانات مع تنسيق احترافي
- **طباعة النماذج**: دعم جميع أنواع النماذج
- **خيارات مرنة**: اختيار الأعمدة المراد تصديرها

## متطلبات التشغيل

### المكتبات المطلوبة
```bash
pip install flask pandas openpyxl
```

### الملفات المطلوبة
- `data.db`: قاعدة البيانات الأساسية
- `01.ico`: أيقونة البرنامج (اختيارية)

## طريقة التشغيل

### 1. التشغيل المباشر
```bash
python sub7777_window.py
```

### 2. التشغيل من كود Python
```python
from sub7777_window import Sub7777Window

# إنشاء النافذة
window = Sub7777Window()

# تشغيل التطبيق
window.run(host='127.0.0.1', port=5000)
```

### 3. التشغيل مع معاملات مخصصة
```python
from sub7777_window import create_sub7777_window

# إنشاء النافذة مع معاملات مخصصة
window = create_sub7777_window(
    db_path="path/to/data.db",
    academic_year="2024-2025"
)

# تشغيل على منفذ مختلف
window.run(host='0.0.0.0', port=8080)
```

## هيكل الملفات

```
project/
├── sub7777_window.py          # الملف الرئيسي
├── data.db                    # قاعدة البيانات
├── 01.ico                     # أيقونة البرنامج
├── templates/
│   └── index.html            # القالب الرئيسي
├── static/
│   ├── css/
│   │   └── style.css         # ملف التنسيق
│   ├── js/
│   │   └── script.js         # ملف JavaScript
│   └── images/
│       └── 01.ico           # أيقونة البرنامج
```

## الوظائف المتوفرة

### 🔍 البحث والتصفية
- عرض جميع المستويات مع عدد التلاميذ
- عرض الأقسام لكل مستوى
- تصفية التلاميذ حسب المستوى والقسم

### 👥 إدارة التلاميذ
- عرض قائمة التلاميذ في جدول منسق
- النقر على الرمز لعرض التفاصيل الكاملة
- تحديد/إلغاء تحديد التلاميذ

### 📋 النماذج والطباعة
- ورقة الدخول
- ورقة التأخر
- ورقة التوجيه
- ورقة الاستئذان
- زيارة الطبيب
- الرمز السري

### 📊 التصدير
- تصدير إلى Excel مع تنسيق احترافي
- اختيار الأعمدة المراد تصديرها
- إضافة شعار المؤسسة والبيانات الأساسية

## المقارنة مع النسخة القديمة

| الميزة | PyQt5 (القديم) | Flask (الجديد) |
|--------|----------------|----------------|
| نوع الواجهة | تطبيق سطح المكتب | تطبيق ويب |
| التوافق | ويندوز فقط | جميع المنصات |
| سهولة التطوير | معقد | بسيط |
| التحديث | يتطلب إعادة تثبيت | تحديث فوري |
| الوصول | محلي فقط | محلي أو عبر الشبكة |
| التصميم | ثابت | متجاوب |

## نصائح الاستخدام

### 🚀 الأداء
- استخدم المتصفحات الحديثة للحصول على أفضل أداء
- تأكد من وجود اتصال جيد بقاعدة البيانات

### 🔒 الأمان
- لا تشغل التطبيق على شبكة عامة بدون حماية
- استخدم HTTPS في البيئات الإنتاجية

### 🛠️ التخصيص
- يمكن تعديل ملف CSS لتغيير الألوان والتصميم
- يمكن إضافة وظائف جديدة في ملف JavaScript

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في قاعدة البيانات
```
خطأ: لم يتم العثور على قاعدة البيانات
الحل: تأكد من وجود ملف data.db في نفس مجلد البرنامج
```

#### 2. خطأ في المنفذ
```
خطأ: المنفذ 5000 مستخدم
الحل: غير المنفذ في كود التشغيل
```

#### 3. خطأ في المكتبات
```
خطأ: ModuleNotFoundError
الحل: pip install flask pandas openpyxl
```

## التطوير المستقبلي

### ميزات مخططة
- [ ] نظام المصادقة والأمان
- [ ] تقارير متقدمة
- [ ] إشعارات فورية
- [ ] نسخ احتياطية تلقائية
- [ ] واجهة إدارة المستخدمين

### تحسينات مقترحة
- [ ] تحسين الأداء للبيانات الكبيرة
- [ ] دعم قواعد بيانات متعددة
- [ ] واجهة برمجة تطبيقات (API) كاملة
- [ ] تطبيق هاتف محمول

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. تحقق من ملف README هذا
2. راجع رسائل الخطأ في وحدة التحكم
3. تأكد من تثبيت جميع المتطلبات

---

**ملاحظة**: هذا الملف يحافظ على جميع وظائف الملف الأصلي `sub4_window.py` مع تحسينات كبيرة في الواجهة والأداء.
