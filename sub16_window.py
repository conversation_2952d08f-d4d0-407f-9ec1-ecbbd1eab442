import os
import sys
import sqlite3
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QFrame, QLabel, QLineEdit,
                           QPushButton, QVBoxLayout, QHBoxLayout, QWidget, QTextEdit,
                           QGraphicsDropShadowEffect, QMessageBox, QDateEdit, QTimeEdit,
                           QGridLayout, QDesktopWidget, QComboBox) # <-- إضافة QComboBox
# استيراد وحدة الرسائل المخصصة
from custom_messages import show_custom_message
from PyQt5.QtGui import QFont, QColor, QIcon, QPalette
from PyQt5.QtCore import Qt, QSize, QDate, QTime

class ParentVisitWindow(QMainWindow):
    def __init__(self, parent=None):
        super().__init__(parent)

        # تعيين النافذة كنافذة مشروطة (Modal)
        self.setWindowModality(Qt.ApplicationModal)

        # تعيين أعلام النافذة لإزالة أزرار التصغير والتكبير والإغلاق الافتراضية
        self.setWindowFlags(Qt.Window | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        # تعيين عنوان النافذة والأبعاد
        self.setWindowTitle("")  # تم إزالة العنوان "توثيق زيارة أولياء الأمور"
        self.setGeometry(100, 50, 1200, 580)  # تم تعديل العرض إلى 650 بدلاً من 980

        # إنشاء الواجهة
        self.initUI()

        # --- >> إضافة: توسيط النافذة << ---
        self.center_window()
        # --- >> نهاية الإضافة << ---

    def initUI(self):
        # إنشاء الويدجت المركزي مع خلفية متدرجة
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # تعيين مؤشر اليد للنافذة بأكملها
        self.setCursor(Qt.ArrowCursor)  # إعادة تعيين المؤشر الافتراضي للنافذة

        # تنسيق الخلفية بتدرج لطيف
        central_widget.setStyleSheet("""
            background-color: qlineargradient(
                x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #f8f9fa, stop: 1 #e9ecef
            );
        """)

        # تخطيط رئيسي مع هوامش متناسقة
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(12, 8, 12, 12)
        main_layout.setSpacing(10)

        # ---- إنشاء العنوان الرئيسي بتصميم أنيق ----
        header_frame = QFrame()
        header_frame.setStyleSheet("background-color: #3a6ea5; border-radius: 5px;")
        header_frame.setFixedHeight(40)
        header_frame.setCursor(Qt.ArrowCursor)  # إعادة تعيين المؤشر الافتراضي للإطار

        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(8, 0, 8, 0)

        title_label = QLabel("توثيق زيارة جديدة")  # Cambiado el título a "توثيق زيارة جديدة" (documentación de nueva visita)
        title_label.setFont(QFont("Calibri", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: white;")
        title_label.setCursor(Qt.ArrowCursor)  # إعادة تعيين المؤشر الافتراضي للعنوان

        self.close_button = QPushButton("×")
        self.close_button.setFont(QFont("Arial", 14, QFont.Bold))
        self.close_button.setFixedSize(28, 28)
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: white;
                border: none;
                border-radius: 14px; /* جعل الزر دائري */
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.3); /* تفتيح اللون عند المرور */
            }
            QPushButton:pressed {
                background-color: rgba(0, 0, 0, 0.2); /* تغميق اللون عند الضغط */
            }
        """)
        self.close_button.setCursor(Qt.PointingHandCursor)
        self.close_button.clicked.connect(self.close)

        header_layout.addWidget(title_label)
        header_layout.addWidget(self.close_button)

        main_layout.addWidget(header_frame)

        # ---- إنشاء منطقة المحتوى الرئيسية ----
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 5px;
                border: 1px solid #ddd;
            }
        """)

        # إضافة ظل خفيف للمحتوى
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setOffset(0, 2)
        shadow.setColor(QColor(0, 0, 0, 30))
        content_frame.setGraphicsEffect(shadow)

        content_layout = QGridLayout(content_frame)
        content_layout.setContentsMargins(15, 15, 15, 15)
        content_layout.setVerticalSpacing(8)
        content_layout.setHorizontalSpacing(8)

        # ---- تنسيق الخطوط والأنماط ----
        title_font = QFont("Calibri", 13, QFont.Bold)
        label_font = QFont("Calibri", 13, QFont.Bold)
        field_font = QFont("Calibri", 13)

        # أنماط العناصر المختلفة
        title_style = "color: #3a6ea5; font-weight: bold; border-bottom: 1px solid #3a6ea5; padding-bottom: 3px; margin-bottom: 5px;"
        label_style = """
            QLabel {
                color: #444;
                padding: 2px;
                font-weight: bold;
            }
        """

        readonly_field_style = """
            QLineEdit {
                background-color: #f8f9fa;
                border: 1px solid #dce0e3;
                border-radius: 4px;
                padding: 4px;
                color: #444;
            }
        """

        editable_field_style = """
            QLineEdit, QDateEdit, QTimeEdit {
                background-color: white;
                border: 1px solid #dce0e3;
                border-radius: 4px;
                padding: 4px;
            }
            QLineEdit:focus, QDateEdit:focus, QTimeEdit:focus {
                border: 1px solid #3a6ea5;
            }
        """

        # ---- إنشاء عناوين الأقسام ----
        student_info_title = QLabel("معلومات التلميذ")
        student_info_title.setFont(title_font)
        student_info_title.setStyleSheet(title_style)
        student_info_title.setCursor(Qt.ArrowCursor)  # إعادة تعيين المؤشر الافتراضي للعنوان
        content_layout.addWidget(student_info_title, 0, 0, 1, 2)

        visit_info_title = QLabel("معلومات الزيارة")
        visit_info_title.setFont(title_font)
        visit_info_title.setStyleSheet(title_style)
        visit_info_title.setCursor(Qt.ArrowCursor)  # إعادة تعيين المؤشر الافتراضي للعنوان
        content_layout.addWidget(visit_info_title, 0, 2, 1, 2)

        # ---- إضافة حقول معلومات التلميذ ----
        field_pairs = [
            ("المستوى:", "level_edit", 1),
            ("القسم:", "class_edit", 2),
            ("الرمز:", "code_edit", 3),
            ("رت:", "rt_edit", 4),
            ("الاسم والنسب:", "name_edit", 5)
        ]

        for label_text, field_name, row in field_pairs:
            label = QLabel(label_text)
            label.setFont(label_font)
            label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
            label.setStyleSheet(label_style)
            label.setCursor(Qt.ArrowCursor)  # إعادة تعيين المؤشر الافتراضي للتسميات

            field = QLineEdit()
            field.setFont(field_font)
            field.setReadOnly(True)
            field.setStyleSheet(readonly_field_style)
            field.setCursor(Qt.PointingHandCursor)

            setattr(self, field_name, field)
            content_layout.addWidget(label, row, 0)
            content_layout.addWidget(field, row, 1)

        # ---- إضافة حقول معلومات الزيارة ----
        # اسم الولي
        parent_name_label = QLabel("اسم الولي:")
        parent_name_label.setFont(label_font)
        parent_name_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        parent_name_label.setStyleSheet(label_style)
        parent_name_label.setCursor(Qt.ArrowCursor)  # إعادة تعيين المؤشر الافتراضي للتسمية

        self.parent_name_edit = QLineEdit()
        self.parent_name_edit.setFont(field_font)
        self.parent_name_edit.setStyleSheet(editable_field_style)
        self.parent_name_edit.setCursor(Qt.PointingHandCursor)

        content_layout.addWidget(parent_name_label, 1, 2)
        content_layout.addWidget(self.parent_name_edit, 1, 3)

        # رقم البطاقة
        id_card_label = QLabel("رقم البطاقة:")
        id_card_label.setFont(label_font)
        id_card_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        id_card_label.setStyleSheet(label_style)
        id_card_label.setCursor(Qt.ArrowCursor)  # إعادة تعيين المؤشر الافتراضي للتسمية

        self.id_card_edit = QLineEdit()
        self.id_card_edit.setFont(field_font)
        self.id_card_edit.setInputMethodHints(Qt.ImhLatinOnly | Qt.ImhUppercaseOnly)
        self.id_card_edit.setStyleSheet(editable_field_style)
        self.id_card_edit.setCursor(Qt.PointingHandCursor)
        # تغيير لغة لوحة المفاتيح إلى الفرنسية عند التركيز وإعادتها إلى العربية عند إزالة التركيز
        self.id_card_edit.focusInEvent = lambda event: self.on_id_card_focus_in(event)
        self.id_card_edit.focusOutEvent = lambda event: self.on_id_card_focus_out(event)

        content_layout.addWidget(id_card_label, 2, 2)
        content_layout.addWidget(self.id_card_edit, 2, 3)

        # وقت الزيارة
        visit_time_label = QLabel("وقت الزيارة:")
        visit_time_label.setFont(label_font)
        visit_time_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        visit_time_label.setStyleSheet(label_style)
        visit_time_label.setCursor(Qt.ArrowCursor)  # إعادة تعيين المؤشر الافتراضي للتسمية

        self.visit_time_edit = QTimeEdit()
        self.visit_time_edit.setFont(field_font)
        self.visit_time_edit.setTime(QTime.currentTime())
        self.visit_time_edit.setDisplayFormat("hh:mm")
        self.visit_time_edit.setStyleSheet(editable_field_style)
        self.visit_time_edit.setCursor(Qt.PointingHandCursor)

        content_layout.addWidget(visit_time_label, 3, 2)
        content_layout.addWidget(self.visit_time_edit, 3, 3)

        # تاريخ الزيارة
        visit_date_label = QLabel("تاريخ الزيارة:")
        visit_date_label.setFont(label_font)
        visit_date_label.setStyleSheet(label_style)
        visit_date_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        visit_date_label.setCursor(Qt.ArrowCursor)  # إعادة تعيين المؤشر الافتراضي للتسمية

        self.visit_date_edit = QDateEdit()
        self.visit_date_edit.setFont(field_font)
        self.visit_date_edit.setFixedHeight(28)
        self.visit_date_edit.setCalendarPopup(True)
        self.visit_date_edit.setDate(QDate.currentDate())
        self.visit_date_edit.setDisplayFormat("dd/MM/yyyy")
        self.visit_date_edit.setStyleSheet(editable_field_style)
        self.visit_date_edit.setCursor(Qt.PointingHandCursor)

        content_layout.addWidget(visit_date_label, 4, 2)
        content_layout.addWidget(self.visit_date_edit, 4, 3)

        # --- >> تعديل: سبب الزيارة (استخدام QComboBox) << ---
        reason_label = QLabel("سبب الزيارة:")
        reason_label.setFont(label_font)
        reason_label.setStyleSheet(label_style)
        reason_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        reason_label.setCursor(Qt.ArrowCursor)  # إعادة تعيين المؤشر الافتراضي للتسمية

        self.reason_combo = QComboBox() # <-- تغيير إلى QComboBox
        self.reason_combo.setFont(field_font)
        self.reason_combo.setEditable(True) # <-- جعل القائمة قابلة للتعديل
        self.reason_combo.setStyleSheet(editable_field_style)
        self.reason_combo.setMinimumHeight(28) # <-- تحديد الارتفاع
        self.reason_combo.setCursor(Qt.PointingHandCursor)

        # --- >> تعديل: تحديث قائمة الأسباب << ---
        self.reasons_map = {
            "": "", # سبب فارغ
            "متابعة الوضع الدراسي": "حضر ولي الأمر قصد متابعة الوضع الدراسي لابنه/ابنته والتعرف على مستواه التعليمي داخل الفصل.",
            "مناقشة سلوك التلميذ": "جاء ولي الأمر لمناقشة سلوكيات ابنه/ابنته داخل المؤسسة ومعالجة بعض التصرفات التربوية.",
            "استلام النتائج": "زار ولي الأمر المؤسسة لاستلام نتائج الفروض أو التقارير الفصلية الخاصة بابنه/ابنته.",
            "المشاركة في مجلس المؤسسة": "حضر ولي الأمر بناءً على دعوة المؤسسة للمشاركة في أشغال مجلس المؤسسة التربوي.",
            "طلب شهادة مدرسية": "تقدم ولي الأمر بطلب الحصول على شهادة مدرسية أو وثيقة إدارية تخص ابنه/ابنته.",
            "التواصل مع الأطر التربوية": "زار ولي الأمر المؤسسة للتواصل مع الأستاذ/الإدارة بخصوص بعض الملاحظات أو الاستفسارات.",
            "الإبلاغ عن ظروف خاصة": "أبلغ ولي الأمر المؤسسة بوضعية اجتماعية أو صحية تخص ابنه/ابنته قد تؤثر على تحصيله الدراسي.",
            "مواكبة الأنشطة التربوية": "جاء ولي الأمر لمتابعة أو دعم نشاط تربوي أو ثقافي تنخرط فيه المؤسسة.",
            "تقديم شكاية أو اقتراح": "زار ولي الأمر المؤسسة من أجل تقديم شكاية أو اقتراح يهم الشأن التربوي والتعليم.",
            # إضافة الأسباب السابقة التي لم يتم ربطها بمضمون محدد
            "مناقشة سلوك التلميذ داخل القسم أو المؤسسة.": "", # يمكن إضافة مضمون لاحقاً
            "استلام النتائج والتقارير الدورية.": "", # يمكن إضافة مضمون لاحقاً
            "المشاركة في مجالس المؤسسة مثل مجلس القسم أو مجلس التدبير.": "", # يمكن إضافة مضمون لاحقاً
            "طلب شهادة مدرسية أو وثائق إدارية.": "", # يمكن إضافة مضمون لاحقاً
            "التواصل مع الأساتذة أو الإدارة بشأن ملاحظات أو مشاكل.": "", # يمكن إضافة مضمون لاحقاً
            "الإبلاغ عن ظروف خاصة تؤثر على التلميذ (مرض، انتقال…).": "", # يمكن إضافة مضمون لاحقاً
            "مواكبة مشاريع المؤسسة أو الأنشطة التربوية.": "", # يمكن إضافة مضمون لاحقاً
            "تقديم شكاوى أو اقتراحات تخص الشأن التربوي.": "", # يمكن إضافة مضمون لاحقاً
            "المساهمة في تتبع الغياب والتأخرات وضبط الانضباط.": "" # يمكن إضافة مضمون لاحقاً
        }
        # إزالة التكرارات والحفاظ على الترتيب
        unique_reasons = list(dict.fromkeys(self.reasons_map.keys()))
        self.reason_combo.addItems(unique_reasons)
        # --- >> نهاية التعديل << ---

        # جعل النص المخصص يظهر في البداية إذا كان الحقل قابلاً للتعديل
        self.reason_combo.lineEdit().setPlaceholderText("اختر أو أدخل سبب الزيارة...")
        self.reason_combo.lineEdit().setCursor(Qt.PointingHandCursor)  # تعيين مؤشر اليد للحقل النصي في القائمة المنسدلة
        self.reason_combo.setCurrentIndex(0) # تحديد الخيار الفارغ كافتراضي

        # --- >> إضافة: ربط تغيير السبب بتحديث المضمون << ---
        self.reason_combo.currentIndexChanged.connect(self.update_content_suggestion)
        # --- >> نهاية الإضافة << ---

        content_layout.addWidget(reason_label, 5, 2)
        content_layout.addWidget(self.reason_combo, 5, 3) # <-- استخدام reason_combo
        # --- >> نهاية التعديل << ---

        # مضمون الزيارة
        content_label = QLabel("مضمون الزيارة:")
        content_label.setFont(label_font)
        content_label.setAlignment(Qt.AlignRight | Qt.AlignTop)  # محاذاة لأعلى بدل المنتصف
        content_label.setStyleSheet(label_style)
        content_label.setCursor(Qt.ArrowCursor)  # إعادة تعيين المؤشر الافتراضي للتسمية

        self.content_edit = QTextEdit()
        self.content_edit.setFont(field_font)
        self.content_edit.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: 1px solid #dce0e3;
                border-radius: 4px;
                padding: 4px;
            }
            QTextEdit:focus {
                border: 1px solid #3a6ea5;
            }
        """)
        self.content_edit.setCursor(Qt.PointingHandCursor)

        content_layout.addWidget(content_label, 6, 2)
        content_layout.addWidget(self.content_edit, 6, 3)

        # إضافة إطار المحتوى إلى التخطيط الرئيسي
        main_layout.addWidget(content_frame)

        # ---- إضافة شريط الأزرار ----
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("background-color: transparent;")

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(0, 5, 0, 0)
        buttons_layout.setSpacing(10) # إضافة تباعد بين الأزرار

        # --- >> تعديل: تحسين أنماط الأزرار << ---
        button_style = """
            QPushButton {{
                padding: 8px 20px; /* زيادة الحشو */
                border-radius: 5px; /* حواف دائرية أكثر */
                font-size: 11pt; /* حجم خط مناسب */
                min-height: 32px; /* ارتفاع ثابت */
                color: {text_color}; /* لون النص دائما مرئي */
                background-color: {bg_color}; /* لون الخلفية دائما مرئي */
                border: 1px solid {border_color}; /* حدود واضحة */
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:pressed {{
                background-color: {pressed_color};
            }}
        """

        self.clear_button = QPushButton("مسح البيانات")
        self.clear_button.setFont(QFont("Calibri", 11, QFont.Bold)) # جعل الخط غامق
        self.clear_button.setStyleSheet(button_style.format(
            text_color="#333333",
            bg_color="#f0f0f0",
            border_color="#ccc",
            hover_color="#e0e0e0",
            pressed_color="#cccccc"))
        self.clear_button.setIcon(QIcon.fromTheme("edit-clear"))
        self.clear_button.setIconSize(QSize(18, 18)) # حجم الأيقونة
        self.clear_button.setCursor(Qt.PointingHandCursor)
        self.clear_button.clicked.connect(self.clear_fields)

        self.save_button = QPushButton("حفظ الزيارة")
        self.save_button.setFont(QFont("Calibri", 13, QFont.Bold))
        self.save_button.setStyleSheet(button_style.format(
            text_color="#ffffff",
            bg_color="#3498db",
            border_color="#2980b9",
            hover_color="#2c5b8e",
            pressed_color="#1e4a75"))
        self.save_button.setIcon(QIcon.fromTheme("document-save"))
        self.save_button.setIconSize(QSize(18, 18)) # حجم الأيقونة
        self.save_button.setCursor(Qt.PointingHandCursor)
        self.save_button.clicked.connect(self.save_visit_data)
        # --- >> نهاية التعديل << ---

        buttons_layout.addWidget(self.clear_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_button)

        main_layout.addWidget(buttons_frame)

    def save_visit_data(self):
        """حفظ بيانات زيارة ولي الأمر في قاعدة البيانات"""
        try:
            # التحقق من وجود البيانات الأساسية
            if not self.code_edit.text() or not self.parent_name_edit.text():
                show_custom_message(self, "الرجاء إدخال رمز التلميذ واسم الولي على الأقل", "تنبيه", "warning")
                return

            # جمع البيانات من الحقول
            student_code = self.code_edit.text()
            student_name = self.name_edit.text()
            student_level = self.level_edit.text()
            student_class = self.class_edit.text()
            student_rt = self.rt_edit.text()

            parent_name = self.parent_name_edit.text()
            id_card = self.id_card_edit.text()
            visit_date = self.visit_date_edit.date().toString("yyyy-MM-dd")
            visit_time = self.visit_time_edit.time().toString("hh:mm")
            # --- >> تعديل: الحصول على النص من QComboBox << ---
            reason = self.reason_combo.currentText() # <-- استخدام currentText()
            # --- >> نهاية التعديل << ---
            content = self.content_edit.toPlainText()

            # اتصال بقاعدة البيانات
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()

            # استعلام للحصول على السنة الدراسية والأسدس من جدول بيانات_المؤسسة
            cursor.execute("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
            school_data = cursor.fetchone()

            # التحقق من وجود بيانات السنة الدراسية والأسدس
            if not school_data:
                show_custom_message(
                    self,
                    "لم يتم العثور على بيانات السنة الدراسية والأسدس في جدول بيانات_المؤسسة",
                    "تنبيه",
                    "warning"
                )
                conn.close()
                return

            # استخراج السنة الدراسية والأسدس
            school_year = school_data[0]
            semester = school_data[1]

            # التحقق مما إذا كان الجدول موجودًا وما هي الأعمدة الموجودة
            cursor.execute("PRAGMA table_info(زيارة_ولي_الأمر)")
            columns = [col[1] for col in cursor.fetchall()]

            if not columns:
                # إنشاء الجدول إذا لم يكن موجودًا
                cursor.execute('''
                    CREATE TABLE زيارة_ولي_الأمر (
                        الرقم INTEGER PRIMARY KEY AUTOINCREMENT,
                        الرمز TEXT,
                        اسم_التلميذ TEXT,
                        المستوى TEXT,
                        القسم TEXT,
                        رت TEXT,
                        اسم_الولي TEXT,
                        رقم_البطاقة TEXT,
                        تاريخ_الزيارة TEXT,
                        وقت_الزيارة TEXT,
                        سبب_الزيارة TEXT,
                        مضمون_الزيارة TEXT,
                        السنة_الدراسية TEXT,
                        الأسدس TEXT,
                        تاريخ_التسجيل TEXT
                    )
                ''')
                # تحديث قائمة الأعمدة بعد إنشاء الجدول
                columns = ["الرقم", "الرمز", "اسم_التلميذ", "المستوى", "القسم", "رت",
                          "اسم_الولي", "رقم_البطاقة", "تاريخ_الزيارة", "وقت_الزيارة",
                          "سبب_الزيارة", "مضمون_الزيارة", "السنة_الدراسية", "الأسدس", "تاريخ_التسجيل"]
            else:
                # إذا كان الجدول موجودًا ولكن ينقصه عمود "اسم_التلميذ"، أضف العمود
                if "اسم_التلميذ" not in columns:
                    try:
                        cursor.execute('ALTER TABLE زيارة_ولي_الأمر ADD COLUMN اسم_التلميذ TEXT')
                        print("تمت إضافة عمود 'اسم_التلميذ' إلى جدول زيارة_ولي_الأمر")
                    except:
                        print("فشلت محاولة إضافة عمود 'اسم_التلميذ'")

                # التحقق من وجود الأعمدة الأخرى المطلوبة وإضافتها إذا لزم الأمر
                required_columns = ["المستوى", "القسم", "رت", "اسم_الولي", "رقم_البطاقة",
                                   "تاريخ_الزيارة", "وقت_الزيارة", "سبب_الزيارة",
                                   "مضمون_الزيارة", "السنة_الدراسية", "الأسدس", "تاريخ_التسجيل"]

                for col in required_columns:
                    if col not in columns:
                        try:
                            cursor.execute(f'ALTER TABLE زيارة_ولي_الأمر ADD COLUMN "{col}" TEXT')
                            print(f"تمت إضافة عمود '{col}' إلى جدول زيارة_ولي_الأمر")
                        except:
                            print(f"فشلت محاولة إضافة عمود '{col}'")

            # استخدام الأعمدة المتوفرة فقط في عملية الإدخال
            column_list = []
            value_list = []

            # إعداد قائمة الأعمدة والقيم المقابلة لها
            col_value_mapping = {
                "الرمز": student_code,
                "اسم_التلميذ": student_name,
                "المستوى": student_level,
                "القسم": student_class,
                "رت": student_rt,
                "اسم_الولي": parent_name,
                "رقم_البطاقة": id_card,
                "تاريخ_الزيارة": visit_date,
                "وقت_الزيارة": visit_time,
                "سبب_الزيارة": reason, # <-- استخدام المتغير reason المحدث
                "مضمون_الزيارة": content,
                "السنة_الدراسية": school_year,
                "الأسدس": semester,
                "تاريخ_التسجيل": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            # إضافة الأعمدة والقيم المتوافقة فقط
            for col, val in col_value_mapping.items():
                if col in columns:
                    column_list.append(col)
                    value_list.append(val)

            # بناء استعلام ديناميكي
            sql_columns = ", ".join([f'"{col}"' for col in column_list])
            sql_placeholders = ", ".join(["?" for _ in column_list])

            # إدخال البيانات في الجدول
            sql_query = f'''
                INSERT INTO زيارة_ولي_الأمر (
                    {sql_columns}
                ) VALUES ({sql_placeholders})
            '''

            cursor.execute(sql_query, value_list)

            # حفظ التغييرات
            conn.commit()

            # عرض رسالة نجاح مخصصة مع سؤال المستخدم عن طباعة الزيارة
            reply = show_custom_message(
                self,
                "تم حفظ بيانات زيارة ولي الأمر بنجاح. هل ترغب في طباعة ورقة الزيارة الآن؟",
                "تم الحفظ بنجاح",
                "question"
            )

            # إذا اختار المستخدم نعم، قم بطباعة الزيارة
            if reply:
                # الحصول على معرف الزيارة المضافة حديثاً
                try:
                    cursor.execute("SELECT MAX(الرقم) FROM زيارة_ولي_الأمر")
                    visit_id = cursor.fetchone()[0]
                except:
                    # محاولة بديلة باستخدام last_insert_rowid()
                    cursor.execute("SELECT last_insert_rowid()")
                    visit_id = cursor.fetchone()[0]

                # طباعة ورقة الزيارة باستخدام print7.py
                try:
                    from print7 import print_parent_visit_sheet
                    print_parent_visit_sheet(visit_id)
                except ImportError:
                    show_custom_message(
                        self,
                        "لم يتم العثور على ملف print7.py. يرجى التأكد من وجود الملف في نفس المجلد.",
                        "خطأ",
                        "error"
                    )
                except Exception as print_error:
                    show_custom_message(
                        self,
                        f"حدث خطأ أثناء محاولة طباعة ورقة الزيارة:\n{str(print_error)}",
                        "خطأ في الطباعة",
                        "error"
                    )

            # مسح حقول الزيارة مع الاحتفاظ ببيانات التلميذ
            self.clear_visit_fields()

            # إغلاق الاتصال بقاعدة البيانات
            conn.close()

        except Exception as e:
            show_custom_message(
                self,
                f"حدث خطأ أثناء حفظ البيانات:\n{str(e)}",
                "خطأ في الحفظ",
                "error"
            )
            print(f"خطأ في حفظ زيارة ولي الأمر: {e}")
            import traceback
            traceback.print_exc()
            if 'conn' in locals() and conn:
                conn.close()

    def clear_fields(self):
        """مسح جميع الحقول"""
        # استخدام رسالة مخصصة للتأكيد
        reply = show_custom_message(
            self,
            "هل تريد مسح جميع البيانات؟",
            "تأكيد المسح",
            "question"
        )

        if reply:
            # مسح بيانات التلميذ
            self.code_edit.clear()
            self.rt_edit.clear()
            self.name_edit.clear()
            self.level_edit.clear()
            self.class_edit.clear()

            # مسح بيانات الزيارة
            self.clear_visit_fields()

            # عرض رسالة نجاح المسح
            show_custom_message(
                self,
                "تم مسح جميع البيانات بنجاح",
                "تم المسح",
                "success"
            )

    def clear_visit_fields(self):
        """مسح حقول بيانات الزيارة فقط"""
        self.parent_name_edit.clear()
        self.id_card_edit.clear()
        self.visit_date_edit.setDate(QDate.currentDate())
        self.visit_time_edit.setTime(QTime.currentTime())
        self.reason_combo.setCurrentIndex(0) # العودة إلى الخيار الفارغ
        self.reason_combo.lineEdit().clear() # مسح النص المخصص إن وجد
        self.content_edit.clear() # <-- مسح حقل المضمون أيضاً

    def set_student_info(self, code, name, id_num, level, class_name):
        """تعيين معلومات الطالب برمجياً"""
        if not code:
            return False

        try:
            # تعيين الرمز والبيانات الأخرى
            self.code_edit.setText(code)
            self.name_edit.setText(name)
            self.rt_edit.setText(id_num)
            self.level_edit.setText(level)
            self.class_edit.setText(class_name)

            # التركيز على حقل اسم الولي
            self.parent_name_edit.setFocus()

            return True
        except Exception as e:
            print(f"خطأ في تعيين معلومات الطالب: {e}")
            return False

    # --- >> إضافة: دالة تحديث المضمون المقترح << ---
    def update_content_suggestion(self, index):
        """تحديث حقل المضمون بناءً على السبب المختار."""
        selected_reason = self.reason_combo.itemText(index)
        suggested_content = self.reasons_map.get(selected_reason, "") # الحصول على المضمون المقابل

        # فقط قم بتحديث المضمون إذا كان الحقل فارغاً أو يحتوي على اقتراح سابق
        current_content = self.content_edit.toPlainText().strip()
        is_previous_suggestion = any(current_content == suggestion for suggestion in self.reasons_map.values() if suggestion)

        if suggested_content and (not current_content or is_previous_suggestion):
            self.content_edit.setPlainText(suggested_content)
        # إذا اختار المستخدم سبباً بدون اقتراح، لا تمسح المضمون الحالي
        elif not suggested_content and is_previous_suggestion:
             self.content_edit.clear() # مسح الاقتراح السابق إذا اختار سبباً بدون اقتراح
    # --- >> نهاية الإضافة << ---

    # دالة معالجة التركيز على حقل رقم البطاقة
    def on_id_card_focus_in(self, event):
        """تغيير لغة لوحة المفاتيح إلى الفرنسية بأحرف كبيرة عند التركيز على حقل رقم البطاقة"""
        # استدعاء الدالة الأصلية للتركيز
        QLineEdit.focusInEvent(self.id_card_edit, event)

        # تعيين وضع الإدخال للأحرف اللاتينية الكبيرة فقط
        self.id_card_edit.setInputMethodHints(Qt.ImhLatinOnly | Qt.ImhUppercaseOnly)

        # تحويل النص الحالي إلى أحرف كبيرة إذا كان موجوداً
        current_text = self.id_card_edit.text()
        if current_text:
            self.id_card_edit.setText(current_text.upper())

        # تغيير لغة لوحة المفاتيح إلى الفرنسية
        try:
            # استخدام أمر النظام لتغيير لغة لوحة المفاتيح إلى الفرنسية
            # هذا الأمر يعمل على نظام Windows فقط
            if sys.platform == "win32":
                import ctypes
                # 0x040C هو رمز اللغة الفرنسية في Windows
                # HWND_BROADCAST = 0xFFFF
                # WM_INPUTLANGCHANGEREQUEST = 0x0050

                # استخدام PostMessageW بدلاً من SendMessageW لتجنب تجميد الواجهة
                result = ctypes.windll.user32.PostMessageW(0xFFFF, 0x0050, 0, 0x040C)

                if result:
                    print("تم تغيير لغة لوحة المفاتيح إلى الفرنسية بنجاح")
                else:
                    print("فشل في تغيير لغة لوحة المفاتيح إلى الفرنسية")
                    # محاولة بديلة باستخدام LoadKeyboardLayout
                    try:
                        # 0x040C هو رمز اللغة الفرنسية في Windows
                        # KLF_ACTIVATE = 0x00000001
                        french_hkl = ctypes.windll.user32.LoadKeyboardLayoutW("0000040C", 0x00000001)
                        if french_hkl:
                            print("تم تحميل لوحة المفاتيح الفرنسية باستخدام LoadKeyboardLayout")
                    except Exception as load_error:
                        print(f"فشل في تحميل لوحة المفاتيح الفرنسية: {load_error}")
        except Exception as e:
            print(f"خطأ في تغيير لغة لوحة المفاتيح: {e}")

    # دالة معالجة إزالة التركيز من حقل رقم البطاقة
    def on_id_card_focus_out(self, event):
        """إعادة لغة لوحة المفاتيح إلى العربية عند إزالة التركيز من حقل رقم البطاقة"""
        # استدعاء الدالة الأصلية لإزالة التركيز
        QLineEdit.focusOutEvent(self.id_card_edit, event)

        # تغيير لغة لوحة المفاتيح إلى العربية
        try:
            # استخدام أمر النظام لتغيير لغة لوحة المفاتيح إلى العربية
            # هذا الأمر يعمل على نظام Windows فقط
            if sys.platform == "win32":
                import ctypes
                # 0x0401 هو رمز اللغة العربية في Windows
                # HWND_BROADCAST = 0xFFFF
                # WM_INPUTLANGCHANGEREQUEST = 0x0050

                # استخدام PostMessageW بدلاً من SendMessageW لتجنب تجميد الواجهة
                result = ctypes.windll.user32.PostMessageW(0xFFFF, 0x0050, 0, 0x0401)

                if result:
                    print("تم إعادة لغة لوحة المفاتيح إلى العربية بنجاح")
                else:
                    print("فشل في إعادة لغة لوحة المفاتيح إلى العربية")
                    # محاولة بديلة باستخدام LoadKeyboardLayout
                    try:
                        # 0x0401 هو رمز اللغة العربية في Windows
                        # KLF_ACTIVATE = 0x00000001
                        arabic_hkl = ctypes.windll.user32.LoadKeyboardLayoutW("00000401", 0x00000001)
                        if arabic_hkl:
                            print("تم تحميل لوحة المفاتيح العربية باستخدام LoadKeyboardLayout")
                    except Exception as load_error:
                        print(f"فشل في تحميل لوحة المفاتيح العربية: {load_error}")
        except Exception as e:
            print(f"خطأ في تغيير لغة لوحة المفاتيح: {e}")

    # --- >> إضافة: دالة توسيط النافذة << ---
    def center_window(self):
        """توسيط النافذة على الشاشة."""
        try:
            qr = self.frameGeometry()
            cp = QDesktopWidget().availableGeometry().center()
            qr.moveCenter(cp)
            self.move(qr.topLeft())
        except Exception as e:
            print(f"خطأ في توسيط النافذة: {e}")
    # --- >> نهاية الإضافة << ---

if __name__ == "__main__":
    app = QApplication([])
    app.setLayoutDirection(Qt.RightToLeft)  # RTL for Arabic language
    window = ParentVisitWindow()
    window.show()
    app.exec_()
