@echo off
chcp 65001 >nul
title نافذة البحث الحديثة - برنامج المعين في الحراسة العامة

echo.
echo ================================================================
echo 🚀 تشغيل نافذة البحث الحديثة
echo    برنامج المعين في الحراسة العامة
echo ================================================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo 💡 يرجى تثبيت Python من: https://python.org
    echo.
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM التحقق من وجود الملفات المطلوبة
if not exist "sub7777_window.py" (
    echo ❌ ملف sub7777_window.py غير موجود
    echo 💡 تأكد من وجود الملف في نفس المجلد
    echo.
    pause
    exit /b 1
)

echo ✅ ملف التطبيق موجود

REM تثبيت المكتبات المطلوبة
echo.
echo 📦 فحص وتثبيت المكتبات المطلوبة...
pip install flask pandas openpyxl --quiet --disable-pip-version-check

if errorlevel 1 (
    echo ⚠️  تحذير: قد تكون هناك مشكلة في تثبيت المكتبات
    echo 💡 سيتم المتابعة مع المحاولة...
)

echo.
echo 🌐 بدء تشغيل التطبيق...
echo.

REM تشغيل التطبيق
python run_sub7777.py

echo.
echo 👋 انتهى التطبيق
pause
