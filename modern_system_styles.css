/*
ملف الأنماط الموحد للنظام التعليمي الحديث
يحتوي على جميع الأنماط المشتركة بين الوحدات
*/

/* إعدادات أساسية */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Aria<PERSON>', sans-serif;
    font-size: 14px;
    font-weight: bold;
    background: #f8f9fa;
    color: #333;
    min-height: 100vh;
    direction: rtl;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
}

/* الحاوي الرئيسي */
.container {
    max-width: 95%;
    margin: 20px auto;
    display: grid;
    gap: 20px;
    height: calc(100vh - 40px);
}

/* شريط العنوان الموحد */
.header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.header h1 {
    color: #667eea;
    font-size: 24px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.header p {
    color: #666;
    font-size: 14px;
    opacity: 0.9;
}

/* اللوحات الموحدة */
.panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.panel-title {
    color: #667eea;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* الأزرار الموحدة */
.btn {
    border: none;
    border-radius: 8px;
    padding: 12px 20px;
    font-size: 13px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #2980b9, #1f618d);
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #27ae60, #229954);
    transform: translateY(-2px);
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e67e22, #d35400);
    transform: translateY(-2px);
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #d62c1a, #a93226);
    transform: translateY(-2px);
}

/* البطاقات الموحدة */
.card {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    transition: all 0.3s ease;
}

.card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.card-title {
    font-size: 16px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 8px;
}

.card-description {
    font-size: 13px;
    color: #6c757d;
    margin-bottom: 15px;
    line-height: 1.4;
}

/* الشبكات الموحدة */
.grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.grid-3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.grid-4 {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

.grid-auto {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

/* السجلات الموحدة */
.log-container {
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    padding: 15px;
    height: 300px;
    overflow-y: auto;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.6;
}

.log-entry {
    margin-bottom: 8px;
    padding: 5px 10px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.log-entry:hover {
    background: rgba(255, 255, 255, 0.5);
    transform: translateX(5px);
}

.log-entry.info {
    background: rgba(52, 152, 219, 0.1);
    border-left: 4px solid #3498db;
}

.log-entry.success {
    background: rgba(46, 204, 113, 0.1);
    border-left: 4px solid #2ecc71;
}

.log-entry.error {
    background: rgba(231, 76, 60, 0.1);
    border-left: 4px solid #e74c3c;
}

.log-entry.warning {
    background: rgba(243, 156, 18, 0.1);
    border-left: 4px solid #f39c12;
}

.log-entry.progress {
    background: rgba(155, 89, 182, 0.1);
    border-left: 4px solid #9b59b6;
}

.log-timestamp {
    color: #7f8c8d;
    margin-left: auto;
    font-size: 11px;
}

/* شريط التقدم الموحد */
.progress-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.98);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    min-width: 400px;
    z-index: 1000;
    display: none;
}

.progress-bar {
    width: 100%;
    height: 25px;
    background: #ecf0f1;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 15px;
    box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.1);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.4s ease;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    text-align: center;
    color: #2c3e50;
    font-weight: bold;
    font-size: 14px;
}

/* الإحصائيات الموحدة */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.stat-item {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.stat-label {
    font-size: 12px;
    opacity: 0.9;
    font-weight: 600;
}

/* معلومات النظام */
.system-info {
    background: #e8f4fd;
    border: 1px solid #bee5eb;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 13px;
}

.info-label {
    font-weight: bold;
    color: #2c3e50;
}

.info-value {
    color: #34495e;
}

/* تأثيرات التحميل */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #667eea;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تأثيرات الرسوم المتحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.float {
    animation: float 6s ease-in-out infinite;
}

/* ألوان إضافية للتنويع */
.color-blue { background: linear-gradient(135deg, #3498db, #2980b9); }
.color-green { background: linear-gradient(135deg, #2ecc71, #27ae60); }
.color-orange { background: linear-gradient(135deg, #f39c12, #e67e22); }
.color-red { background: linear-gradient(135deg, #e74c3c, #c0392b); }
.color-purple { background: linear-gradient(135deg, #9b59b6, #8e44ad); }
.color-teal { background: linear-gradient(135deg, #1abc9c, #16a085); }

/* أنماط خاصة بنظام SPA */

/* شريط التنقل العلوي */
.navbar {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
    color: white;
    padding: 15px 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.navbar-brand {
    font-size: 24px;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 10px;
}

.navbar-nav {
    display: flex;
    gap: 5px;
    list-style: none;
}

.nav-item {
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    overflow: hidden;
}

.nav-link {
    display: block;
    padding: 10px 15px;
    color: white;
    text-decoration: none;
    font-size: 13px;
    font-weight: bold;
    cursor: pointer;
}

.nav-link:hover, .nav-link.active {
    background: rgba(255,255,255,0.2);
    color: white;
}

.nav-link.active {
    background: rgba(255,255,255,0.3);
}

/* شريط الحالة */
.status-bar {
    background: #e9ecef;
    padding: 10px 20px;
    font-size: 12px;
    color: #6c757d;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #28a745;
}

/* منطقة المحتوى */
.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px 20px;
    min-height: calc(100vh - 120px);
}

/* الصفحات */
.page {
    display: none;
    opacity: 0;
    transform: translateY(20px);
}

.page.active {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

/* قسم الترحيب */
.welcome-section {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 60px 40px;
    border-radius: 20px;
    text-align: center;
    margin-bottom: 40px;
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
}

.welcome-section h1 {
    font-size: 32px;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.welcome-section p {
    font-size: 18px;
    opacity: 0.9;
    line-height: 1.6;
}

/* بطاقات الوحدات */
.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.module-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
}

.module-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    border-color: #667eea;
}

.module-icon {
    font-size: 48px;
    margin-bottom: 20px;
    display: block;
}

.module-title {
    font-size: 22px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 15px;
}

.module-description {
    color: #7f8c8d;
    line-height: 1.6;
    margin-bottom: 20px;
}

.module-status {
    background: #e8f5e8;
    color: #4caf50;
    padding: 8px 15px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
    display: inline-block;
}

/* إحصائيات النظام */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border-left: 4px solid #667eea;
}

.stat-value {
    font-size: 32px;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 8px;
}

.stat-label {
    color: #7f8c8d;
    font-size: 14px;
}

/* أزرار الاستيراد */
.import-buttons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.import-action-button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 20px;
    border-radius: 12px;
    cursor: pointer;
    font-weight: bold;
    font-size: 16px;
    transition: all 0.3s ease;
}

.import-action-button:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

/* شريط التقدم للاستيراد */
.import-progress-container {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.import-progress-text {
    text-align: center;
    color: #495057;
    font-weight: bold;
    margin-bottom: 10px;
}

.import-progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.import-progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

.import-progress-percentage {
    text-align: center;
    color: #6c757d;
    font-size: 12px;
}

/* إحصائيات الاستيراد */
.import-stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.import-stat-item {
    background: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 3px 10px rgba(0,0,0,0.05);
}

.import-stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.import-stat-label {
    color: #6c757d;
    font-size: 12px;
}

/* مربع المعلومات */
.info-box {
    background: #e8f4fd;
    border: 1px solid #bee5eb;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.info-box h4 {
    color: #0c5460;
    margin-bottom: 10px;
}

.info-box p {
    color: #495057;
    margin-bottom: 8px;
    line-height: 1.5;
}

/* تمييز المعلومات */
.highlight {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    margin: 20px 0;
}

.highlight h3, .highlight h4 {
    margin-bottom: 10px;
}

/* صفحة الوحدة */
.module-page {
    max-width: 100%;
}

.page-header {
    text-align: center;
    margin-bottom: 40px;
}

.page-header h2 {
    color: #2c3e50;
    font-size: 28px;
    margin-bottom: 10px;
}

.page-header p {
    color: #6c757d;
    font-size: 16px;
}

/* أزرار التحكم */
.control-buttons {
    text-align: center;
    margin-top: 30px;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
}

/* صفحة الإعدادات */
.settings-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.settings-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.settings-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

/* صفحة المؤسسة */
.institution-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.institution-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 30px;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

.logo-display {
    width: 120px;
    height: 120px;
    border: 2px dashed #667eea;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    flex-shrink: 0;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.logo-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.institution-form {
    margin-bottom: 30px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
}

.form-group input,
.form-group select {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
}

/* التجاوب مع الشاشات الصغيرة */
@media (max-width: 768px) {
    .grid-2, .grid-3, .grid-4 {
        grid-template-columns: 1fr;
    }
    
    .container {
        margin: 10px;
        height: calc(100vh - 20px);
    }
    
    .header h1 {
        font-size: 20px;
    }
    
    .panel {
        padding: 15px;
    }
    
    .progress-container {
        min-width: 300px;
        padding: 20px;
    }
    
    .navbar-container {
        flex-direction: column;
        gap: 10px;
    }
    
    .navbar-nav {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .main-content {
        padding: 20px 10px;
    }
    
    .welcome-section {
        padding: 40px 20px;
    }
    
    .welcome-section h1 {
        font-size: 24px;
    }
    
    .logo-section {
        flex-direction: column;
        text-align: center;
    }
    
    .control-buttons {
        flex-direction: column;
        align-items: center;
    }
}
