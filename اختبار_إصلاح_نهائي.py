#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الإصلاح النهائي لمشكلة sub4_window
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon

def test_final_fix():
    """اختبار الإصلاح النهائي"""
    print("🔧 اختبار الإصلاح النهائي لمشكلة sub4_window")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    
    try:
        from main_window import MainWindow
        print("✅ تم استيراد MainWindow")
        
        main_window = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # فحص الإصلاح النهائي
        def check_final_fix():
            print("\n🔍 فحص الإصلاح النهائي:")
            
            # التحقق من اتصالات قاعدة البيانات
            print("\n📊 فحص اتصالات قاعدة البيانات:")
            
            if hasattr(main_window, 'qsql_db'):
                print(f"   ✅ QSqlDatabase موجود: {type(main_window.qsql_db)}")
                if main_window.qsql_db.isOpen():
                    print("   ✅ QSqlDatabase متصل")
                else:
                    print("   ❌ QSqlDatabase غير متصل")
            else:
                print("   ❌ QSqlDatabase غير موجود")
            
            # التحقق من تبويب اللوائح والأقسام
            print("\n📋 فحص تبويب اللوائح والأقسام:")
            
            if "lists_sections" in main_window.windows:
                window = main_window.windows["lists_sections"]
                print(f"   📄 نوع النافذة: {type(window).__name__}")
                
                if "Sub4Window" in type(window).__name__:
                    print("   ✅ النافذة من النوع الصحيح (Sub4Window)")
                    
                    # التحقق من نوع قاعدة البيانات
                    if hasattr(window, 'db'):
                        print(f"   📊 نوع قاعدة البيانات: {type(window.db)}")
                        
                        if "QSqlDatabase" in str(type(window.db)):
                            print("   ✅ النافذة تستخدم QSqlDatabase")
                            
                            # اختبار دالة check_regulations_table
                            try:
                                print("   🧪 اختبار check_regulations_table...")
                                result = window.check_regulations_table()
                                print(f"   ✅ check_regulations_table تعمل: {result}")
                            except Exception as e:
                                print(f"   ❌ خطأ في check_regulations_table: {e}")
                            
                            # اختبار تحميل البيانات
                            try:
                                print("   🧪 اختبار تحميل البيانات...")
                                window.load_initial_data()
                                print("   ✅ تحميل البيانات يعمل")
                            except Exception as e:
                                print(f"   ❌ خطأ في تحميل البيانات: {e}")
                                
                        else:
                            print(f"   ❌ نوع قاعدة البيانات خاطئ: {type(window.db)}")
                    else:
                        print("   ❌ النافذة لا تحتوي على قاعدة بيانات")
                        
                else:
                    print(f"   ❌ نوع النافذة خاطئ: {type(window).__name__}")
                    
            else:
                print("   ❌ النافذة غير موجودة")
            
            # اختبار النقر على التبويب
            print("\n🎯 اختبار النقر على التبويب:")
            
            # البحث عن التبويب
            lists_sections_index = -1
            tab_count = main_window.tabWidget.count()
            
            for i in range(tab_count):
                tab_text = main_window.tabWidget.tabText(i)
                tab_data = main_window.tabWidget.tabData(i)
                
                if tab_text == "اللوائح والأقسام" or tab_data == "lists_sections":
                    lists_sections_index = i
                    print(f"   ✅ تم العثور على التبويب في الفهرس {i}")
                    break
            
            if lists_sections_index >= 0:
                print("   🎯 محاولة النقر على التبويب...")
                
                # محاكاة النقر على التبويب
                main_window.tabWidget.setCurrentIndex(lists_sections_index)
                current_index = main_window.tabWidget.currentIndex()
                
                if current_index == lists_sections_index:
                    print("   ✅ تم التنقل للتبويب بنجاح!")
                    
                    # التحقق من ظهور البيانات
                    current_widget = main_window.tabWidget.currentWidget()
                    if current_widget:
                        print("   ✅ النافذة معروضة")
                        
                        # البحث عن الجداول
                        if hasattr(current_widget, 'findChild'):
                            from PyQt5.QtWidgets import QTableView
                            tables = current_widget.findChildren(QTableView)
                            print(f"   📊 عدد الجداول الموجودة: {len(tables)}")
                            
                            for i, table in enumerate(tables):
                                if table.model():
                                    row_count = table.model().rowCount()
                                    print(f"   📋 الجدول {i+1}: {row_count} صف")
                                else:
                                    print(f"   📋 الجدول {i+1}: لا يحتوي على نموذج")
                        else:
                            print("   ⚠️ لا يمكن البحث عن الجداول")
                    else:
                        print("   ❌ النافذة غير معروضة")
                else:
                    print("   ❌ فشل في التنقل للتبويب")
            else:
                print("   ❌ لم يتم العثور على التبويب")
        
        # فحص الإصلاح بعد ثانية واحدة
        QTimer.singleShot(1000, check_final_fix)
        
        main_window.show()
        main_window.setWindowTitle("اختبار الإصلاح النهائي - sub4_window")
        
        print("\n📋 تعليمات الاختبار:")
        print("1. راقب رسائل وحدة التحكم")
        print("2. تحقق من عدم وجود رسائل خطأ 'cursor'")
        print("3. انقر على تبويب 'اللوائح والأقسام'")
        print("4. تحقق من ظهور الجداول والبيانات")
        print("5. جرب تحديد مستوى وقسم")
        
        print("\n🔧 الإصلاحات المطبقة:")
        print("   ✅ إضافة QSqlDatabase في main_window")
        print("   ✅ تمرير QSqlDatabase لـ sub4_window")
        print("   ✅ إصلاح check_regulations_table لاستخدام QSqlQuery")
        print("   ✅ إصلاح update_levels_model لاستخدام QSqlQuery")
        print("   ✅ إزالة استدعاءات cursor() مع QSqlDatabase")
        
        print("\n📊 النتائج المتوقعة:")
        print("   ✅ لا توجد رسائل خطأ عن 'cursor'")
        print("   ✅ النافذة من نوع Sub4Window")
        print("   ✅ النافذة تستخدم QSqlDatabase")
        print("   ✅ check_regulations_table تعمل")
        print("   ✅ تحميل البيانات يعمل")
        print("   ✅ الجداول تظهر البيانات")
        
        print("\n🎉 إذا نجح الاختبار:")
        print("   ✅ sub4_window يعمل بكامل وظائفه مدمجاً")
        print("   ✅ جميع الأخطاء تم إصلاحها")
        print("   ✅ النافذة تعمل كما في الملف الأصلي")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return 1

def show_complete_fix_summary():
    """عرض ملخص الإصلاح الكامل"""
    print("\n" + "=" * 60)
    print("🔧 ملخص الإصلاح الكامل")
    print("=" * 60)
    
    print("\n🎯 المشكلة الأصلية:")
    print("   • sub4_window يعمل منفصلاً لكن لا يعمل مدمجاً")
    print("   • رسائل خطأ: 'QSqlDatabase' object has no attribute 'cursor'")
    print("   • عدم ظهور البيانات في النافذة المدمجة")
    
    print("\n🔧 الحلول المطبقة:")
    print("   1. إضافة QSqlDatabase في main_window")
    print("   2. تمرير QSqlDatabase بدلاً من sqlite3.Connection")
    print("   3. إصلاح check_regulations_table لاستخدام QSqlQuery")
    print("   4. إصلاح update_levels_model لاستخدام QSqlQuery")
    print("   5. إزالة استدعاءات cursor() مع QSqlDatabase")
    
    print("\n✅ النتيجة النهائية:")
    print("   • sub4_window يعمل بكامل وظائفه مدمجاً")
    print("   • لا توجد رسائل خطأ")
    print("   • البيانات تظهر بشكل صحيح")
    print("   • جميع الوظائف تعمل كما في الملف الأصلي")

if __name__ == "__main__":
    print("🔧 مرحباً بك في الاختبار النهائي!")
    
    # عرض ملخص الإصلاح الكامل
    show_complete_fix_summary()
    
    print("\n" + "=" * 60)
    print("🚀 بدء تشغيل الاختبار النهائي...")
    print("=" * 60)
    
    # تشغيل الاختبار
    exit_code = test_final_fix()
    
    print(f"\n🔚 انتهى الاختبار بكود الخروج: {exit_code}")
    sys.exit(exit_code)
