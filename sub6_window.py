# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from PyQt5.QtWidgets import (
    QApplication, QWidget, QTableWidget, QTableWidgetItem, QVBoxLayout,
    QLabel, QFrame, QGraphicsDropShadowEffect, QHeaderView, QAbstractItemView,
    QMessageBox, QHBoxLayout # لاستخدام رسائل الخطأ والتخطيط الأفقي
)
from PyQt5.QtGui import QColor, QFont, QPalette
from PyQt5.QtCore import Qt, QRect

# اسم الملف: sub6_window.py
# اسم قاعدة البيانات
DATABASE_NAME = "data.db"
TABLE_NAME = "تعديل_المسميات" # اسم الجدول باللغة العربية كما هو مطلوب

def setup_database():
    """
    تتأكد من وجود قاعدة البيانات والجدول، وتنشئهما إذا لم يكونا موجودين.
    **لا تضيف بيانات افتراضية.**
    """
    db_path = os.path.join(os.path.dirname(__file__), DATABASE_NAME)
    conn = None
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # إنشاء الجدول إذا لم يكن موجودًا فقط
        cursor.execute(f'''
            CREATE TABLE IF NOT EXISTS "{TABLE_NAME}" (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                الاسم TEXT NOT NULL,
                العنوان TEXT,
                ملاحظات TEXT
            )
        ''')
        conn.commit()

    except sqlite3.Error as e:
        print(f"خطأ في قاعدة البيانات: {e}")
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setText(f"حدث خطأ أثناء إعداد أو الوصول إلى قاعدة البيانات:\n{e}")
        msg_box.setWindowTitle("خطأ في قاعدة البيانات")
        msg_box.exec_()
    finally:
        if conn:
            conn.close()

class Sub6Window(QWidget):
    def __init__(self):
        super().__init__()
        setup_database()
        self.db_path = os.path.join(os.path.dirname(__file__), DATABASE_NAME)
        self._loading_data = False

        self.header_font = QFont('Calibri', 13)
        self.header_font.setBold(True)
        self.cell_font = QFont('Calibri', 13)
        self.cell_font.setBold(True)

        # تصحيح عنوان النافذة ليتطابق مع اسم الملف والفئة
        self.setWindowTitle("Sub6 Window - الأوراق والملاحظات")
         # الحفاظ على حجم النافذة الكلية
        self.setGeometry(100, 100, 1200, 800)

        palette = self.palette()
        background_color = QColor("#B0E0E6")
        palette.setColor(QPalette.Window, background_color)
        self.setPalette(palette)
        self.setAutoFillBackground(True)

        self.initUI()
        self.load_all_tables_data()

    def initUI(self):
        # --- إطار المحتوى والظل ---
        self.content_frame = QFrame(self)
        # --- تقليل الهامش الخارجي ---
        frame_margin = 15 # <<<--- تقليل الهامش الخارجي (مثلاً: 15)
        frame_width = self.width() - 2 * frame_margin
        frame_height = self.height() - 2 * frame_margin
        self.content_frame.setGeometry(frame_margin, frame_margin, frame_width, frame_height)
        self.content_frame.setStyleSheet("background-color: white; border-radius: 8px;")

        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setXOffset(5)
        shadow.setYOffset(5)
        shadow.setColor(QColor(0, 0, 0, 150))
        self.content_frame.setGraphicsEffect(shadow)

        # --- عرض الجدول المحدد (الحفاظ على 900 أو تعديله إذا لزم الأمر) ---
        fixed_table_width = 900

        # --- أعمدة الجدول الأول ---
        col1_t1_width = 150
        col2_t1_width = 150
        # تعديل العمود الثالث بناءً على العرض الكلي ومساحة الشريط
        scrollbar_allowance = 20
        col3_t1_width = fixed_table_width - col1_t1_width - col2_t1_width - scrollbar_allowance
        if col3_t1_width < 100: col3_t1_width = 100

        # --- الجدول الأول (ID 1-6) ---
        self.table1 = QTableWidget(self.content_frame)
        # --- تقليل الهامش العلوي الداخلي ---
        table1_top_margin = 10 # <<<--- تقليل الهامش العلوي (مثلاً: 10)
        table1_y_pos = table1_top_margin
        table1_height = 40 + 6 * 25 + 5
        # --- تعديل حساب X لاستخدام frame_width المحدثة ---
        table1_x = int((frame_width - fixed_table_width) / 2)
        self.table1.setGeometry(table1_x, table1_y_pos, fixed_table_width, table1_height)
        self.table1.setFixedWidth(fixed_table_width)

        self.table1.setLayoutDirection(Qt.RightToLeft)
        self.table1.setColumnCount(3)
        self.table1.setHorizontalHeaderLabels(['الاسم', 'العنوان', 'ملاحظات'])

        header1 = self.table1.horizontalHeader()
        header1.setFont(self.header_font)
        header1.setFixedHeight(40)
        header1.setStyleSheet("""
            QHeaderView::section {
                background-color: #1976d2; color: white; font-size: 14px;
                border-radius: 4px; padding: 8px; border: 1px solid #1976d2;
            }
        """)
        self.table1.setColumnWidth(0, col1_t1_width)
        self.table1.setColumnWidth(1, col2_t1_width)
        self.table1.setColumnWidth(2, col3_t1_width)

        self.table1.verticalHeader().setVisible(False)
        self.table1.setSelectionBehavior(QAbstractItemView.SelectItems)
        self.table1.cellChanged.connect(self.handle_cell_changed)

        # --- تعديل: استبدال الجدول الثاني بجدولين متقابلين ---
        # إنشاء إطار للجدولين المتقابلين
        tables_frame = QFrame(self.content_frame)
        tables_frame_y = table1_y_pos + table1_height + 15  # مسافة بعد الجدول الأول
        tables_frame_height = 40 + 10 * 25 + 5 + 20  # ارتفاع كافٍ لاستيعاب الجدولين مع هوامش
        tables_frame.setGeometry(table1_x, tables_frame_y, fixed_table_width, tables_frame_height)
        
        # إنشاء تخطيط أفقي للجدولين
        tables_layout = QHBoxLayout(tables_frame)
        tables_layout.setContentsMargins(0, 0, 0, 0)
        tables_layout.setSpacing(10)  # مسافة بين الجدولين
        
        # --- الجدول الثاني (المخالفات: ID 7-16) ---
        self.violations_table = QTableWidget()
        self.violations_table.setLayoutDirection(Qt.RightToLeft)
        self.violations_table.setColumnCount(2)
        self.violations_table.setHorizontalHeaderLabels(['الاسم', 'العنوان'])
        
        # تعيين عنوان للجدول
        violations_title = QLabel("المخالفات")
        violations_title.setFont(QFont('Calibri', 14, QFont.Bold))
        violations_title.setAlignment(Qt.AlignCenter)
        violations_title.setStyleSheet("color: #0d47a1; margin-bottom: 5px;")
        
        # إنشاء تخطيط عمودي لعنوان الجدول والجدول
        violations_layout = QVBoxLayout()
        violations_layout.addWidget(violations_title)
        violations_layout.addWidget(self.violations_table)
        
        violations_header = self.violations_table.horizontalHeader()
        violations_header.setFont(self.header_font)
        violations_header.setFixedHeight(40)
        violations_header.setStyleSheet("""
            QHeaderView::section {
                background-color: #1976d2; color: white; font-size: 14px;
                border-radius: 4px; padding: 8px; border: 1px solid #1976d2;
            }
        """)
        
        # تعيين عرض الأعمدة
        violations_col1_width = 120
        violations_col2_width = (fixed_table_width // 2) - 120 - scrollbar_allowance - 5
        
        self.violations_table.setColumnWidth(0, violations_col1_width)
        self.violations_table.setColumnWidth(1, violations_col2_width)
        
        self.violations_table.verticalHeader().setVisible(False)
        self.violations_table.setSelectionBehavior(QAbstractItemView.SelectItems)
        self.violations_table.cellChanged.connect(self.handle_cell_changed)

        # --- الجدول الثالث (الإجراءات: ID 17-20) ---
        self.procedures_table = QTableWidget()
        self.procedures_table.setLayoutDirection(Qt.RightToLeft)
        self.procedures_table.setColumnCount(2)
        self.procedures_table.setHorizontalHeaderLabels(['الاسم', 'العنوان'])
        
        # تعيين عنوان للجدول
        procedures_title = QLabel("الإجراءات")
        procedures_title.setFont(QFont('Calibri', 14, QFont.Bold))
        procedures_title.setAlignment(Qt.AlignCenter)
        procedures_title.setStyleSheet("color: #0d47a1; margin-bottom: 5px;")
        
        # إنشاء تخطيط عمودي لعنوان الجدول والجدول
        procedures_layout = QVBoxLayout()
        procedures_layout.addWidget(procedures_title)
        procedures_layout.addWidget(self.procedures_table)
        
        procedures_header = self.procedures_table.horizontalHeader()
        procedures_header.setFont(self.header_font)
        procedures_header.setFixedHeight(40)
        procedures_header.setStyleSheet("""
            QHeaderView::section {
                background-color: #1976d2; color: white; font-size: 14px;
                border-radius: 4px; padding: 8px; border: 1px solid #1976d2;
            }
        """)
        
        # تعيين عرض الأعمدة
        procedures_col1_width = 120
        procedures_col2_width = (fixed_table_width // 2) - 120 - scrollbar_allowance - 5
        
        self.procedures_table.setColumnWidth(0, procedures_col1_width)
        self.procedures_table.setColumnWidth(1, procedures_col2_width)
        
        self.procedures_table.verticalHeader().setVisible(False)
        self.procedures_table.setSelectionBehavior(QAbstractItemView.SelectItems)
        self.procedures_table.cellChanged.connect(self.handle_cell_changed)
        
        # إضافة التخطيطات العمودية للتخطيط الأفقي
        tables_layout.addLayout(violations_layout)
        tables_layout.addLayout(procedures_layout)

    def load_table_data(self, table_widget, min_id, max_id, columns):
        """
        تحميل البيانات من قاعدة البيانات إلى الجدول المحدد، تطبيق خط (غليظ) ولون الخلايا.
        """
        self._loading_data = True
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            select_cols = ["ID"] + columns
            cols_str = ", ".join([f'"{col}"' for col in select_cols])
            query = f'SELECT {cols_str} FROM "{TABLE_NAME}" WHERE ID BETWEEN ? AND ? ORDER BY ID'
            cursor.execute(query, (min_id, max_id))
            data = cursor.fetchall()

            table_widget.setRowCount(0)
            table_widget.setRowCount(len(data))

            for row_idx, row_data in enumerate(data):
                db_id = row_data[0]
                table_widget.setRowHeight(row_idx, 25)

                for col_idx, col_name in enumerate(columns):
                    item_value = row_data[col_idx + 1]
                    item = QTableWidgetItem(str(item_value) if item_value is not None else "")

                    item.setFont(self.cell_font)
                    item.setForeground(QColor('black'))

                    if col_idx == 0:
                       item.setData(Qt.UserRole, db_id)

                    if col_name == "الاسم":
                        item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                    else:
                        item.setFlags(Qt.ItemIsSelectable | Qt.ItemIsEnabled | Qt.ItemIsEditable)

                    table_widget.setItem(row_idx, col_idx, item)

        except sqlite3.Error as e:
            print(f"خطأ أثناء تحميل البيانات لـ ID {min_id}-{max_id}: {e}")
            QMessageBox.warning(self, "خطأ تحميل البيانات", f"لا يمكن تحميل البيانات من قاعدة البيانات:\n{e}")
        finally:
            if conn:
                conn.close()
            self._loading_data = False

    def load_all_tables_data(self):
        """تحميل البيانات لكل الجداول."""
        print("بدء تحميل بيانات الجداول...")
        self.load_table_data(self.table1, 1, 6, ["الاسم", "العنوان", "ملاحظات"])
        self.load_table_data(self.violations_table, 7, 16, ["الاسم", "العنوان"])
        self.load_table_data(self.procedures_table, 17, 20, ["الاسم", "العنوان"])
        print("اكتمل تحميل بيانات الجداول.")

    def handle_cell_changed(self, row, column):
        """
        تُستدعى عند تغيير محتوى خلية في أحد الجداول، وتقوم بحفظ التغيير في قاعدة البيانات.
        """
        if self._loading_data:
            return

        sender_table = self.sender()
        item = sender_table.item(row, column)
        if not item:
            return

        id_item = sender_table.item(row, 0)
        if not id_item:
             print(f"خطأ: لا يمكن العثور على عنصر ID للصف {row}")
             return
        db_id = id_item.data(Qt.UserRole)
        if db_id is None:
             print(f"خطأ: لم يتم العثور على ID قاعدة البيانات للصف {row}")
             return

        new_value = item.text()
        column_name_db = ""
        if sender_table == self.table1:
             column_map = {0: "الاسم", 1: "العنوان", 2: "ملاحظات"}
             column_name_db = column_map.get(column)
        elif sender_table == self.violations_table or sender_table == self.procedures_table:
             column_map = {0: "الاسم", 1: "العنوان"}
             column_name_db = column_map.get(column)

        if column_name_db and column_name_db != "الاسم":
            print(f"تغيير في الجدول: ID={db_id}, العمود='{column_name_db}', القيمة الجديدة='{new_value}'")
            self.save_change_to_db(db_id, column_name_db, new_value)
        elif column_name_db == "الاسم":
            print(f"تم اكتشاف تغيير في عمود 'الاسم' (ID: {db_id})، ولكن لن يتم الحفظ.")

    def save_change_to_db(self, db_id, column_name, new_value):
        """
        حفظ قيمة جديدة لعمود معين وسجل معين في قاعدة البيانات.
        """
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            query = f'UPDATE "{TABLE_NAME}" SET "{column_name}" = ? WHERE ID = ?'
            cursor.execute(query, (new_value, db_id))
            conn.commit()
            print(f"تم حفظ التغيير بنجاح: ID={db_id}, العمود='{column_name}'")
        except sqlite3.Error as e:
            print(f"خطأ أثناء حفظ التغيير لـ ID {db_id}: {e}")
            QMessageBox.critical(self, "خطأ في الحفظ", f"لم يتم حفظ التغيير في قاعدة البيانات:\n{e}")
        finally:
            if conn:
                conn.close()
# --- تشغيل التطبيق ---
if __name__ == '__main__':
    # الحصول على الوقت والتاريخ الحاليين
    import datetime
    # Use current time information provided by the context
    now_str = "2025-03-26 18:04:04"
    now = datetime.datetime.strptime(now_str, '%Y-%m-%d %H:%M:%S')
    print(f"Current date and time: {now.strftime('%Y-%m-%d %H:%M:%S')}")
    # Use current location information provided by the context
    print("Location: Tangier, Tangier-Tétouan-Al Hoceima, Morocco")

    app = QApplication(sys.argv)
    mainWin = Sub6Window()
    mainWin.show()
    sys.exit(app.exec_())