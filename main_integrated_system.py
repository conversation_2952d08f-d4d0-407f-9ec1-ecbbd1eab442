"""
النظام التعليمي الشامل - النوافذ المدمجة
نظام بسيط وسريع يستخدم التبويبات المدمجة بدلاً من HTML
"""

import sys
import os
import json
import sqlite3
from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget, 
                           QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
                           QFrame, QGridLayout, QProgressBar, QTextEdit,
                           QMessageBox, QSplashScreen)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal, QDateTime
from PyQt5.QtGui import QFont, QIcon, QPixmap, QPalette, QColor

# استيراد النوافذ الموجودة
try:
    from sub2_window import SubWindow as InstitutionWindow
    INSTITUTION_AVAILABLE = True
    print("✅ تم استيراد نافذة بيانات المؤسسة بنجاح")
except ImportError as e:
    INSTITUTION_AVAILABLE = False
    print(f"❌ خطأ في استيراد نافذة المؤسسة: {e}")

# استيراد محركات النظام
try:
    from import_engine_corrected import ImportEngine
    IMPORT_ENGINE_AVAILABLE = True
    print("✅ تم استيراد محرك الاستيراد بنجاح")
except ImportError as e:
    IMPORT_ENGINE_AVAILABLE = False
    print(f"❌ خطأ في استيراد محرك الاستيراد: {e}")

try:
    from settings_engine import SettingsEngine
    SETTINGS_ENGINE_AVAILABLE = True
    print("✅ تم استيراد محرك الإعدادات بنجاح")
except ImportError as e:
    SETTINGS_ENGINE_AVAILABLE = False
    print(f"❌ خطأ في استيراد محرك الإعدادات: {e}")

class DatabaseStatsWorker(QThread):
    """خيط منفصل لجمع إحصائيات قاعدة البيانات"""
    statsReady = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.db_path = "data.db"
    
    def run(self):
        try:
            stats = self.get_database_stats()
            self.statsReady.emit(stats)
        except Exception as e:
            self.statsReady.emit({"error": str(e)})
    
    def get_database_stats(self):
        """جمع إحصائيات قاعدة البيانات"""
        if not os.path.exists(self.db_path):
            return {
                "students_count": 0,
                "teachers_count": 0,
                "sections_count": 0,
                "levels_count": 0,
                "secret_codes_count": 0,
                "current_year": "غير محدد"
            }
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        stats = {}
        
        try:
            # السنة الدراسية الحالية
            cursor.execute("SELECT DISTINCT السنة_الدراسية FROM اللوائح ORDER BY السنة_الدراسية DESC LIMIT 1")
            result = cursor.fetchone()
            stats['current_year'] = result[0] if result and result[0] else "غير محدد"
            
            # إحصائيات الطلاب
            cursor.execute("SELECT COUNT(*) FROM اللوائح")
            stats['students_count'] = cursor.fetchone()[0]
            
            # إحصائيات الأساتذة
            cursor.execute("SELECT COUNT(*) FROM الأساتذة")
            stats['teachers_count'] = cursor.fetchone()[0]
            
            # إحصائيات الأقسام
            cursor.execute("SELECT COUNT(DISTINCT القسم) FROM اللوائح")
            stats['sections_count'] = cursor.fetchone()[0]
            
            # إحصائيات المستويات
            cursor.execute("SELECT COUNT(DISTINCT المستوى) FROM اللوائح")
            stats['levels_count'] = cursor.fetchone()[0]
            
            # إحصائيات الرموز السرية
            cursor.execute("SELECT COUNT(*) FROM الرمز_السري")
            stats['secret_codes_count'] = cursor.fetchone()[0]
            
        except Exception as e:
            print(f"خطأ في جمع الإحصائيات: {e}")
            stats = {
                "students_count": 0,
                "teachers_count": 0,
                "sections_count": 0,
                "levels_count": 0,
                "secret_codes_count": 0,
                "current_year": "غير محدد"
            }
        finally:
            conn.close()
        
        return stats

class HomeWidget(QWidget):
    """الصفحة الرئيسية"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = parent
        self.setup_ui()
        self.setup_stats_worker()
    
    def setup_ui(self):
        """إعداد واجهة الصفحة الرئيسية"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
          # العنوان الرئيسي مع تصميم أنيق
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 rgba(102, 126, 234, 0.9), 
                    stop:0.3 rgba(118, 75, 162, 0.9), 
                    stop:0.7 rgba(240, 147, 251, 0.9), 
                    stop:1 rgba(245, 87, 108, 0.9));
                border-radius: 20px;
                padding: 25px;
                border: 2px solid rgba(255, 255, 255, 0.2);
                box-shadow: 0 12px 40px rgba(102, 126, 234, 0.3);
            }
        """)
        
        title_layout = QVBoxLayout(title_frame)
        title_label = QLabel("🎓 النظام التعليمي الشامل")
        title_label.setFont(QFont("Arial", 24, QFont.Bold))
        title_label.setStyleSheet("color: white; text-align: center;")
        title_label.setAlignment(Qt.AlignCenter)
        
        subtitle_label = QLabel("نظام إدارة المؤسسات التعليمية - النوافذ المدمجة")
        subtitle_label.setFont(QFont("Arial", 12))
        subtitle_label.setStyleSheet("color: #E3F2FD; text-align: center;")
        subtitle_label.setAlignment(Qt.AlignCenter)
        
        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)
        layout.addWidget(title_frame)
        
        # إحصائيات النظام
        self.stats_frame = self.create_stats_frame()
        layout.addWidget(self.stats_frame)
        
        # وحدات النظام
        modules_frame = self.create_modules_frame()
        layout.addWidget(modules_frame)
        
        # معلومات النظام
        info_frame = self.create_info_frame()
        layout.addWidget(info_frame)
        
        layout.addStretch()
    
    def create_stats_frame(self):
        """إنشاء إطار الإحصائيات"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # عنوان الإحصائيات
        title = QLabel("📊 إحصائيات النظام")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #1976D2; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # شريط التقدم للتحميل
        self.stats_progress = QProgressBar()
        self.stats_progress.setVisible(False)
        layout.addWidget(self.stats_progress)
        
        # شبكة الإحصائيات
        self.stats_grid = QGridLayout()
        self.stats_widgets = {}
        
        stats_items = [
            ("👥", "الطلاب", "students_count", "#4CAF50"),
            ("👨‍🏫", "الأساتذة", "teachers_count", "#FF9800"),
            ("🏫", "الأقسام", "sections_count", "#2196F3"),
            ("📚", "المستويات", "levels_count", "#9C27B0"),
            ("🔐", "الرموز السرية", "secret_codes_count", "#F44336"),
        ]
        
        for i, (icon, label, key, color) in enumerate(stats_items):
            widget = self.create_stat_widget(icon, label, "0", color)
            self.stats_widgets[key] = widget
            row, col = divmod(i, 3)
            self.stats_grid.addWidget(widget, row, col)
        
        layout.addLayout(self.stats_grid)
        
        # السنة الدراسية
        self.year_label = QLabel("📅 السنة الدراسية: غير محدد")
        self.year_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.year_label.setStyleSheet("color: #666; text-align: center; margin-top: 10px;")
        self.year_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.year_label)
        
        return frame
    
    def create_stat_widget(self, icon, label, value, color):
        """إنشاء عنصر إحصائية واحد"""
        widget = QFrame()
        widget.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }}
        """)
        
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setFont(QFont("Arial", 20))
        icon_label.setStyleSheet("color: white;")
        icon_label.setAlignment(Qt.AlignCenter)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 18, QFont.Bold))
        value_label.setStyleSheet("color: white;")
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setObjectName("value")
        
        # التسمية
        text_label = QLabel(label)
        text_label.setFont(QFont("Arial", 10))
        text_label.setStyleSheet("color: white;")
        text_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(icon_label)
        layout.addWidget(value_label)
        layout.addWidget(text_label)
        
        return widget
    
    def create_modules_frame(self):
        """إنشاء إطار وحدات النظام"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # عنوان الوحدات
        title = QLabel("🚀 وحدات النظام")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #1976D2; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # شبكة الوحدات
        modules_grid = QGridLayout()
        
        modules = [
            ("🏢", "بيانات المؤسسة", "إدارة معلومات المؤسسة والشعار", "institution", "#4CAF50"),
            ("📥", "استيراد البيانات", "استيراد اللوائح والأساتذة والرموز", "import", "#2196F3"),
            ("⚙️", "الإعدادات", "النسخ الاحتياطي وصيانة النظام", "settings", "#FF9800"),
            ("📊", "الإحصائيات", "تقارير وإحصائيات شاملة", "statistics", "#9C27B0"),
        ]
        
        for i, (icon, title, desc, tab_name, color) in enumerate(modules):
            button = self.create_module_button(icon, title, desc, tab_name, color)
            row, col = divmod(i, 2)
            modules_grid.addWidget(button, row, col)
        
        layout.addLayout(modules_grid)
        return frame
    
    def create_module_button(self, icon, title, description, tab_name, color):
        """إنشاء زر وحدة"""
        button = QPushButton()
        button.setFixedHeight(100)
        button.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                border: none;
                border-radius: 10px;
                color: white;
                text-align: left;
                padding: 15px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
        """)
        
        # إنشاء النص
        text = f"{icon} {title}\n{description}"
        button.setText(text)
        button.clicked.connect(lambda: self.open_module(tab_name))
        
        return button
    
    def darken_color(self, color, factor=0.9):
        """تغميق اللون"""
        color = color.replace("#", "")
        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)
        
        r = int(r * factor)
        g = int(g * factor)
        b = int(b * factor)
        
        return f"#{r:02x}{g:02x}{b:02x}"
    
    def create_info_frame(self):
        """إنشاء إطار معلومات النظام"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #F5F5F5;
                border: 1px solid #E0E0E0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        
        # معلومات النسخة
        version_info = QLabel("النظام التعليمي الشامل - النوافذ المدمجة v2.0\nتطوير: نظم التعليم الحديثة")
        version_info.setFont(QFont("Arial", 10))
        version_info.setStyleSheet("color: #666;")
        
        # الوقت الحالي
        self.time_label = QLabel()
        self.time_label.setFont(QFont("Arial", 10))
        self.time_label.setStyleSheet("color: #666;")
        self.time_label.setAlignment(Qt.AlignRight)
        
        # مؤقت لتحديث الوقت
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)
        self.update_time()
        
        layout.addWidget(version_info)
        layout.addStretch()
        layout.addWidget(self.time_label)
        
        return frame
    
    def setup_stats_worker(self):
        """إعداد خيط جمع الإحصائيات"""
        self.stats_worker = DatabaseStatsWorker()
        self.stats_worker.statsReady.connect(self.update_stats)
        
        # تحديث الإحصائيات كل 30 ثانية
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.refresh_stats)
        self.stats_timer.start(30000)
        
        # تحديث أولي
        self.refresh_stats()
    
    def refresh_stats(self):
        """تحديث الإحصائيات"""
        self.stats_progress.setVisible(True)
        self.stats_progress.setRange(0, 0)  # شريط تقدم غير محدد
        self.stats_worker.start()
    
    def update_stats(self, stats):
        """تحديث عرض الإحصائيات"""
        self.stats_progress.setVisible(False)
        
        if "error" in stats:
            print(f"خطأ في الإحصائيات: {stats['error']}")
            return
        
        # تحديث قيم الإحصائيات
        for key, widget in self.stats_widgets.items():
            if key in stats:
                value_label = widget.findChild(QLabel, "value")
                if value_label:
                    value_label.setText(str(stats[key]))
        
        # تحديث السنة الدراسية
        if "current_year" in stats:
            self.year_label.setText(f"📅 السنة الدراسية: {stats['current_year']}")
    
    def update_time(self):
        """تحديث الوقت"""
        current_time = QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm:ss")
        self.time_label.setText(f"🕐 {current_time}")
    
    def open_module(self, tab_name):
        """فتح وحدة معينة"""
        if self.main_window:
            self.main_window.switch_to_tab(tab_name)

class ImportWidget(QWidget):
    """واجهة الاستيراد المبسطة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = parent
        self.setup_ui()
        
        # إنشاء محرك الاستيراد إذا كان متوفراً
        self.import_engine = None
        if IMPORT_ENGINE_AVAILABLE:
            try:
                self.import_engine = ImportEngine(parent_window=parent)
                print("✅ تم إنشاء محرك الاستيراد في واجهة الاستيراد")
            except Exception as e:
                print(f"❌ خطأ في إنشاء محرك الاستيراد: {e}")
    
    def setup_ui(self):
        """إعداد واجهة الاستيراد"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # العنوان
        title = QLabel("📥 استيراد البيانات")
        title.setFont(QFont("Arial", 20, QFont.Bold))
        title.setStyleSheet("color: #1976D2; margin-bottom: 20px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # أزرار الاستيراد
        buttons_frame = QFrame()
        buttons_layout = QGridLayout(buttons_frame)
        
        # أزرار الاستيراد
        import_buttons = [
            ("📚", "استيراد لوائح منظومة مسار", self.import_masar_data, "#4CAF50"),
            ("👨‍🏫", "استيراد أسماء الأساتذة", self.import_teachers_data, "#FF9800"),
            ("🔐", "استيراد الرموز السرية", self.import_secret_codes, "#F44336"),
            ("📋", "تحديث البيانات", self.refresh_data, "#2196F3"),
        ]
        
        for i, (icon, text, callback, color) in enumerate(import_buttons):
            button = QPushButton(f"{icon} {text}")
            button.setFixedHeight(80)
            button.setFont(QFont("Arial", 12, QFont.Bold))
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 15px;
                }}
                QPushButton:hover {{
                    background-color: {self.darken_color(color)};
                }}
            """)
            button.clicked.connect(callback)
            
            row, col = divmod(i, 2)
            buttons_layout.addWidget(button, row, col)
        
        layout.addWidget(buttons_frame)
        
        # سجل العمليات
        log_frame = QFrame()
        log_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        log_layout = QVBoxLayout(log_frame)
        
        log_title = QLabel("📝 سجل العمليات")
        log_title.setFont(QFont("Arial", 14, QFont.Bold))
        log_title.setStyleSheet("color: #1976D2;")
        log_layout.addWidget(log_title)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #F5F5F5;
                border: 1px solid #DDD;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Courier New';
            }
        """)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_frame)
        layout.addStretch()
    
    def darken_color(self, color, factor=0.9):
        """تغميق اللون"""
        color = color.replace("#", "")
        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)
        
        r = int(r * factor)
        g = int(g * factor)
        b = int(b * factor)
        
        return f"#{r:02x}{g:02x}{b:02x}"
    
    def log_message(self, message, level="info"):
        """إضافة رسالة للسجل"""
        timestamp = QDateTime.currentDateTime().toString("hh:mm:ss")
        
        # تحديد رمز الحالة
        icons = {
            "info": "ℹ️",
            "success": "✅",
            "error": "❌",
            "warning": "⚠️",
            "progress": "🔄"
        }
        
        icon = icons.get(level, "ℹ️")
        formatted_message = f"[{timestamp}] {icon} {message}"
        
        self.log_text.append(formatted_message)
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum()
        )
    
    def import_masar_data(self):
        """استيراد بيانات منظومة مسار"""
        if not IMPORT_ENGINE_AVAILABLE:
            self.log_message("محرك الاستيراد غير متوفر", "error")
            QMessageBox.warning(self, "خطأ", "محرك الاستيراد غير متوفر!")
            return
        
        if not self.import_engine:
            self.log_message("فشل في إنشاء محرك الاستيراد", "error")
            return
        
        self.log_message("بدء استيراد بيانات منظومة مسار...", "progress")
        try:
            self.import_engine.selectMasarFile()
        except Exception as e:
            self.log_message(f"خطأ في استيراد منظومة مسار: {str(e)}", "error")
    
    def import_teachers_data(self):
        """استيراد بيانات الأساتذة"""
        if not self.import_engine:
            self.log_message("محرك الاستيراد غير متوفر", "error")
            return
        
        self.log_message("بدء استيراد بيانات الأساتذة...", "progress")
        try:
            self.import_engine.selectTeachersFile()
        except Exception as e:
            self.log_message(f"خطأ في استيراد الأساتذة: {str(e)}", "error")
    
    def import_secret_codes(self):
        """استيراد الرموز السرية"""
        if not self.import_engine:
            self.log_message("محرك الاستيراد غير متوفر", "error")
            return
        
        self.log_message("بدء استيراد الرموز السرية...", "progress")
        try:
            self.import_engine.selectSecretCodesFiles()
        except Exception as e:
            self.log_message(f"خطأ في استيراد الرموز السرية: {str(e)}", "error")
    
    def refresh_data(self):
        """تحديث البيانات"""
        if not self.import_engine:
            self.log_message("محرك الاستيراد غير متوفر", "error")
            return
        
        self.log_message("جاري تحديث البيانات...", "progress")
        try:
            self.import_engine.refreshData()
            self.log_message("تم تحديث البيانات بنجاح", "success")
        except Exception as e:
            self.log_message(f"خطأ في تحديث البيانات: {str(e)}", "error")

class SettingsWidget(QWidget):
    """واجهة الإعدادات المبسطة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = parent
        self.setup_ui()
        
        # إنشاء محرك الإعدادات إذا كان متوفراً
        self.settings_engine = None
        if SETTINGS_ENGINE_AVAILABLE:
            try:
                self.settings_engine = SettingsEngine(parent_window=parent)
                print("✅ تم إنشاء محرك الإعدادات في واجهة الإعدادات")
            except Exception as e:
                print(f"❌ خطأ في إنشاء محرك الإعدادات: {e}")
    
    def setup_ui(self):
        """إعداد واجهة الإعدادات"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # العنوان
        title = QLabel("⚙️ إعدادات النظام")
        title.setFont(QFont("Arial", 20, QFont.Bold))
        title.setStyleSheet("color: #1976D2; margin-bottom: 20px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # إطار معلومات قاعدة البيانات
        db_info_frame = self.create_database_info_frame()
        layout.addWidget(db_info_frame)
        
        # إطار عمليات النظام
        operations_frame = self.create_operations_frame()
        layout.addWidget(operations_frame)
        
        layout.addStretch()
    
    def create_database_info_frame(self):
        """إنشاء إطار معلومات قاعدة البيانات"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        title = QLabel("📊 معلومات قاعدة البيانات")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #1976D2;")
        layout.addWidget(title)
        
        # معلومات قاعدة البيانات
        self.db_info_label = QLabel("جاري تحميل معلومات قاعدة البيانات...")
        self.db_info_label.setFont(QFont("Arial", 12))
        self.db_info_label.setStyleSheet("color: #666; padding: 10px;")
        layout.addWidget(self.db_info_label)
        
        # تحديث معلومات قاعدة البيانات
        self.update_database_info()
        
        return frame
    
    def create_operations_frame(self):
        """إنشاء إطار عمليات النظام"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        title = QLabel("🔧 عمليات النظام")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #1976D2;")
        layout.addWidget(title)
        
        # شبكة الأزرار
        buttons_layout = QGridLayout()
        
        operations = [
            ("💾", "نسخة احتياطية", self.create_backup, "#4CAF50"),
            ("📂", "استعادة نسخة احتياطية", self.restore_backup, "#2196F3"),
            ("🔄", "إعادة تعيين السنة الدراسية", self.reset_school_year, "#FF9800"),
            ("🗑️", "حذف جميع البيانات", self.delete_all_data, "#F44336"),
        ]
        
        for i, (icon, text, callback, color) in enumerate(operations):
            button = QPushButton(f"{icon} {text}")
            button.setFixedHeight(60)
            button.setFont(QFont("Arial", 11, QFont.Bold))
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px;
                }}
                QPushButton:hover {{
                    background-color: {self.darken_color(color)};
                }}
            """)
            button.clicked.connect(callback)
            
            row, col = divmod(i, 2)
            buttons_layout.addWidget(button, row, col)
        
        layout.addLayout(buttons_layout)
        return frame
    
    def darken_color(self, color, factor=0.9):
        """تغميق اللون"""
        color = color.replace("#", "")
        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)
        
        r = int(r * factor)
        g = int(g * factor)
        b = int(b * factor)
        
        return f"#{r:02x}{g:02x}{b:02x}"
    
    def update_database_info(self):
        """تحديث معلومات قاعدة البيانات"""
        try:
            db_path = "data.db"
            if os.path.exists(db_path):
                size = os.path.getsize(db_path) / 1024 / 1024  # MB
                info_text = f"""
📁 مسار قاعدة البيانات: {db_path}
📏 حجم قاعدة البيانات: {size:.2f} ميجابايت
📅 آخر تعديل: {QDateTime.fromSecsSinceEpoch(int(os.path.getmtime(db_path))).toString()}
✅ حالة قاعدة البيانات: متصلة
                """.strip()
            else:
                info_text = "❌ قاعدة البيانات غير موجودة"
            
            self.db_info_label.setText(info_text)
        except Exception as e:
            self.db_info_label.setText(f"❌ خطأ في قراءة معلومات قاعدة البيانات: {str(e)}")
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        if not self.settings_engine:
            QMessageBox.warning(self, "خطأ", "محرك الإعدادات غير متوفر!")
            return
        
        try:
            # استدعاء دالة النسخ الاحتياطي
            result = QMessageBox.question(
                self, "تأكيد", "هل تريد إنشاء نسخة احتياطية من قاعدة البيانات؟"
            )
            if result == QMessageBox.Yes:
                # هنا يمكن إضافة منطق النسخ الاحتياطي
                QMessageBox.information(self, "نجح", "تم إنشاء النسخة الاحتياطية بنجاح!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء النسخة الاحتياطية: {str(e)}")
    
    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        QMessageBox.information(self, "قيد التطوير", "هذه الميزة قيد التطوير")
    
    def reset_school_year(self):
        """إعادة تعيين السنة الدراسية"""
        QMessageBox.information(self, "قيد التطوير", "هذه الميزة قيد التطوير")
    
    def delete_all_data(self):
        """حذف جميع البيانات"""
        result = QMessageBox.warning(
            self, "تحذير!", 
            "هل أنت متأكد من حذف جميع البيانات؟\nهذه العملية لا يمكن التراجع عنها!",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if result == QMessageBox.Yes:
            # طلب كلمة مرور التأكيد
            password = QMessageBox.getText(self, "كلمة المرور", "أدخل كلمة المرور للتأكيد:")
            if password and password == "admin":
                QMessageBox.information(self, "نجح", "تم حذف جميع البيانات!")
            else:
                QMessageBox.warning(self, "خطأ", "كلمة مرور خاطئة!")

class StatisticsWidget(QWidget):
    """واجهة الإحصائيات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة الإحصائيات"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان قيد التطوير
        title = QLabel("📊 الإحصائيات والتقارير")
        title.setFont(QFont("Arial", 20, QFont.Bold))
        title.setStyleSheet("color: #1976D2;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # رسالة قيد التطوير
        development_frame = QFrame()
        development_frame.setStyleSheet("""
            QFrame {
                background-color: #FFF3E0;
                border: 2px solid #FF9800;
                border-radius: 15px;
                padding: 30px;
                margin: 50px;
            }
        """)
        
        dev_layout = QVBoxLayout(development_frame)
        
        icon_label = QLabel("🚧")
        icon_label.setFont(QFont("Arial", 60))
        icon_label.setAlignment(Qt.AlignCenter)
        dev_layout.addWidget(icon_label)
        
        dev_title = QLabel("قيد التطوير")
        dev_title.setFont(QFont("Arial", 24, QFont.Bold))
        dev_title.setStyleSheet("color: #F57C00;")
        dev_title.setAlignment(Qt.AlignCenter)
        dev_layout.addWidget(dev_title)
        
        dev_desc = QLabel("ستتوفر وحدة الإحصائيات والتقارير الشاملة في التحديثات القادمة")
        dev_desc.setFont(QFont("Arial", 14))
        dev_desc.setStyleSheet("color: #666;")
        dev_desc.setAlignment(Qt.AlignCenter)
        dev_desc.setWordWrap(True)
        dev_layout.addWidget(dev_desc)
        
        layout.addWidget(development_frame)
        layout.addStretch()

class MainIntegratedSystem(QMainWindow):
    """النظام الرئيسي للنوافذ المدمجة"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_window()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.setWindowTitle("النظام التعليمي الشامل - النوافذ المدمجة")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # تطبيق أيقونة إذا كانت متوفرة
        if os.path.exists("01.ico"):
            self.setWindowIcon(QIcon("01.ico"))        # تطبيق نمط جمالي متطور وأنيق
        self.setStyleSheet("""
            /* النافذة الرئيسية - خلفية متدرجة أنيقة */
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #667eea, stop:0.3 #764ba2, stop:0.7 #f093fb, stop:1 #f5576c);
                font-family: 'Segoe UI', 'Microsoft YaHei UI', 'Helvetica Neue', Arial, sans-serif;
                color: #2C3E50;
            }
            
            /* التبويبات الرئيسية - تصميم Glass Morphism */
            QTabWidget::pane {
                border: none;
                background: rgba(255, 255, 255, 0.15);
                backdrop-filter: blur(20px);
                border-radius: 20px;
                margin-top: 15px;
                box-shadow: 
                    0 8px 32px rgba(31, 38, 135, 0.37),
                    inset 0 1px 0 rgba(255, 255, 255, 0.18);
                border: 1px solid rgba(255, 255, 255, 0.18);
            }
            
            QTabWidget::tab-bar {
                alignment: center;
                background: transparent;
            }
            
            /* تبويبات أنيقة مع تأثيرات حديثة */
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.25), 
                    stop:0.5 rgba(255, 255, 255, 0.15), 
                    stop:1 rgba(255, 255, 255, 0.05));
                color: #1A365D;
                padding: 18px 30px;
                margin: 3px 5px;
                border-radius: 15px;
                font-weight: 700;
                font-size: 14px;
                border: 2px solid rgba(255, 255, 255, 0.2);
                min-width: 140px;
                backdrop-filter: blur(10px);
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            }
            
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:0.3 #764ba2, stop:0.7 #f093fb, stop:1 #f5576c);
                color: white;
                border: 2px solid rgba(255, 255, 255, 0.3);
                transform: translateY(-3px);
                box-shadow: 
                    0 8px 25px rgba(102, 126, 234, 0.6),
                    0 3px 5px rgba(0, 0, 0, 0.2);
                font-weight: 800;
            }
            
            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(102, 126, 234, 0.4), 
                    stop:0.5 rgba(118, 75, 162, 0.4), 
                    stop:1 rgba(240, 147, 251, 0.4));
                border: 2px solid rgba(102, 126, 234, 0.6);
                color: #1A365D;
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
            }
            
            QTabBar::tab:pressed {
                transform: translateY(0px);
                box-shadow: 0 2px 10px rgba(102, 126, 234, 0.4);
            }
            
            /* الأزرار العامة - تصميم حديث */
            QPushButton {
                font-family: 'Segoe UI', 'Microsoft YaHei UI', Arial, sans-serif;
                font-weight: 600;
                border: none;
                border-radius: 15px;
                padding: 15px 25px;
                font-size: 13px;
                color: white;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            }
            
            QPushButton:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #7c3aed, stop:1 #f093fb);
            }
            
            QPushButton:pressed {                transform: translateY(1px);
                box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #5b21b6, stop:1 #ec4899);
            }
            
            /* الإطارات - تصميم Glass Morphism */
            QFrame {
                border-radius: 15px;
                background: rgba(255, 255, 255, 0.25);
                backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.18);
                box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            }
            
            /* التسميات - خطوط حديثة */
            QLabel {
                font-family: 'Segoe UI', 'Microsoft YaHei UI', Arial, sans-serif;
                color: #1A365D;
                font-weight: 500;
            }
            
            /* النصوص - تصميم أنيق */
            QTextEdit {
                border: 2px solid rgba(102, 126, 234, 0.3);
                border-radius: 12px;
                background: rgba(255, 255, 255, 0.9);
                font-family: 'Cascadia Code', 'Consolas', 'JetBrains Mono', monospace;
                font-size: 12px;
                padding: 10px;
                color: #2D3748;
                selection-background-color: rgba(102, 126, 234, 0.3);
            }
            
            QTextEdit:focus {
                border: 2px solid #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            }
            
            /* شريط التقدم - تصميم متدرج */
            QProgressBar {
                border: none;
                border-radius: 8px;
                background-color: rgba(255, 255, 255, 0.3);
                text-align: center;
                font-weight: bold;
                color: #1A365D;
            }
            
            QProgressBar::chunk {
                border-radius: 8px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
            }
            
            /* القوائم والجداول */
            QListWidget, QTableWidget {
                border: 2px solid rgba(102, 126, 234, 0.2);
                border-radius: 12px;
                background: rgba(255, 255, 255, 0.95);
                alternate-background-color: rgba(102, 126, 234, 0.05);
                selection-background-color: rgba(102, 126, 234, 0.3);
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            
            /* حقول الإدخال */
            QLineEdit, QComboBox {
                border: 2px solid rgba(102, 126, 234, 0.3);
                border-radius: 10px;
                padding: 8px 12px;
                background: rgba(255, 255, 255, 0.9);
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 12px;
                color: #2D3748;
            }
            
            QLineEdit:focus, QComboBox:focus {
                border: 2px solid #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            }
            
            /* شريط التمرير */
            QScrollBar:vertical {
                background: rgba(255, 255, 255, 0.1);
                width: 12px;
                border-radius: 6px;
            }
            
            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 6px;
                min-height: 20px;
            }
              QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #7c3aed, stop:1 #f093fb);
            }
        """)
        
        # توسيط النافذة في الشاشة
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4A90E2, stop:1 #2E86C1);
                border-radius: 6px;
            }
        """)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إنشاء التبويبات الرئيسية
        self.tabs = QTabWidget()
        self.setCentralWidget(self.tabs)
        
        # إضافة التبويبات
        self.add_home_tab()
        self.add_institution_tab()
        self.add_import_tab()
        self.add_settings_tab()
        self.add_statistics_tab()
        
        # ربط تغيير التبويبة
        self.tabs.currentChanged.connect(self.on_tab_changed)
    
    def add_home_tab(self):
        """إضافة تبويبة الصفحة الرئيسية"""
        self.home_widget = HomeWidget(self)
        self.tabs.addTab(self.home_widget, "🏠 الرئيسية")
    
    def add_institution_tab(self):
        """إضافة تبويبة بيانات المؤسسة"""
        if INSTITUTION_AVAILABLE:
            try:
                self.institution_widget = InstitutionWindow(self)
                self.tabs.addTab(self.institution_widget, "🏢 بيانات المؤسسة")
                print("✅ تم إضافة تبويبة بيانات المؤسسة")
            except Exception as e:
                print(f"❌ خطأ في إضافة تبويبة المؤسسة: {e}")
                # إضافة تبويبة بديلة
                placeholder = QLabel("❌ خطأ في تحميل واجهة بيانات المؤسسة")
                placeholder.setAlignment(Qt.AlignCenter)
                self.tabs.addTab(placeholder, "🏢 بيانات المؤسسة")
        else:
            # إضافة تبويبة بديلة
            placeholder = QLabel("❌ واجهة بيانات المؤسسة غير متوفرة")
            placeholder.setAlignment(Qt.AlignCenter)
            self.tabs.addTab(placeholder, "🏢 بيانات المؤسسة")
    
    def add_import_tab(self):
        """إضافة تبويبة الاستيراد"""
        self.import_widget = ImportWidget(self)
        self.tabs.addTab(self.import_widget, "📥 الاستيراد")
    
    def add_settings_tab(self):
        """إضافة تبويبة الإعدادات"""
        self.settings_widget = SettingsWidget(self)
        self.tabs.addTab(self.settings_widget, "⚙️ الإعدادات")
    
    def add_statistics_tab(self):
        """إضافة تبويبة الإحصائيات"""
        self.statistics_widget = StatisticsWidget(self)
        self.tabs.addTab(self.statistics_widget, "📊 الإحصائيات")
    
    def on_tab_changed(self, index):
        """استدعاء عند تغيير التبويبة"""
        tab_text = self.tabs.tabText(index)
        print(f"📄 تم الانتقال إلى: {tab_text}")
        
        # تحديث خاص بكل تبويبة
        if "الرئيسية" in tab_text and hasattr(self, 'home_widget'):
            self.home_widget.refresh_stats()
        elif "الإعدادات" in tab_text and hasattr(self, 'settings_widget'):
            self.settings_widget.update_database_info()
    
    def switch_to_tab(self, tab_name):
        """التبديل إلى تبويبة معينة"""
        tab_mapping = {
            "institution": "🏢 بيانات المؤسسة",
            "import": "📥 الاستيراد", 
            "settings": "⚙️ الإعدادات",
            "statistics": "📊 الإحصائيات"
        }
        
        target_tab = tab_mapping.get(tab_name, "")
        if target_tab:
            for i in range(self.tabs.count()):
                if target_tab in self.tabs.tabText(i):
                    self.tabs.setCurrentIndex(i)
                    break

def show_splash_screen():
    """عرض شاشة البداية"""
    app = QApplication.instance()
    
    # إنشاء شاشة البداية
    splash_pix = QPixmap(400, 300)
    splash_pix.fill(QColor("#2196F3"))
    
    splash = QSplashScreen(splash_pix)
    splash.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
    splash.show()
    
    # إضافة نص للشاشة
    splash.showMessage(
        "🎓 النظام التعليمي الشامل\nالنوافذ المدمجة v2.0\n\nجاري التحميل...",
        Qt.AlignCenter,
        QColor("white")
    )
    
    app.processEvents()
    
    # محاكاة التحميل
    import time
    time.sleep(2)
    
    splash.close()
    return splash

def main():
    """تشغيل النظام الرئيسي"""
    app = QApplication(sys.argv)
    
    # إعداد التطبيق
    app.setApplicationName("النظام التعليمي الشامل - النوافذ المدمجة")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Modern Education Systems")
    
    # عرض شاشة البداية
    splash = show_splash_screen()
    
    # إنشاء النافذة الرئيسية
    window = MainIntegratedSystem()
    window.show()
    
    # تشغيل التطبيق
    sys.exit(app.exec_())

if __name__ == '__main__':
    print("🌐 بدء تشغيل النظام التعليمي الشامل - النوافذ المدمجة...")
    print("=" * 60)
    print("📋 الميزات الجديدة:")
    print("   🔹 نوافذ مدمجة سريعة ومستقرة")
    print("   🔹 تبويبات منظمة وسهلة الاستخدام")
    print("   🔹 أداء محسن وذاكرة أقل")
    print("   🔹 واجهة نظيفة وعملية")
    print("   🔹 سهولة الصيانة والتطوير")
    print("=" * 60)
    print("🚀 جاري تشغيل النظام...")
    
    try:
        main()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("💡 تأكد من تثبيت PyQt5:")
        print("   pip install PyQt5")
