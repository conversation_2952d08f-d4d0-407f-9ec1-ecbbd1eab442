#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف اختبار القائمة المنسدلة المدمجة في التبويب
اختبار القائمة التي تظهر كجزء من التبويب وليس كنافذة منفصلة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

def test_integrated_dropdown():
    """اختبار القائمة المنسدلة المدمجة"""
    print("=" * 60)
    print("🔗 اختبار القائمة المنسدلة المدمجة في التبويب")
    print("=" * 60)
    
    # إنشاء تطبيق Qt
    app = QApplication(sys.argv)
    app.setApplicationName("اختبار القائمة المدمجة")
    
    # تعيين أيقونة التطبيق إذا كانت متوفرة
    if os.path.exists("01.ico"):
        app.setWindowIcon(QIcon("01.ico"))
    
    try:
        print("📥 استيراد main_window...")
        from main_window import MainWindow
        print("✅ تم استيراد main_window بنجاح")
        
        print("\n🏗️ إنشاء النافذة الرئيسية...")
        main_window = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # التحقق من وجود القائمة المنسدلة
        if hasattr(main_window, 'dropdown_widget'):
            print("✅ تم العثور على القائمة المنسدلة المدمجة")
            dropdown = main_window.dropdown_widget
            print(f"   📏 حجم القائمة: {dropdown.size()}")
            print(f"   👁️ مرئية: {dropdown.isVisible()}")
            print(f"   📐 ارتفاع: {dropdown.height()}")
            print(f"   🎨 اسم الكائن: {dropdown.objectName()}")
            
            # التحقق من الوالد
            parent = dropdown.parent()
            if parent:
                print(f"   👨‍👩‍👧‍👦 الوالد: {parent.objectName()}")
            else:
                print("   ⚠️ لا يوجد والد محدد")
                
        else:
            print("❌ لم يتم العثور على القائمة المنسدلة")
            
        # التحقق من الدوال الجديدة
        if hasattr(main_window, '_show_dropdown'):
            print("✅ تم العثور على دالة إظهار القائمة")
        else:
            print("❌ لم يتم العثور على دالة إظهار القائمة")
            
        if hasattr(main_window, '_hide_dropdown'):
            print("✅ تم العثور على دالة إخفاء القائمة")
        else:
            print("❌ لم يتم العثور على دالة إخفاء القائمة")
        
        print("\n🎨 مواصفات القائمة المدمجة:")
        print("   📋 التصميم:")
        print("      • خلفية فاتحة (#f8f9fa)")
        print("      • حدود خضراء من 3 جهات")
        print("      • زوايا مدورة في الأسفل")
        print("      • تخطيط أفقي (صف واحد)")
        
        print("\n   🎯 الأزرار:")
        print("      🏢 بيانات المؤسسة (أزرق)")
        print("      ⚙️ تهيئة البرنامج (برتقالي)")
        print("      🏫 البنية التربوية (بنفسجي)")
        print("      📊 الإحصائيات (أخضر)")
        
        print("\n🎭 التحسينات المطبقة:")
        print("   ✅ القائمة مدمجة في النافذة الرئيسية")
        print("   ✅ تظهر تحت التبويب مباشرة")
        print("   ✅ تخطيط أفقي مدمج")
        print("   ✅ أزرار أصغر وأكثر تناسقاً")
        print("   ✅ تأثير انزلاق سلس")
        print("   ✅ إخفاء تلقائي عند التنقل")
        
        print("\n🖥️ عرض النافذة...")
        main_window.show()
        main_window.setWindowTitle("اختبار القائمة المدمجة - انقر على تبويب بيانات المؤسسة")
        
        print("\n🎯 تعليمات الاختبار:")
        print("   1️⃣ ابحث عن تبويب 'بيانات المؤسسة'")
        print("   2️⃣ انقر على التبويب - يجب أن تظهر قائمة مدمجة تحته")
        print("   3️⃣ لاحظ أن القائمة جزء من النافذة وليست منفصلة")
        print("   4️⃣ انقر مرة أخرى على التبويب - يجب أن تختفي القائمة")
        print("   5️⃣ جرب الأزرار في القائمة")
        print("   6️⃣ انقر على تبويب آخر - يجب أن تختفي القائمة تلقائياً")
        
        print("\n🌟 الفرق عن النسخة السابقة:")
        print("   ❌ السابق: قائمة تظهر كنافذة منفصلة")
        print("   ✅ الحالي: قائمة مدمجة تحت التبويب مباشرة")
        print("   ❌ السابق: تخطيط عمودي يأخذ مساحة كبيرة")
        print("   ✅ الحالي: تخطيط أفقي مدمج وأنيق")
        print("   ❌ السابق: أزرار كبيرة")
        print("   ✅ الحالي: أزرار متوسطة ومتناسقة")
        
        print("\n🚀 تشغيل التطبيق...")
        return app.exec_()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد main_window: {e}")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()
        return 1

def show_integration_details():
    """عرض تفاصيل التكامل"""
    print("\n📋 تفاصيل التكامل:")
    print("-" * 40)
    
    details = [
        ("🔗 التكامل مع النافذة", [
            "القائمة جزء من central_widget",
            "مدمجة في main_layout",
            "تظهر تحت navbar_frame مباشرة",
            "لا تظهر كنافذة منفصلة"
        ]),
        ("📐 التخطيط المحسن", [
            "تخطيط أفقي بدلاً من عمودي",
            "أزرار بعرض 180 بكسل وارتفاع 35 بكسل",
            "مسافات 10 بكسل بين الأزرار",
            "حشو 15 بكسل من الجانبين"
        ]),
        ("🎨 التصميم المحسن", [
            "خلفية فاتحة (#f8f9fa)",
            "حدود خضراء من 3 جهات فقط",
            "زوايا مدورة في الأسفل (12px)",
            "ألوان أفتح للأزرار"
        ]),
        ("⚡ الأداء المحسن", [
            "تأثير انزلاق سلس",
            "إظهار/إخفاء فوري",
            "استهلاك ذاكرة أقل",
            "تفاعل أسرع"
        ])
    ]
    
    for title, items in details:
        print(f"\n{title}:")
        for item in items:
            print(f"   • {item}")

def show_before_after():
    """مقارنة قبل وبعد التحسين"""
    print("\n" + "=" * 60)
    print("📊 مقارنة قبل وبعد التحسين")
    print("=" * 60)
    
    comparison = [
        ("الموقع", "نافذة منفصلة", "مدمجة تحت التبويب"),
        ("التخطيط", "عمودي (4 صفوف)", "أفقي (صف واحد)"),
        ("الحجم", "كبير (160px ارتفاع)", "صغير (59px ارتفاع)"),
        ("الأزرار", "40px ارتفاع", "35px ارتفاع"),
        ("المساحة", "يأخذ مساحة كبيرة", "مدمج وموفر للمساحة"),
        ("التفاعل", "قائمة منفصلة", "جزء من الواجهة"),
        ("المظهر", "منفصل عن التبويب", "متصل بالتبويب")
    ]
    
    print(f"{'الخاصية':<15} {'قبل التحسين':<20} {'بعد التحسين':<20}")
    print("-" * 60)
    for feature, before, after in comparison:
        print(f"{feature:<15} {before:<20} {after:<20}")

if __name__ == "__main__":
    print("🔗 مرحباً بك في اختبار القائمة المدمجة!")
    
    # عرض تفاصيل التكامل
    show_integration_details()
    
    # عرض مقارنة قبل وبعد
    show_before_after()
    
    print("\n" + "=" * 60)
    print("🚀 بدء تشغيل النافذة الرئيسية...")
    print("=" * 60)
    
    # تشغيل الاختبار
    exit_code = test_integrated_dropdown()
    
    print(f"\n🔚 انتهى الاختبار بكود الخروج: {exit_code}")
    sys.exit(exit_code)
