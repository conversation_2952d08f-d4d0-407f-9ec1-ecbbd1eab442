#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الطباعة المحسنة - thermal1_image_print.py
"""

import sys
import os
from datetime import datetime

def test_thermal_printing():
    """اختبار وحدة الطباعة الحرارية المحسنة"""
    print("🖨️ اختبار وحدة الطباعة الحرارية المحسنة")
    print("=" * 60)
    
    try:
        # استيراد الوحدة الجديدة
        import thermal1_image_print as thermal_print
        print("✅ تم استيراد وحدة الطباعة المحسنة بنجاح")
        
        # اختبار الحصول على اسم الطابعة
        print("\n🔍 اختبار الحصول على اسم الطابعة:")
        printer_name = thermal_print.get_thermal_printer_name()
        if printer_name:
            print(f"✅ تم العثور على الطابعة: {printer_name}")
        else:
            print("⚠️ لم يتم العثور على طابعة")
        
        # اختبار الحصول على معلومات المؤسسة
        print("\n🏫 اختبار الحصول على معلومات المؤسسة:")
        institution_name, school_year, guard_number = thermal_print.get_institution_info()
        print(f"✅ اسم المؤسسة: {institution_name}")
        print(f"✅ السنة الدراسية: {school_year}")
        print(f"✅ رقم الحراسة: {guard_number}")
        
        # إنشاء بيانات تجريبية للتلاميذ
        test_students = [
            {"rt": "1", "name": "أحمد محمد علي", "code": "001"},
            {"rt": "2", "name": "فاطمة أحمد حسن", "code": "002"},
            {"rt": "3", "name": "محمد عبد الله سالم", "code": "003"},
            {"rt": "4", "name": "عائشة محمود إبراهيم", "code": "004"},
            {"rt": "5", "name": "يوسف عبد الرحمن", "code": "005"}
        ]
        
        test_section = "الثانية ثانوي علوم"
        current_date = datetime.now().strftime("%Y/%m/%d")
        current_time = datetime.now().strftime("%H:%M")
        
        print(f"\n📋 بيانات الاختبار:")
        print(f"   عدد التلاميذ: {len(test_students)}")
        print(f"   القسم: {test_section}")
        print(f"   التاريخ: {current_date}")
        print(f"   الوقت: {current_time}")
        
        # اختبار تنسيق محتوى ورقة الدخول
        print("\n📄 اختبار تنسيق محتوى ورقة الدخول:")
        content = thermal_print.format_entry_form_content(
            test_students, test_section, current_date, current_time
        )
        print("✅ تم تنسيق المحتوى بنجاح")
        print(f"📏 طول المحتوى: {len(content)} حرف")
        
        # عرض عينة من المحتوى
        print("\n📖 عينة من المحتوى المنسق:")
        print("-" * 50)
        lines = content.split('\n')
        for i, line in enumerate(lines[:15]):  # عرض أول 15 سطر
            print(f"{i+1:2d}: {line}")
        if len(lines) > 15:
            print(f"... و {len(lines) - 15} سطر إضافي")
        print("-" * 50)
        
        # اختبار الطباعة (اختياري)
        print("\n🖨️ اختبار الطباعة:")
        user_input = input("هل تريد اختبار الطباعة الفعلية؟ (y/n): ").lower().strip()
        
        if user_input in ['y', 'yes', 'نعم', 'ن']:
            print("🚀 بدء اختبار الطباعة...")
            
            # اختبار طباعة صفحة تجريبية
            print("\n1️⃣ اختبار طباعة صفحة تجريبية:")
            test_result = thermal_print.test_printer()
            if test_result:
                print("✅ تم إرسال صفحة الاختبار")
            else:
                print("❌ فشل في إرسال صفحة الاختبار")
            
            # اختبار طباعة ورقة الدخول
            print("\n2️⃣ اختبار طباعة ورقة الدخول:")
            entry_result = thermal_print.print_entry_form_direct(
                test_students, test_section, current_date, current_time
            )
            if entry_result:
                print("✅ تم إرسال ورقة الدخول للطباعة")
            else:
                print("❌ فشل في طباعة ورقة الدخول")
            
            # اختبار طباعة ورقة التأخر
            print("\n3️⃣ اختبار طباعة ورقة التأخر:")
            late_students = test_students[:3]  # أول 3 تلاميذ فقط
            late_result = thermal_print.print_late_form_direct(
                late_students, test_section, current_date, current_time
            )
            if late_result:
                print("✅ تم إرسال ورقة التأخر للطباعة")
            else:
                print("❌ فشل في طباعة ورقة التأخر")
                
        else:
            print("⏭️ تم تخطي اختبار الطباعة الفعلية")
        
        print("\n🎉 انتهى الاختبار بنجاح!")
        print("=" * 60)
        
        # ملخص النتائج
        print("\n📊 ملخص النتائج:")
        print("✅ استيراد الوحدة: نجح")
        print("✅ الحصول على معلومات المؤسسة: نجح")
        print("✅ تنسيق المحتوى: نجح")
        if 'user_input' in locals() and user_input in ['y', 'yes', 'نعم', 'ن']:
            print("✅ اختبار الطباعة: تم")
        else:
            print("⏭️ اختبار الطباعة: تم تخطيه")
        
        print("\n🔧 الميزات الجديدة:")
        print("• طباعة نصية مباشرة بدون تحويل إلى صورة")
        print("• دعم محسن للنصوص العربية")
        print("• سرعة أكبر في الطباعة")
        print("• توافق مع جميع أنواع الطابعات")
        print("• جودة طباعة محسنة")
        print("• تنسيق أفضل للمحتوى")
        print("• معلومات تشخيصية مفصلة")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدة: {e}")
        print("تأكد من وجود ملف thermal1_image_print.py")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_with_old_method():
    """مقارنة بين الطريقة القديمة والجديدة"""
    print("\n📊 مقارنة بين الطريقة القديمة والجديدة:")
    print("=" * 60)
    
    comparison = [
        ("الخاصية", "الطريقة القديمة", "الطريقة الجديدة"),
        ("-" * 15, "-" * 20, "-" * 20),
        ("نوع الطباعة", "تحويل إلى صورة", "طباعة نصية مباشرة"),
        ("السرعة", "بطيئة", "سريعة"),
        ("جودة الطباعة", "متوسطة", "عالية"),
        ("استهلاك الذاكرة", "عالي", "منخفض"),
        ("التوافق", "محدود", "شامل"),
        ("دعم العربية", "مشاكل أحياناً", "ممتاز"),
        ("سهولة الصيانة", "معقدة", "بسيطة"),
        ("حجم الملف", "كبير", "صغير"),
        ("المتطلبات", "PIL, win32ui", "مكتبات أساسية فقط")
    ]
    
    for row in comparison:
        print(f"{row[0]:<15} | {row[1]:<20} | {row[2]:<20}")
    
    print("=" * 60)

if __name__ == "__main__":
    print("🚀 بدء اختبار وحدة الطباعة الحرارية المحسنة")
    print("تاريخ الاختبار:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # تشغيل الاختبار
    success = test_thermal_printing()
    
    # عرض المقارنة
    compare_with_old_method()
    
    if success:
        print("\n🎉 تم الاختبار بنجاح!")
        print("يمكنك الآن استخدام thermal1_image_print.py للطباعة المحسنة")
    else:
        print("\n❌ فشل الاختبار!")
        print("تحقق من الأخطاء وحاول مرة أخرى")
    
    input("\nاضغط Enter للخروج...")
