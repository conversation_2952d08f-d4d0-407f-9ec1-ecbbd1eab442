import re

def clean_css_from_spa_file():
    """إزالة الأنماط المدمجة من ملف run_spa_system.py"""
    try:
        # قراءة الملف
        with open('run_spa_system.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"حجم الملف الأصلي: {len(content)} حرف")
        
        # البحث عن بداية ونهاية الأنماط المدمجة باستخدام regex أبسط
        # نبحث عن كل شيء بين link stylesheet وحتى </style>
        pattern = r'(<link rel="stylesheet" href="modern_system_styles\.css">)\s*\n.*?</style>'
        
        # استبدال المنطقة بين البداية والنهاية
        new_content = re.sub(pattern, r'\1', content, flags=re.DOTALL)
        
        print(f"حجم الملف بعد التنظيف: {len(new_content)} حرف")
        print(f"تم توفير: {len(content) - len(new_content)} حرف")
        
        # كتابة الملف المُحدث
        with open('run_spa_system.py', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print('✅ تم تنظيف الأنماط المدمجة بنجاح!')
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تنظيف الملف: {str(e)}")
        return False

if __name__ == "__main__":
    clean_css_from_spa_file()
