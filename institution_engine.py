"""
محرك بيانات المؤسسة - نسخة محسنة
يحتوي على جميع دوال إدارة بيانات المؤسسة مع الطريقة الصحيحة
"""

import os
import json
import sqlite3
from pathlib import Path
from PyQt5.QtWidgets import QFileDialog, QMessageBox
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal, QDateTime
from PyQt5.QtGui import QPixmap

class InstitutionEngine(QObject):
    """محرك بيانات المؤسسة - مسؤول عن كل معالجة بيانات المؤسسة والتفاعل مع قاعدة البيانات"""
    
    # إشارات للتواصل مع واجهة HTML
    logUpdated = pyqtSignal(str, str, str)  # message, status, timestamp
    dataUpdated = pyqtSignal(str)  # institution data JSON
    logoUpdated = pyqtSignal(str)  # logo path
    
    def __init__(self, parent_window=None):
        super().__init__()
        self.db_path = "data.db"
        self.parent_window = parent_window
        self.setup_database()
    
    def emit_log(self, message, status="info"):
        """إرسال رسالة إلى واجهة HTML"""
        timestamp = QDateTime.currentDateTime().toString("hh:mm:ss")
        self.logUpdated.emit(message, status, timestamp)
    
    def setup_database(self):
        """إنشاء جدول البيانات في قاعدة البيانات إذا لم يكن موجودًا"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('''CREATE TABLE IF NOT EXISTS بيانات_المؤسسة (
                الأكاديمية TEXT,
                المديرية TEXT,
                الجماعة TEXT,
                المؤسسة TEXT,
                السنة_الدراسية TEXT,
                البلدة TEXT,
                المدير TEXT,
                الحارس_العام TEXT,
                السلك TEXT,
                رقم_الحراسة TEXT,
                رقم_التسجيل TEXT,
                الأسدس TEXT,
                ImagePath1 TEXT
            )''')

            # التحقق من وجود سجلات في الجدول
            cursor.execute("SELECT COUNT(*) FROM بيانات_المؤسسة")
            count = cursor.fetchone()[0]

            # إذا لم يكن هناك سجلات، قم بإنشاء سجل فارغ
            if count == 0:
                cursor.execute("""
                    INSERT INTO بيانات_المؤسسة (
                        الأكاديمية, المديرية, الجماعة, المؤسسة,
                        السنة_الدراسية, البلدة, المدير, الحارس_العام,
                        السلك, رقم_الحراسة, رقم_التسجيل, الأسدس, ImagePath1
                    ) VALUES (
                        '', '', '', '',
                        '', '', '', '',
                        '', '', '', '', ''
                    )
                """)
                self.emit_log("تم إنشاء سجل فارغ في جدول بيانات_المؤسسة", "info")

            conn.commit()
            conn.close()
        except Exception as e:
            self.emit_log(f"خطأ في إعداد قاعدة البيانات: {e}", "error")

    @pyqtSlot(result=str)
    def getInstitutionData(self):
        """الحصول على بيانات المؤسسة كـ JSON"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود سجلات في الجدول
            cursor.execute("SELECT COUNT(*) FROM بيانات_المؤسسة")
            count = cursor.fetchone()[0]

            if count > 0:
                # استعلام لاسترجاع البيانات من أول سجل فقط
                cursor.execute("SELECT * FROM بيانات_المؤسسة LIMIT 1")
                row = cursor.fetchone()

                if row:
                    # استخراج أسماء الأعمدة
                    cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
                    columns = [info[1] for info in cursor.fetchall()]

                    # تحويل البيانات إلى قاموس
                    data = {}
                    for i, col_name in enumerate(columns):
                        if i < len(row):
                            data[col_name] = row[i] if row[i] else ""

                    conn.close()
                    return json.dumps(data, ensure_ascii=False)
            
            conn.close()
            # إرجاع بيانات فارغة إذا لم توجد سجلات
            return json.dumps({
                "الأكاديمية": "",
                "المديرية": "",
                "الجماعة": "",
                "المؤسسة": "",
                "السنة_الدراسية": "",
                "البلدة": "",
                "المدير": "",
                "الحارس_العام": "",
                "السلك": "",
                "رقم_الحراسة": "",
                "رقم_التسجيل": "",
                "الأسدس": "",
                "ImagePath1": ""
            }, ensure_ascii=False)

        except Exception as e:
            self.emit_log(f"خطأ في تحميل بيانات المؤسسة: {str(e)}", "error")
            return json.dumps({"error": str(e)}, ensure_ascii=False)

    @pyqtSlot(str, result=str)
    def saveInstitutionData(self, dataJson):
        """حفظ بيانات المؤسسة"""
        try:
            data = json.loads(dataJson)
            
            # التحقق من كود التفعيل قبل الحفظ
            if not self.verify_activation_code(data):
                return json.dumps({
                    "success": False,
                    "message": "كود التفعيل غير صحيح. لا يمكن حفظ البيانات!\nالرجاء التأكد من رقم التسجيل المدخل."
                }, ensure_ascii=False)

            # التحقق من حقل البلدة - إلزامي
            if not data.get("البلدة", "").strip():
                return json.dumps({
                    "success": False,
                    "message": "حقل البلدة فارغ\nيجب إدخال اسم البلدة قبل حفظ البيانات."
                }, ensure_ascii=False)

            # التحقق من وجود شعار المؤسسة - إلزامي
            if not self.check_logo_exists():
                return json.dumps({
                    "success": False,
                    "message": "شعار المؤسسة غير موجود\nيجب تحميل شعار المؤسسة قبل حفظ البيانات."
                }, ensure_ascii=False)

            # التحقق من وجود سجل على الأقل قبل التحديث
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT COUNT(*) FROM بيانات_المؤسسة")
            count = cursor.fetchone()[0]

            if count == 0:
                # إذا كان الجدول فارغاً، نقوم بإنشاء سجل جديد أولاً
                cursor.execute("INSERT INTO بيانات_المؤسسة DEFAULT VALUES")
                conn.commit()

            # بناء استعلام التحديث
            update_columns = []
            update_values = []

            for field, value in data.items():
                if field in ["الأكاديمية", "المديرية", "الجماعة", "المؤسسة", "السنة_الدراسية", 
                           "البلدة", "المدير", "الحارس_العام", "السلك", "رقم_الحراسة", 
                           "رقم_التسجيل", "الأسدس"]:
                    update_columns.append(f"{field} = ?")
                    update_values.append(value)

            if update_columns:
                update_query = f"UPDATE بيانات_المؤسسة SET {', '.join(update_columns)} WHERE rowid=1"
                cursor.execute(update_query, update_values)
                conn.commit()

            conn.close()

            self.emit_log("تم حفظ بيانات المؤسسة بنجاح", "success")
            self.dataUpdated.emit(self.getInstitutionData())
            
            return json.dumps({
                "success": True,
                "message": "تم حفظ بيانات المؤسسة بنجاح"
            }, ensure_ascii=False)

        except Exception as e:
            self.emit_log(f"خطأ في حفظ بيانات المؤسسة: {str(e)}", "error")
            return json.dumps({
                "success": False,
                "message": f"حدث خطأ أثناء حفظ البيانات: {str(e)}"
            }, ensure_ascii=False)

    def verify_activation_code(self, data):
        """التحقق من كود التفعيل باستخدام المعادلة المحددة"""
        try:
            # الحصول على رمز المؤسسة
            institution_code = self.generate_institution_code(data)
            
            # التأكد من أن رمز المؤسسة عبارة عن رقم
            if not institution_code.isdigit():
                return False

            # الحصول على رقم التسجيل المدخل
            registration_number = data.get("رقم_التسجيل", "").strip()

            # التأكد من أن رقم التسجيل عبارة عن رقم
            if not registration_number.isdigit():
                return False

            # تطبيق المعادلة: (رمز المؤسسة * 98) + (أول 3 أرقام من رمز المؤسسة * 71)
            school_code_int = int(institution_code)
            first_three_digits = int(institution_code[:3])

            expected_registration = (school_code_int * 98) + (first_three_digits * 71)

            # التحقق مما إذا كان رقم التسجيل المدخل يطابق النتيجة المتوقعة
            return int(registration_number) == expected_registration

        except Exception as e:
            self.emit_log(f"خطأ في التحقق من كود التفعيل: {str(e)}", "error")
            return False

    def generate_institution_code(self, data):
        """توليد رقم المؤسسة بناءً على البيانات"""
        try:
            academy = data.get("الأكاديمية", "") or "غير متوفر"
            directorate = data.get("المديرية", "") or "غير متوفر"
            school = data.get("المؤسسة", "") or "غير متوفر"

            # توليد رقم فريد 10 أرقام بناءً على بيانات المؤسسة
            combined_text = f"{academy}-{directorate}-{school}"

            # حساب قيمة هاش ثابتة باستخدام خوارزمية بسيطة
            hash_value = 0
            for char in combined_text:
                hash_value = (hash_value * 31 + ord(char)) & 0xFFFFFFFF

            # تأكد من أن الرقم يكون دائمًا 10 أرقام
            numeric_code = hash_value % 10000000000
            if numeric_code < 1000000000:  # إذا كان أقل من 10 أرقام
                numeric_code += 1000000000  # أضف 1 في أول خانة

            return f"{numeric_code:010d}"

        except Exception as e:
            self.emit_log(f"خطأ في توليد رمز المؤسسة: {str(e)}", "error")
            return "0000000000"

    @pyqtSlot(result=str)
    def getInstitutionCode(self):
        """الحصول على رمز المؤسسة"""
        try:
            data_json = self.getInstitutionData()
            data = json.loads(data_json)
            
            if "error" in data:
                return json.dumps({"code": "غير متوفر", "error": data["error"]}, ensure_ascii=False)
            
            code = self.generate_institution_code(data)
            return json.dumps({"code": code}, ensure_ascii=False)
            
        except Exception as e:
            self.emit_log(f"خطأ في الحصول على رمز المؤسسة: {str(e)}", "error")
            return json.dumps({"code": "غير متوفر", "error": str(e)}, ensure_ascii=False)

    @pyqtSlot()
    def selectLogo(self):
        """اختيار وتحميل شعار المؤسسة"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self.parent_window, 
                "اختر صورة الشعار", 
                "", 
                "Images (*.png *.jpg *.jpeg *.bmp *.gif)"
            )
            
            if file_path:
                # حفظ مسار الشعار في قاعدة البيانات
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # التأكد من وجود سجل
                cursor.execute("SELECT COUNT(*) FROM بيانات_المؤسسة")
                if cursor.fetchone()[0] == 0:
                    cursor.execute("INSERT INTO بيانات_المؤسسة DEFAULT VALUES")
                
                cursor.execute("UPDATE بيانات_المؤسسة SET ImagePath1=? WHERE rowid=1", (file_path,))
                conn.commit()
                conn.close()
                
                self.emit_log("تم حفظ الشعار بنجاح", "success")
                self.logoUpdated.emit(file_path)
                
        except Exception as e:
            self.emit_log(f"خطأ في تحميل الشعار: {str(e)}", "error")

    @pyqtSlot(result=str)
    def getLogoPath(self):
        """الحصول على مسار الشعار"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة WHERE rowid=1")
            result = cursor.fetchone()
            conn.close()
            
            if result and result[0] and os.path.exists(result[0]):
                return json.dumps({"path": result[0], "exists": True}, ensure_ascii=False)
            else:
                return json.dumps({"path": "", "exists": False}, ensure_ascii=False)
                
        except Exception as e:
            self.emit_log(f"خطأ في الحصول على مسار الشعار: {str(e)}", "error")
            return json.dumps({"path": "", "exists": False, "error": str(e)}, ensure_ascii=False)

    def check_logo_exists(self):
        """التحقق من وجود الشعار"""
        try:
            logo_info = json.loads(self.getLogoPath())
            return logo_info.get("exists", False)
        except:
            return False

    @pyqtSlot(result=str)
    def getAcademicYears(self):
        """الحصول على السنوات الدراسية من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # استعلام للحصول على السنوات الدراسية الفريدة مرتبة تنازليًا
            cursor.execute("SELECT DISTINCT السنة_الدراسية FROM البنية_التربوية ORDER BY السنة_الدراسية DESC")
            years = cursor.fetchall()
            conn.close()

            year_list = ["اختر السنة"]
            for year in years:
                if year[0]:  # تأكد من أن القيمة ليست فارغة
                    year_list.append(year[0])

            return json.dumps({"years": year_list}, ensure_ascii=False)

        except Exception as e:
            self.emit_log(f"خطأ في تحميل السنوات الدراسية: {str(e)}", "error")
            return json.dumps({"years": ["اختر السنة"], "error": str(e)}, ensure_ascii=False)

    @pyqtSlot()
    def refreshData(self):
        """تحديث جميع البيانات"""
        try:
            self.emit_log("جاري تحديث بيانات المؤسسة...", "progress")
            
            # إعادة تحميل البيانات وإرسال الإشارات
            self.dataUpdated.emit(self.getInstitutionData())
            
            # تحديث مسار الشعار
            logo_info = json.loads(self.getLogoPath())
            if logo_info.get("exists", False):
                self.logoUpdated.emit(logo_info.get("path", ""))
            
            self.emit_log("تم تحديث بيانات المؤسسة بنجاح", "success")
            
        except Exception as e:
            self.emit_log(f"خطأ في تحديث البيانات: {str(e)}", "error")

    @pyqtSlot(result=str)
    def getInstitutionInfo(self):
        """عرض معلومات المؤسسة للعرض"""
        try:
            data_json = self.getInstitutionData()
            data = json.loads(data_json)
            
            if "error" in data:
                return json.dumps({
                    "success": False,
                    "message": "لم يتم العثور على بيانات المؤسسة. الرجاء إدخال البيانات أولاً."
                }, ensure_ascii=False)

            # استخراج رمز المؤسسة
            institution_code = self.generate_institution_code(data)

            info = {
                "academy": data.get("الأكاديمية", "") or "غير متوفر",
                "directorate": data.get("المديرية", "") or "غير متوفر", 
                "school": data.get("المؤسسة", "") or "غير متوفر",
                "institution_code": institution_code,
                "registration_code": data.get("رقم_التسجيل", "") or "غير متوفر"
            }

            return json.dumps({
                "success": True,
                "info": info
            }, ensure_ascii=False)

        except Exception as e:
            self.emit_log(f"خطأ في الحصول على معلومات المؤسسة: {str(e)}", "error")
            return json.dumps({
                "success": False,
                "message": f"حدث خطأ أثناء استرجاع بيانات المؤسسة: {str(e)}"
            }, ensure_ascii=False)
