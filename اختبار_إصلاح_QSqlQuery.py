#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة QSqlQuery مع sqlite3.Connection
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon

def test_qsqlquery_fix():
    """اختبار إصلاح مشكلة QSqlQuery"""
    print("🔧 اختبار إصلاح مشكلة QSqlQuery مع sqlite3.Connection")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    
    try:
        from main_window import MainWindow
        print("✅ تم استيراد MainWindow")
        
        main_window = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # فحص إصلاح مشكلة QSqlQuery
        def check_qsqlquery_fix():
            print("\n🔍 فحص إصلاح مشكلة QSqlQuery:")
            
            # التحقق من تبويب اللوائح والأقسام
            if "lists_sections" in main_window.windows:
                window = main_window.windows["lists_sections"]
                print(f"   📄 نوع النافذة: {type(window).__name__}")
                
                # التحقق من أنها ليست PlaceholderWindow
                if "PlaceholderWindow" in type(window).__name__:
                    print("   ❌ النافذة هي PlaceholderWindow - لم يتم تحميل sub4_window بشكل صحيح")
                    print("   💡 تحقق من رسائل الخطأ في وحدة التحكم")
                    
                else:
                    print("   ✅ النافذة من النوع الصحيح (Sub4Window)")
                    
                    # اختبار وظائف النافذة
                    print("\n🧪 اختبار وظائف النافذة:")
                    
                    # التحقق من وجود قاعدة البيانات
                    if hasattr(window, 'db'):
                        print("   ✅ النافذة تحتوي على اتصال قاعدة البيانات")
                        print(f"   📄 نوع قاعدة البيانات: {type(window.db)}")
                        
                        # اختبار دالة التحقق من جدول اللوائح
                        try:
                            print("   🧪 اختبار دالة check_regulations_table...")
                            result = window.check_regulations_table()
                            print(f"   ✅ دالة check_regulations_table تعمل: {result}")
                        except Exception as e:
                            if "QSqlQuery" in str(e):
                                print(f"   ❌ لا يزال هناك خطأ QSqlQuery: {e}")
                            else:
                                print(f"   ⚠️ خطأ آخر في check_regulations_table: {e}")
                        
                        # اختبار تحميل البيانات
                        try:
                            print("   🧪 اختبار تحميل البيانات...")
                            window.load_initial_data()
                            print("   ✅ تم تحميل البيانات بنجاح (لا توجد أخطاء QSqlQuery)")
                        except Exception as e:
                            if "QSqlQuery" in str(e):
                                print(f"   ❌ لا يزال هناك خطأ QSqlQuery: {e}")
                            else:
                                print(f"   ⚠️ خطأ آخر في تحميل البيانات: {e}")
                    else:
                        print("   ❌ النافذة لا تحتوي على اتصال قاعدة البيانات")
                        
            else:
                print("   ❌ النافذة غير موجودة في قاموس النوافذ")
            
            # اختبار النقر على التبويب
            print("\n🎯 اختبار النقر على تبويب اللوائح والأقسام:")
            
            # البحث عن التبويب
            lists_sections_index = -1
            tab_count = main_window.tabWidget.count()
            
            for i in range(tab_count):
                tab_text = main_window.tabWidget.tabText(i)
                tab_data = main_window.tabWidget.tabData(i)
                
                if tab_text == "اللوائح والأقسام" or tab_data == "lists_sections":
                    lists_sections_index = i
                    print(f"   ✅ تم العثور على التبويب في الفهرس {i}")
                    break
            
            if lists_sections_index >= 0:
                print("   🎯 محاولة النقر على التبويب...")
                
                # محاكاة النقر على التبويب
                main_window.tabWidget.setCurrentIndex(lists_sections_index)
                current_index = main_window.tabWidget.currentIndex()
                
                if current_index == lists_sections_index:
                    print("   ✅ تم التنقل للتبويب بنجاح!")
                    print("   📋 راقب وحدة التحكم للتأكد من عدم ظهور أخطاء QSqlQuery")
                else:
                    print("   ❌ فشل في التنقل للتبويب")
            else:
                print("   ❌ لم يتم العثور على التبويب")
        
        # فحص الإصلاح بعد ثانية واحدة
        QTimer.singleShot(1000, check_qsqlquery_fix)
        
        main_window.show()
        main_window.setWindowTitle("اختبار إصلاح QSqlQuery - sub4_window")
        
        print("\n📋 تعليمات الاختبار:")
        print("1. راقب رسائل وحدة التحكم")
        print("2. ابحث عن أي رسائل خطأ تحتوي على 'QSqlQuery'")
        print("3. انقر على تبويب 'اللوائح والأقسام'")
        print("4. تحقق من ظهور البيانات في النافذة")
        print("5. جرب تحديد مستوى وقسم")
        
        print("\n🔧 الإصلاحات المطبقة:")
        print("   ✅ تحويل check_regulations_table لاستخدام sqlite3")
        print("   ✅ تحويل التحقق من جدول البنية التربوية لاستخدام sqlite3")
        print("   ✅ إزالة استدعاءات QSqlQuery مع sqlite3.Connection")
        print("   ✅ استخدام cursor.execute() بدلاً من QSqlQuery")
        
        print("\n📊 النتائج المتوقعة:")
        print("   ✅ لا توجد رسائل خطأ عن 'QSqlQuery'")
        print("   ✅ النافذة من نوع Sub4Window وليس PlaceholderWindow")
        print("   ✅ تحميل البيانات يعمل بدون أخطاء")
        print("   ✅ ظهور البيانات في جداول النافذة")
        
        print("\n⚠️ ملاحظات:")
        print("   • QSqlQuery يعمل فقط مع QSqlDatabase")
        print("   • sqlite3.Connection يحتاج cursor.execute()")
        print("   • تم إصلاح الدوال الأساسية فقط")
        print("   • قد تحتاج دوال أخرى لإصلاحات مماثلة")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return 1

def show_fix_summary():
    """عرض ملخص الإصلاحات"""
    print("\n" + "=" * 60)
    print("🔧 ملخص إصلاحات مشكلة QSqlQuery")
    print("=" * 60)
    
    fixes = [
        ("المشكلة الأصلية", "QSqlQuery(): argument 'db' has unexpected type 'sqlite3.Connection'"),
        ("السبب", "استخدام QSqlQuery مع sqlite3.Connection بدلاً من QSqlDatabase"),
        ("الحل", "تحويل الكود لاستخدام sqlite3 cursor مباشرة"),
        ("الدوال المصلحة", "check_regulations_table, update_levels_model"),
        ("النتيجة", "sub4_window يعمل ويظهر البيانات")
    ]
    
    print("\n🔧 تفاصيل الإصلاحات:")
    for title, detail in fixes:
        print(f"   {title}: {detail}")
    
    print("\n📝 التغييرات المطبقة:")
    print("   قبل: check_query = QSqlQuery(db=self.db)")
    print("   بعد: cursor = self.db.cursor()")
    print()
    print("   قبل: check_query.exec_('SELECT ...')")
    print("   بعد: cursor.execute('SELECT ...')")
    print()
    print("   قبل: result = check_query.next()")
    print("   بعد: result = cursor.fetchone()")

if __name__ == "__main__":
    print("🔧 مرحباً بك في اختبار إصلاح مشكلة QSqlQuery!")
    
    # عرض ملخص الإصلاحات
    show_fix_summary()
    
    print("\n" + "=" * 60)
    print("🚀 بدء تشغيل الاختبار...")
    print("=" * 60)
    
    # تشغيل الاختبار
    exit_code = test_qsqlquery_fix()
    
    print(f"\n🔚 انتهى الاختبار بكود الخروج: {exit_code}")
    sys.exit(exit_code)
