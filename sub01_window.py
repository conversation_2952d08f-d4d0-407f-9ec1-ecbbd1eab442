# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
                           QGraphicsDropShadowEffect)
from PyQt5.QtGui import QFont, QColor, QIcon, QPixmap
from PyQt5.QtCore import Qt

class Sub01Window(QWidget):
    """نافذة المعين في الحراسة العامة"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🎓المعين في الحراسة العامة ")
        self.setFixedSize(1000, 600)
        self.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج إلى النافذة
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # تعريف مسار قاعدة البيانات
        self.db_path = "data.db"

        # إعداد واجهة المستخدم
        self.setup_ui()

        # تحميل البيانات من قاعدة البيانات
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(5)

        # إنشاء مربع تسمية "المعين في الحراسة العامة"
        self.title_label = QLabel("🎓 المعين في الحراسة العامة ")
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setFont(QFont("Calibri", 24, QFont.Bold))
        self.title_label.setStyleSheet("""
            background-color: #5C83B4;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px;
        """)

        # إضافة تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setXOffset(5)
        shadow.setYOffset(5)
        shadow.setColor(QColor(0, 0, 0, 150))
        self.title_label.setGraphicsEffect(shadow)

        # إنشاء مربع تسمية "السلك" كعنوان ثابت
        self.cycle_label = QLabel("نظام متكامل وذكي بواجهة حديثة لإدارة بيانات التلاميذ بكفاءة واحتراف")
        self.cycle_label.setAlignment(Qt.AlignCenter)
        self.cycle_label.setFont(QFont("Calibri", 18, QFont.Bold))
        self.cycle_label.setWordWrap(True)  # السماح بتقسيم النص على أسطر متعددة
        self.cycle_label.setMinimumHeight(80)  # تحديد ارتفاع أدنى
        self.cycle_label.setStyleSheet("""
            background-color: #5C83B4;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 5px;
        """)

        # إضافة تأثير الظل
        shadow2 = QGraphicsDropShadowEffect()
        shadow2.setBlurRadius(15)
        shadow2.setXOffset(5)
        shadow2.setYOffset(5)
        shadow2.setColor(QColor(0, 0, 0, 150))
        self.cycle_label.setGraphicsEffect(shadow2)

        # إنشاء مربع تسمية "المؤسسة" - سيتم تحديثه من قاعدة البيانات
        self.institution_label = QLabel("")
        self.institution_label.setAlignment(Qt.AlignCenter)
        self.institution_label.setFont(QFont("Calibri", 22, QFont.Bold))
        self.institution_label.setStyleSheet("""
            background-color: white;
            color: black;
            padding: 12px;
            border-radius: 8px;
            border: 2px solid #5C83B4;
            margin: 5px;
        """)

        # إضافة تأثير الظل
        shadow3 = QGraphicsDropShadowEffect()
        shadow3.setBlurRadius(15)
        shadow3.setXOffset(5)
        shadow3.setYOffset(5)
        shadow3.setColor(QColor(0, 0, 0, 150))
        self.institution_label.setGraphicsEffect(shadow3)

        # إنشاء مربع تسمية "السنة الدراسية"
        self.academic_year_label = QLabel("")
        self.academic_year_label.setAlignment(Qt.AlignCenter)
        self.academic_year_label.setFont(QFont("Calibri", 22, QFont.Bold))
        self.academic_year_label.setStyleSheet("""
            background-color: white;
            color: black;
            padding: 12px;
            border-radius: 8px;
            border: 2px solid #5C83B4;
            margin: 5px;
        """)

        # إضافة تأثير الظل
        shadow4 = QGraphicsDropShadowEffect()
        shadow4.setBlurRadius(15)
        shadow4.setXOffset(5)
        shadow4.setYOffset(5)
        shadow4.setColor(QColor(0, 0, 0, 150))
        self.academic_year_label.setGraphicsEffect(shadow4)

        # إنشاء مربع تسمية "الهاتف"
        self.phone_label = QLabel("")
        self.phone_label.setAlignment(Qt.AlignCenter)
        self.phone_label.setFont(QFont("Calibri", 22, QFont.Bold))
        self.phone_label.setStyleSheet("""
            background-color: white;
            color: black;
            padding: 12px;
            border-radius: 8px;
            border: 2px solid #5C83B4;
            margin: 5px;
        """)

        # إضافة تأثير الظل
        shadow5 = QGraphicsDropShadowEffect()
        shadow5.setBlurRadius(15)
        shadow5.setXOffset(5)
        shadow5.setYOffset(5)
        shadow5.setColor(QColor(0, 0, 0, 150))
        self.phone_label.setGraphicsEffect(shadow5)

        # إنشاء مربع تسمية "رقم التسجيل"
        self.registration_label = QLabel("")
        self.registration_label.setAlignment(Qt.AlignCenter)
        self.registration_label.setFont(QFont("Calibri", 22, QFont.Bold))
        self.registration_label.setStyleSheet("""
            background-color: white;
            color: black;
            padding: 12px;
            border-radius: 8px;
            border: 2px solid #5C83B4;
            margin: 5px;
        """)

        # إضافة تأثير الظل
        shadow6 = QGraphicsDropShadowEffect()
        shadow6.setBlurRadius(15)
        shadow6.setXOffset(5)
        shadow6.setYOffset(5)
        shadow6.setColor(QColor(0, 0, 0, 150))
        self.registration_label.setGraphicsEffect(shadow6)

        # إنشاء مربع تسمية "الأسدس"
        self.semester_label = QLabel("")
        self.semester_label.setAlignment(Qt.AlignCenter)
        self.semester_label.setFont(QFont("Calibri", 22, QFont.Bold))
        self.semester_label.setStyleSheet("""
            background-color: white;
            color: black;
            padding: 12px;
            border-radius: 8px;
            border: 2px solid #5C83B4;
            margin: 5px;
        """)

        # إضافة تأثير الظل
        shadow7 = QGraphicsDropShadowEffect()
        shadow7.setBlurRadius(15)
        shadow7.setXOffset(5)
        shadow7.setYOffset(5)
        shadow7.setColor(QColor(0, 0, 0, 150))
        self.semester_label.setGraphicsEffect(shadow7)



        # إضافة العناصر إلى التخطيط الرئيسي - المؤسسة والسنة الدراسية والأسدس ورقم التسجيل
        main_layout.addWidget(self.title_label)
        main_layout.addWidget(self.cycle_label)
        main_layout.addWidget(self.institution_label)
        main_layout.addWidget(self.academic_year_label)
        main_layout.addWidget(self.semester_label)
        main_layout.addWidget(self.registration_label)

        # إضافة مساحة فارغة في الأسفل
        main_layout.addStretch()

    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            # التحقق من وجود قاعدة البيانات
            if not os.path.exists(self.db_path):
                print(f"خطأ: ملف قاعدة البيانات '{self.db_path}' غير موجود.")
                # إخفاء البيانات عدم عرضها
                self.hide_all_data()
                return

            # فتح اتصال بقاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # استعلام للحصول على بيانات المؤسسة (المؤسسة والسنة الدراسية والأسدس ورقم التسجيل)
            cursor.execute("""
                SELECT المؤسسة, السنة_الدراسية, الأسدس, رقم_التسجيل
                FROM بيانات_المؤسسة LIMIT 1
            """)
            result = cursor.fetchone()

            if result:
                # تحديث مربعات التسمية بالبيانات المسترجعة - عرض المؤسسة والسنة الدراسية والأسدس ورقم التسجيل
                institution_value = result[0] if result[0] else ""
                academic_year = result[1] if result[1] else ""
                semester_value = result[2] if result[2] else ""
                registration_number = result[3] if result[3] else ""

                # السلك يبقى كعنوان ثابت
                self.cycle_label.setText("نظام متكامل وذكي بواجهة حديثة لإدارة بيانات التلاميذ بكفاءة واحتراف")
                
                # عرض المؤسسة إذا كانت موجودة
                if institution_value:
                    self.institution_label.setText(institution_value)
                    self.institution_label.show()
                else:
                    self.institution_label.hide()
                
                # عرض السنة الدراسية إذا كانت موجودة
                if academic_year:
                    self.academic_year_label.setText("السنة الدراسية : " + academic_year)
                    self.academic_year_label.show()
                else:
                    self.academic_year_label.hide()
                
                # عرض الأسدس إذا كان موجوداً
                if semester_value:
                    self.semester_label.setText("الأسدس : " + semester_value)
                    self.semester_label.show()
                else:
                    self.semester_label.hide()
                
                # عرض رقم التسجيل إذا كان موجوداً مع خلفية خضراء
                if registration_number:
                    self.registration_label.setText("رقم تسجيل البرنامج : " + registration_number)
                    self.registration_label.setStyleSheet("""
                        background-color: #4CAF50;
                        color: white;
                        padding: 12px;
                        border-radius: 8px;
                        border: 2px solid #45a049;
                        margin: 5px;
                    """)
                    self.registration_label.show()
                else:
                    self.registration_label.hide()
                
                # إخفاء البيانات الأخرى
                self.phone_label.hide()
            else:
                print("لم يتم العثور على بيانات المؤسسة.")
                # إخفاء جميع البيانات
                self.hide_all_data()

            # إغلاق الاتصال بقاعدة البيانات
            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل البيانات: {str(e)}")
            # إخفاء جميع البيانات في حالة الخطأ
            self.hide_all_data()
    
    def hide_all_data(self):
        """إخفاء المؤسسة والسنة الدراسية والأسدس ورقم التسجيل عندما لا توجد بيانات في قاعدة البيانات"""
        self.institution_label.hide()
        self.academic_year_label.hide()
        self.semester_label.hide()
        self.registration_label.hide()
        # إخفاء البيانات الأخرى أيضاً للتأكد
        self.phone_label.hide()

    def refresh_data(self):
        """تحديث البيانات من قاعدة البيانات"""
        self.load_data()
    
    def show_all_data(self):
        """إظهار المؤسسة والسنة الدراسية والأسدس ورقم التسجيل فقط"""
        self.institution_label.show()
        self.academic_year_label.show()
        self.semester_label.show()
        self.registration_label.show()
        # البيانات الأخرى تبقى مخفية
        self.phone_label.hide()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    window = Sub01Window()
    window.show()
    sys.exit(app.exec_())
