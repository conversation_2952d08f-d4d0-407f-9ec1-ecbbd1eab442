"""
وحدة للرسائل المخصصة ذات التصميم الجميل
"""
from PyQt5.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QPushButton
)
from PyQt5.QtGui import QFont, QIcon
from PyQt5.QtCore import Qt, QSize

def show_custom_message(parent, message, title="رسالة", message_type="success"):
    """عرض رسالة مخصصة بتصميم جميل

    Args:
        parent: العنصر الأب للرسالة
        message (str): نص الرسالة
        title (str): عنوان الرسالة
        message_type (str): نوع الرسالة ('success', 'warning', 'error', 'question')

    Returns:
        bool: True إذا تم النقر على زر "نعم" أو "موافق"، False خلاف ذلك
    """
    # إنشاء نافذة حوار مخصصة
    dialog = QDialog(parent)
    dialog.setWindowTitle(title)
    dialog.setFixedSize(450, 250)
    dialog.setLayoutDirection(Qt.RightToLeft)

    # إضافة أيقونة البرنامج
    try:
        app_icon = QIcon("01.ico")
        dialog.setWindowIcon(app_icon)
    except Exception as e:
        print(f"خطأ في تحميل أيقونة البرنامج: {e}")

    # تحديد الألوان حسب نوع الرسالة
    if message_type == "success":
        bg_color = "#e8f5e9"  # أخضر فاتح
        border_color = "#4caf50"  # أخضر
        icon_name = "SP_DialogApplyButton"
        button_bg = "#4caf50"
        button_hover = "#388e3c"
    elif message_type == "warning":
        bg_color = "#ffecb3"  # أصفر فاتح أكثر تميزًا
        border_color = "#ff9800"  # برتقالي
        icon_name = "SP_MessageBoxWarning"
        button_bg = "#ff9800"  # برتقالي للزر
        button_hover = "#f57c00"  # برتقالي داكن عند المرور
    elif message_type == "error":
        bg_color = "#ffebee"  # أحمر فاتح
        border_color = "#f44336"  # أحمر
        icon_name = "SP_MessageBoxCritical"
        button_bg = "#f44336"
        button_hover = "#d32f2f"
    elif message_type == "question":
        bg_color = "#e3f2fd"  # أزرق فاتح
        border_color = "#2196f3"  # أزرق
        icon_name = "SP_MessageBoxQuestion"
        button_bg = "#2196f3"
        button_hover = "#1976d2"
    else:
        bg_color = "#e8f5e9"
        border_color = "#4caf50"
        icon_name = "SP_DialogApplyButton"
        button_bg = "#4caf50"
        button_hover = "#388e3c"

    # تنسيق النافذة
    dialog.setStyleSheet(f"""
        QDialog {{
            background-color: {bg_color};
            border: 2px solid {border_color};
            border-radius: 10px;
        }}
        QLabel {{
            color: #2c3e50;
            font-weight: bold;
        }}
        QPushButton {{
            background-color: {button_bg};
            color: white;
            border-radius: 5px;
            padding: 8px 16px;
            font-weight: bold;
            min-height: 40px;
            min-width: 120px;
        }}
        QPushButton:hover {{
            background-color: {button_hover};
        }}
    """)

    # إنشاء تخطيط النافذة
    layout = QVBoxLayout(dialog)
    layout.setContentsMargins(20, 20, 20, 20)
    layout.setSpacing(15)

    # إضافة أيقونة النجاح
    icon_label = QLabel()
    icon_label.setAlignment(Qt.AlignCenter)

    # محاولة تحميل أيقونة النجاح
    try:
        # استخدام أيقونة النظام المناسبة
        system_icon = parent.style().standardIcon(
            getattr(parent.style(), icon_name)
        ).pixmap(QSize(64, 64))
        icon_label.setPixmap(system_icon)
    except Exception as e:
        print(f"خطأ في تحميل أيقونة الرسالة: {e}")

    layout.addWidget(icon_label)

    # إضافة رسالة النجاح
    message_label = QLabel(message)
    message_label.setAlignment(Qt.AlignCenter)
    message_label.setFont(QFont("Calibri", 14, QFont.Bold))
    message_label.setWordWrap(True)
    layout.addWidget(message_label)

    # إضافة الأزرار حسب نوع الرسالة
    buttons_layout = QHBoxLayout()

    if message_type == "question":
        # زر نعم
        yes_button = QPushButton("نعم")
        yes_button.setFont(QFont("Calibri", 12, QFont.Bold))
        yes_button.setCursor(Qt.PointingHandCursor)
        yes_button.clicked.connect(lambda: dialog.done(QDialog.Accepted))

        # زر لا
        no_button = QPushButton("لا")
        no_button.setFont(QFont("Calibri", 12, QFont.Bold))
        no_button.setCursor(Qt.PointingHandCursor)
        no_button.setStyleSheet("""
            QPushButton {
                background-color: #bdbdbd;
                color: white;
            }
            QPushButton:hover {
                background-color: #9e9e9e;
            }
        """)
        no_button.clicked.connect(lambda: dialog.done(QDialog.Rejected))

        buttons_layout.addWidget(yes_button)
        buttons_layout.addWidget(no_button)
    else:
        # زر موافق
        ok_button = QPushButton("موافق")
        ok_button.setFont(QFont("Calibri", 12, QFont.Bold))
        ok_button.setCursor(Qt.PointingHandCursor)
        ok_button.clicked.connect(dialog.accept)
        buttons_layout.addWidget(ok_button)

    layout.addLayout(buttons_layout)

    # عرض النافذة
    result = dialog.exec_()
    return result == QDialog.Accepted
