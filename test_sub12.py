#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نافذة المخالفات الحديثة
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

try:
    from PyQt5.QtWebEngineWidgets import QWebEngineView
    print("✅ QWebEngineView متاحة")
except ImportError as e:
    print(f"❌ خطأ في استيراد QWebEngineView: {e}")
    sys.exit(1)

# استيراد نافذة المخالفات
try:
    from sub12_window_html import StudentViolationsWindow
    print("✅ تم استيراد StudentViolationsWindow بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد StudentViolationsWindow: {e}")
    sys.exit(1)

def main():
    """اختبار نافذة المخالفات"""
    try:
        print("🚀 بدء اختبار نافذة المخالفات...")
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("📝 إنشاء نافذة المخالفات...")
        window = StudentViolationsWindow(
            student_code="TEST001",
            student_name="تلميذ تجريبي",
            db=None
        )
        
        print("🖥️ عرض النافذة...")
        window.show()
        window.raise_()
        window.activateWindow()
        
        print("✅ تم فتح النافذة بنجاح!")
        print("💡 لإغلاق النافذة، أغلق النافذة أو اضغط Ctrl+C")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    print(f"🏁 انتهى الاختبار برمز الخروج: {exit_code}")
    sys.exit(exit_code)
