#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إزالة تبويب اللوائح والأقسام نهائياً
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon

def test_lists_sections_removal():
    """اختبار إزالة تبويب اللوائح والأقسام"""
    print("🗑️ اختبار إزالة تبويب اللوائح والأقسام نهائياً")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    
    try:
        from main_window import MainWindow
        print("✅ تم استيراد MainWindow")
        
        main_window = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # فحص إزالة التبويب
        def check_removal():
            print("\n🔍 فحص إزالة تبويب اللوائح والأقسام:")
            
            # فحص التبويبات
            tab_count = main_window.tabWidget.count()
            print(f"📋 عدد التبويبات الحالي: {tab_count}")
            
            lists_sections_found = False
            
            for i in range(tab_count):
                tab_text = main_window.tabWidget.tabText(i)
                tab_data = main_window.tabWidget.tabData(i)
                print(f"   {i}: {tab_text} ({tab_data})")
                
                if "اللوائح والأقسام" in tab_text or tab_data == "lists_sections":
                    lists_sections_found = True
                    print(f"   ❌ تم العثور على تبويب اللوائح والأقسام!")
            
            if not lists_sections_found:
                print("   ✅ تم إزالة تبويب اللوائح والأقسام بنجاح")
            
            # فحص قاموس النوافذ
            print("\n🔍 فحص قاموس النوافذ:")
            if "lists_sections" in main_window.windows:
                print("   ❌ لا يزال lists_sections موجود في قاموس النوافذ")
            else:
                print("   ✅ تم إزالة lists_sections من قاموس النوافذ")
            
            # فحص القائمة المنسدلة
            print("\n🔍 فحص القائمة المنسدلة:")
            if hasattr(main_window, 'dropdown_items'):
                print("📋 أزرار القائمة المنسدلة الحالية:")
                lists_in_dropdown = False
                
                for text, window_key, color in main_window.dropdown_items:
                    print(f"   {text} -> {window_key} ({color})")
                    if window_key == "lists_sections":
                        lists_in_dropdown = True
                        print(f"      ❌ لا يزال lists_sections موجود في القائمة!")
                
                if not lists_in_dropdown:
                    print("   ✅ تم إزالة lists_sections من القائمة المنسدلة")
            
            # فحص navbar_items
            print("\n🔍 فحص navbar_items:")
            lists_in_navbar = False
            
            for text, window_key in main_window.navbar_items:
                if window_key == "lists_sections":
                    lists_in_navbar = True
                    print(f"   ❌ لا يزال lists_sections موجود في navbar_items")
            
            if not lists_in_navbar:
                print("   ✅ تم إزالة lists_sections من navbar_items")
            
            # اختبار القائمة المنسدلة
            print("\n🎯 اختبار القائمة المنسدلة:")
            if hasattr(main_window, '_show_dropdown'):
                main_window._show_dropdown()
                print("   تم إظهار القائمة المنسدلة للاختبار")
                
                # إخفاء بعد 3 ثوان
                QTimer.singleShot(3000, lambda: main_window._hide_dropdown())
            
        # فحص الإزالة بعد ثانية واحدة
        QTimer.singleShot(1000, check_removal)
        
        main_window.show()
        main_window.setWindowTitle("اختبار إزالة اللوائح والأقسام")
        
        print("\n📋 تعليمات الاختبار:")
        print("1. تحقق من عدم وجود تبويب 'اللوائح والأقسام'")
        print("2. انقر على تبويب 'إعدادات البرنامج'")
        print("3. تحقق من عدم وجود زر 'البنية التربوية' في القائمة")
        print("4. تأكد من أن القائمة تحتوي على الأزرار الصحيحة فقط")
        
        print("\n🗑️ ما تم إزالته:")
        print("   ❌ تبويب 'اللوائح والأقسام' من شريط التبويبات")
        print("   ❌ جميع الكود المرتبط بـ sub252_window")
        print("   ❌ lists_sections_window من قاموس النوافذ")
        print("   ❌ إضافة النافذة لمنطقة المحتوى")
        print("   ❌ جميع المراجع لـ lists_sections")
        
        print("\n✅ ما تم الاحتفاظ به:")
        print("   ✅ تبويب 'إعدادات البرنامج'")
        print("   ✅ القائمة المنسدلة مع الأزرار الأخرى")
        print("   ✅ باقي التبويبات والنوافذ")
        
        print("\n📊 النتيجة المتوقعة:")
        print("   • عدد أقل من التبويبات")
        print("   • عدم وجود أخطاء متعلقة بـ lists_sections")
        print("   • القائمة المنسدلة تعمل بدون مشاكل")
        print("   • البرنامج يعمل بشكل طبيعي")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return 1

def show_removal_summary():
    """عرض ملخص الإزالة"""
    print("\n" + "=" * 60)
    print("📋 ملخص إزالة تبويب اللوائح والأقسام")
    print("=" * 60)
    
    removed_items = [
        "تبويب 'اللوائح والأقسام' من navbar_items",
        "كامل قسم إنشاء نافذة اللوائح والأقسام",
        "استيراد sub252_window",
        "lists_sections_window من قاموس النوافذ",
        "إضافة النافذة لمنطقة المحتوى",
        "جميع المراجع والمتغيرات المرتبطة"
    ]
    
    print("\n🗑️ العناصر المحذوفة:")
    for item in removed_items:
        print(f"   ❌ {item}")
    
    remaining_items = [
        "تبويب 'إعدادات البرنامج'",
        "القائمة المنسدلة",
        "الأزرار الأخرى في القائمة",
        "باقي التبويبات والنوافذ"
    ]
    
    print("\n✅ العناصر المحتفظ بها:")
    for item in remaining_items:
        print(f"   ✅ {item}")

if __name__ == "__main__":
    print("🗑️ مرحباً بك في اختبار إزالة اللوائح والأقسام!")
    
    # عرض ملخص الإزالة
    show_removal_summary()
    
    print("\n" + "=" * 60)
    print("🚀 بدء تشغيل الاختبار...")
    print("=" * 60)
    
    # تشغيل الاختبار
    exit_code = test_lists_sections_removal()
    
    print(f"\n🔚 انتهى الاختبار بكود الخروج: {exit_code}")
    sys.exit(exit_code)
