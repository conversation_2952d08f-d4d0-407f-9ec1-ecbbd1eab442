#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import sqlite3
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                           QHBoxLayout, QLabel, QPushButton, QTableView,
                           QMessageBox, QFrame, QHeaderView, QProxyStyle, QStyleOptionViewItem, QStyle,
                           QDialog)
from PyQt5.QtGui import QFont, QIcon, QStandardItemModel, QStandardItem, QBrush, QPixmap
from PyQt5.QtCore import Qt, QDate, QTimer
import os
from datetime import datetime
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
from reportlab.pdfgen import canvas
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from templates_manager import PDFTemplateManager
import traceback

# استيراد وحدة الرسائل المخصصة
from custom_messages import show_custom_message

class ViolationsManagementWindow(QMainWindow):
    def __init__(self, student_code=None, parent=None):
        super().__init__(parent)
        self.student_code = student_code
        self.db_path = "data.db"
        self.selected_class = None  # إضافة متغير القسم المحدد
        self.template_manager = PDFTemplateManager()

        # إضافة خاصية للتحقق مما إذا كانت النافذة مدمجة في التبويب
        self.is_embedded = parent is not None and not isinstance(parent, QMainWindow)

        self.setWindowTitle("معالجة المخالفات")
        self.setGeometry(100, 100, 1200, 600)

        # تعطيل خاصية النقر على النافذة لفتحها مرة أخرى عند دمجها
        if self.is_embedded:
            self.setAttribute(Qt.WA_TransparentForMouseEvents, False)

        # إنشاء Widget مركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # إطار العنوان
        title_frame = QFrame()
        title_layout = QHBoxLayout(title_frame)

        # زر الإغلاق
        self.close_button = QPushButton("×")
        self.close_button.setFont(QFont("Arial", 16, QFont.Bold))
        self.close_button.setCursor(Qt.PointingHandCursor)  # تغيير المؤشر إلى شكل يد
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 15px;
                min-width: 30px;
                min-height: 30px;
                max-width: 30px;
                max-height: 30px;
                padding: 0px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.close_button.clicked.connect(self.close)



        # تحسين الجدول - استبدال الكود القديم بتنفيذ أكثر احترافية
        self.setup_advanced_table()
        main_layout.addWidget(self.violations_table)

        # أزرار التحكم
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)

        self.edit_btn = QPushButton("تعديل مخالفة")
        self.edit_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        self.edit_btn.setCursor(Qt.PointingHandCursor)  # تغيير المؤشر إلى شكل يد
        self.edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
        """)
        self.edit_btn.clicked.connect(self.edit_violation)

        self.delete_btn = QPushButton("حذف مخالفة")
        self.delete_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        self.delete_btn.setCursor(Qt.PointingHandCursor)  # تغيير المؤشر إلى شكل يد
        self.delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.delete_btn.clicked.connect(self.delete_violation)

        self.print_btn = QPushButton("طباعة مخالفة تلميذ")
        self.print_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        self.print_btn.setCursor(Qt.PointingHandCursor)  # تغيير المؤشر إلى شكل يد
        self.print_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.print_btn.clicked.connect(self.print_violation)

        # --- إزالة زر حفظ PDF ---
        # self.save_btn = QPushButton("حفظ PDF")
        # self.save_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        # self.save_btn.setStyleSheet("""
        #     QPushButton {
        #         background-color: #e67e22;
        #         color: white;
        #         border-radius: 4px;
        #         padding: 8px 16px;
        #         font-weight: bold;
        #         min-width: 120px;
        #     }
        #     QPushButton:hover {
        #         background-color: #d35400;
        #     }
        # """)
        # self.save_btn.clicked.connect(self.save_violation_to_pdf)
        # --- نهاية الإزالة ---

        self.student_record_btn = QPushButton("سجل مخالفة التلميذ(ة)") # <-- تم تغيير النص هنا
        self.student_record_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        self.student_record_btn.setCursor(Qt.PointingHandCursor)  # تغيير المؤشر إلى شكل يد
        self.student_record_btn.setStyleSheet("""
            QPushButton {
                background-color: #8e44ad;
                color: white;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #9b59b6;
            }
        """)
        self.student_record_btn.clicked.connect(self.save_record_to_pdf)

        # إضافة الأزرار للتخطيط بالترتيب الصحيح
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.edit_btn)
        buttons_layout.addWidget(self.delete_btn)
        # --- إزالة إضافة زر حفظ PDF ---
        # buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.student_record_btn)
        buttons_layout.addWidget(self.print_btn)

        main_layout.addWidget(buttons_frame)

        # تطبيق النمط
        self.apply_style()

        # Load violations when the window opens
        self.load_violations()

    def apply_style(self):
        """تطبيق النمط على النافذة وعناصرها"""
        self.setStyleSheet("""
            QMainWindow, QWidget {
                background-color: #f5f5f5;
                font-family: 'Calibri';
            }
            QLabel {
                font-size: 13pt;
                font-weight: bold;
                color: black;
            }
        """)

    def setup_advanced_table(self):
        """إنشاء جدول متقدم بمظهر احترافي"""
        # إنشاء الجدول
        self.violations_table = QTableView()
        self.model = QStandardItemModel()
        self.violations_table.setModel(self.model)

        # تعيين خصائص الجدول الأساسية
        self.violations_table.setObjectName("professionalTable")
        self.violations_table.setSortingEnabled(True)  # تمكين الفرز
        self.violations_table.setLayoutDirection(Qt.RightToLeft)  # RTL للغة العربية
        self.violations_table.setCornerButtonEnabled(False)  # إخفاء زر الركن
        self.violations_table.setEditTriggers(QTableView.NoEditTriggers)  # جعل الجدول للقراءة فقط
        self.violations_table.setSelectionBehavior(QTableView.SelectRows)
        self.violations_table.setSelectionMode(QTableView.SingleSelection)
        self.violations_table.horizontalHeader().setStretchLastSection(True)
        self.violations_table.horizontalHeader().setSortIndicatorShown(True)
        self.violations_table.verticalHeader().hide()  # إخفاء رؤوس الصفوف

        # تعيين خط الجدول وحجمه
        table_font = QFont('Calibri', 13)
        self.violations_table.setFont(table_font)

        # تعيين عناوين الأعمدة - تم حذف عمود مربع الاختيار
        headers = [
            "التاريخ",
            "اسم التلميذ",
            "القسم",
            "المستوى",
            "المادة",
            "الأستاذ"
        ]
        self.model.setHorizontalHeaderLabels(headers)

        # تطبيق تنسيق خاص لرؤوس الأعمدة
        header = self.violations_table.horizontalHeader()
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumHeight(45)  # زيادة ارتفاع رأس العمود

        # تعيين عرض الأعمدة المخصص
        column_widths = [120, 180, 120, 200, 130, 150]
        for i, width in enumerate(column_widths):
            self.violations_table.setColumnWidth(i, width)
            header.setSectionResizeMode(i, QHeaderView.Interactive)

        # تعيين النمط الاحترافي للجدول
        self.apply_professional_table_style()

        # تعديل: إضافة وظيفة النقر المزدوج على الصف لتحرير المخالفة
        # فقط عندما تكون النافذة مستقلة وليست مدمجة في تبويب
        self.violations_table.doubleClicked.connect(self.on_table_double_clicked)

    def on_table_double_clicked(self, index):
        """عند النقر المزدوج على الجدول"""
        # فقط إذا كانت النافذة مستقلة (ليست مدمجة في تبويب)
        if not self.is_embedded:
            self.edit_violation()

    def apply_professional_table_style(self):
        """تطبيق نمط احترافي للجدول"""
        # الألوان المستخدمة في التصميم
        header_bg = "#1a237e"       # خلفية العنوان - أزرق غامق
        header_text = "#ffffff"     # لون نص العنوان - أبيض
        row_odd = "#ffffff"         # لون الصف الفردي - أبيض
        row_even = "#f5f7fa"        # لون الصف الزوجي - رمادي فاتح
        row_hover = "#e8f0fe"       # لون التمرير فوق الصف - أزرق فاتح جدًا
        row_selected = "#bbdefb"    # لون الصف المحدد - أزرق فاتح
        border = "#d0d0d0"          # لون الحدود - رمادي فاتح

        # تنسيق CSS المتقدم للجدول
        style = f"""
            QTableView {{
                background-color: {row_odd};
                alternate-background-color: {row_even};
                border: 1px solid {border};
                border-radius: 5px;
                padding: 5px;
                gridline-color: {border};
                selection-background-color: {row_selected};
                selection-color: #000000;
            }}

            QTableView::item {{
                border-bottom: 1px solid {border};
                padding: 8px;
                border-radius: 0px;
            }}

            QTableView::item:selected {{
                background-color: {row_selected};
                color: #000000;
            }}

            QTableView::item:hover:!selected {{
                background-color: {row_hover};
            }}

            QHeaderView::section {{
                background-color: {header_bg};
                color: {header_text};
                padding: 10px;
                border: none;
                border-right: 1px solid #393939;
                font-weight: bold;
                font-size: 14px;
            }}

            QHeaderView::section:hover {{
                background-color: #283593;  /* ظل أغمق قليلاً عند التمرير */
            }}

            QHeaderView::section:checked {{
                background-color: #303f9f;  /* ظل أغمق عند التحديد */
            }}

            /* الرأس السفلي - لا يستخدم عادة لكن للاكتمال */
            QHeaderView::section:horizontal:first {{
                border-top-left-radius: 5px;
            }}

            QHeaderView::section:horizontal:last {{
                border-top-right-radius: 5px;
                border-right: none;
            }}

            /* تنسيق شريط التمرير */
            QScrollBar:vertical {{
                border: none;
                background-color: #f0f0f0;
                width: 12px;
                margin: 0px;
            }}

            QScrollBar::handle:vertical {{
                background-color: #c0c0c0;
                min-height: 20px;
                border-radius: 6px;
            }}

            QScrollBar::handle:vertical:hover {{
                background-color: #a0a0a0;
            }}

            QScrollBar::add-line:vertical,
            QScrollBar::sub-line:vertical {{
                height: 0px;
            }}

            QScrollBar:horizontal {{
                border: none;
                background-color: #f0f0f0;
                height: 12px;
                margin: 0px;
            }}

            QScrollBar::handle:horizontal {{
                background-color: #c0c0c0;
                min-width: 20px;
                border-radius: 6px;
            }}

            QScrollBar::handle:horizontal:hover {{
                background-color: #a0a0a0;
            }}

            QScrollBar::add-line:horizontal,
            QScrollBar::sub-line:horizontal {{
                width: 0px;
            }}
        """

        # تطبيق النمط
        self.violations_table.setStyleSheet(style)
        self.violations_table.setAlternatingRowColors(True)

        # تعيين خلفية شفافة لخلايا الجدول لإظهار التناوب بين الصفوف
        delegate = NoBackgroundProxyStyle(self.violations_table.style())
        self.violations_table.setStyle(delegate)

    def load_violations(self):
        """تحميل بيانات المخالفات بطريقة محسنة"""
        try:
            # عرض مؤشر الانتظار
            QApplication.setOverrideCursor(Qt.WaitCursor)

            conn = sqlite3.connect(self.db_path)
            # استخدام dictfactory لسهولة التعامل مع النتائج
            conn.row_factory = lambda c, r: {col[0]: r[idx] for idx, col in enumerate(c.description)}
            cursor = conn.cursor()

            query = """
                SELECT
                    id,  -- إضافة معرف المخالفة
                    التاريخ,
                    اسم_التلميذ,
                    القسم,
                    المستوى,
                    المادة,
                    الأستاذ,
                    رمز_التلميذ
                FROM المخالفات
            """

            # تصفية حسب رمز التلميذ إذا تم توفيره
            params = []
            if self.student_code:
                query += " WHERE رمز_التلميذ = ?"
                params.append(self.student_code)

            query += " ORDER BY التاريخ DESC"

            cursor.execute(query, params)
            violations = cursor.fetchall()

            # مسح البيانات الحالية
            self.model.setRowCount(0)

            # تعبئة البيانات
            for violation in violations:
                row_items = []

                # حفظ معرف المخالفة ورمز التلميذ للاستخدام لاحقاً
                violation_id = violation.get('id')
                student_code = violation.get('رمز_التلميذ')

                # إضافة بيانات الصف
                for field in ['التاريخ', 'اسم_التلميذ', 'القسم', 'المستوى', 'المادة', 'الأستاذ']:
                    value = violation.get(field, '')
                    item = QStandardItem(str(value) if value is not None else "")
                    item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                    item.setEditable(False)

                    # تخزين معرف المخالفة ورمز التلميذ في البيانات المخفية للعنصر الأول
                    if field == 'التاريخ':
                        item.setData(violation_id, Qt.UserRole)
                        item.setData(student_code, Qt.UserRole + 1)

                    # تنسيق العناصر
                    font = QFont("Calibri", 13)
                    item.setFont(font)
                    row_items.append(item)

                self.model.appendRow(row_items)

            # تعديل أحجام الصفوف للملاءمة
            for row in range(self.model.rowCount()):
                self.violations_table.setRowHeight(row, 40)

            conn.close()

            # إزالة مؤشر الانتظار
            QApplication.restoreOverrideCursor()

        except Exception as e:
            QApplication.restoreOverrideCursor()
            show_custom_message(self, f"فشل تحميل البيانات:\n{str(e)}", "خطأ", "error")

    def edit_violation(self):
        """تعديل المخالفة المحددة"""
        # الحصول على الصف المحدد
        selected_row = -1
        selection_model = self.violations_table.selectionModel()

        if selection_model.hasSelection():
            selected_indexes = selection_model.selectedRows()
            if selected_indexes:
                selected_row = selected_indexes[0].row()

        # التحقق من وجود صف محدد
        if selected_row == -1:
            show_custom_message(self, "الرجاء تحديد مخالفة للتعديل عن طريق النقر على الصف", "تنبيه", "warning")
            return

        try:
            # استخراج معرف المخالفة ورمز الطالب
            from PyQt5.QtCore import Qt
            violation_id = self.model.item(selected_row, 0).data(Qt.UserRole)
            student_code = self.model.item(selected_row, 0).data(Qt.UserRole + 1)

            if not violation_id:
                QMessageBox.warning(self, "تنبيه", "لم يتم العثور على معرف المخالفة")
                return

            # استخراج بيانات المخالفة من الصف المحدد
            date = self.model.item(selected_row, 0).text()
            name = self.model.item(selected_row, 1).text()
            section = self.model.item(selected_row, 2).text()
            level = self.model.item(selected_row, 3).text()
            subject = self.model.item(selected_row, 4).text()
            teacher = self.model.item(selected_row, 5).text()

            # استرجاع البيانات الإضافية من قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # الاستعلام عن بيانات المخالفة الكاملة
            cursor.execute("""
                SELECT
                    الملاحظات,
                    الإجراءات,
                    إجراءات_الحراسة,
                    التاريخ
                FROM المخالفات
                WHERE id = ?
            """, (violation_id,))

            additional_data = cursor.fetchone()
            conn.close()

            if additional_data:
                notes = additional_data[0] or ""
                procedures = additional_data[1] or ""
                guard_actions = additional_data[2] or ""
                date_str = additional_data[3] or date  # استخدم القيمة من الاستعلام أو من الجدول
            else:
                notes = ""
                procedures = ""
                guard_actions = ""
                date_str = date

            # استيراد نافذة تسجيل المخالفات
            try:
                try:
                    from sub11_window import ViolationsWindow
                except ImportError:
                    raise ImportError("The module 'sub11_window' could not be found. Ensure it exists in the same directory or is in the Python path.")

                # إنشاء نافذة تعديل المخالفة
                edit_window = ViolationsWindow(parent=self)

                # وضع النافذة في وضع التعديل
                edit_window.setWindowTitle("تعديل المخالفة")

                # تعبئة البيانات الحالية في الحقول
                edit_window.code_edit.setText(str(student_code))
                edit_window.name_edit.setText(name)

                # تحديد المستوى
                level_index = edit_window.level_combo.findText(level)
                if level_index >= 0:
                    edit_window.level_combo.setCurrentIndex(level_index)

                    # تحديث الأقسام ثم تحديد القسم
                    try:
                        edit_window.update_sections()
                        section_index = edit_window.section_combo.findText(section)
                        if section_index >= 0:
                            edit_window.section_combo.setCurrentIndex(section_index)
                    except:
                        print("تعذر تحديث الأقسام")

                # تحديد المادة
                subject_index = edit_window.subject_combo.findText(subject)
                if subject_index >= 0:
                    edit_window.subject_combo.setCurrentIndex(subject_index)

                    # تحديث الأساتذة ثم تحديد الأستاذ
                    try:
                        edit_window.update_teachers()
                        teacher_index = edit_window.teacher_combo.findText(teacher)
                        if teacher_index >= 0:
                            edit_window.teacher_combo.setCurrentIndex(teacher_index)
                    except:
                        print("تعذر تحديث الأساتذة")

                # تعيين النصوص
                if hasattr(edit_window, 'notes_text'):
                    edit_window.notes_text.setPlainText(notes)
                if hasattr(edit_window, 'procedures_text'):
                    edit_window.procedures_text.setPlainText(procedures)
                if hasattr(edit_window, 'guard_actions_text'):
                    edit_window.guard_actions_text.setPlainText(guard_actions)

                # تحديث التاريخ إذا كان موجودًا
                if hasattr(edit_window, 'date_edit'):
                    try:
                        # محاولة تحليل التاريخ من النص
                        date_parts = date_str.split('/')
                        if len(date_parts) == 3:
                            qdate = QDate(int(date_parts[2]), int(date_parts[1]), int(date_parts[0]))
                            edit_window.date_edit.setDate(qdate)
                    except:
                        print("تعذر تعيين التاريخ")

                # تغيير نص زر الحفظ إلى "تعديل"
                if hasattr(edit_window, 'save_btn'):
                    edit_window.save_btn.setText("تعديل")

                    # تخزين معرف المخالفة في النافذة للاستخدام عند التعديل
                    edit_window.violation_id = violation_id

                    # تعديل وظيفة زر الحفظ ليقوم بالتعديل بدلاً من الإضافة
                    def update_violation():
                        """تعديل المخالفة القائمة بدلاً من إضافة واحدة جديدة"""
                        try:
                            # جمع البيانات من النموذج
                            code = edit_window.code_edit.text()
                            name = edit_window.name_edit.text()
                            level = edit_window.level_combo.currentText()
                            section = edit_window.section_combo.currentText()
                            subject = edit_window.subject_combo.currentText()
                            teacher = edit_window.teacher_combo.currentText()
                            notes = edit_window.notes_text.toPlainText()
                            procedures = edit_window.procedures_text.toPlainText() if hasattr(edit_window, 'procedures_text') else ""
                            guard_actions = edit_window.guard_actions_text.toPlainText() if hasattr(edit_window, 'guard_actions_text') else ""

                            # الحصول على التاريخ
                            date_obj = edit_window.date_edit.date()
                            formatted_date = f"{date_obj.day():02d}/{date_obj.month():02d}/{date_obj.year()}"

                            # فتح اتصال لقاعدة البيانات
                            conn = sqlite3.connect(self.db_path)
                            cursor = conn.cursor()

                            # تحديث البيانات في قاعدة البيانات
                            cursor.execute("""
                                UPDATE المخالفات
                                SET التاريخ = ?,
                                    اسم_التلميذ = ?,
                                    رمز_التلميذ = ?,
                                    القسم = ?,
                                    المستوى = ?,
                                    المادة = ?,
                                    الأستاذ = ?,
                                    الملاحظات = ?,
                                    الإجراءات = ?,
                                    إجراءات_الحراسة = ?
                                WHERE id = ?
                            """, (
                                formatted_date, name, code, section, level, subject, teacher,
                                notes, procedures, guard_actions, violation_id
                            ))

                            conn.commit()
                            conn.close()

                            QMessageBox.information(edit_window, "نجاح", "تم تعديل المخالفة بنجاح")

                            # إغلاق النافذة وتحديث الجدول الرئيسي
                            edit_window.close()
                            self.load_violations()

                        except Exception as e:
                            QMessageBox.critical(edit_window, "خطأ", f"فشل في تعديل المخالفة:\n{str(e)}")

                    # استبدال دالة الحفظ بدالة التحديث
                    edit_window.save_btn.clicked.disconnect()
                    edit_window.save_btn.clicked.connect(update_violation)

                # عرض نافذة التعديل
                edit_window.show()

            except ImportError:
                QMessageBox.critical(self, "خطأ", "لم يتم العثور على نافذة تسجيل المخالفات (tabbed5_window.py)")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء محاولة تعديل المخالفة:\n{str(e)}")
            import traceback
            traceback.print_exc()

    def delete_violation(self):
        """حذف المخالفة المحددة اعتمادًا على معرفها الفريد وتحديث السجل العام"""
        # الحصول على الصف المحدد
        selected_row = -1
        selection_model = self.violations_table.selectionModel()

        if selection_model.hasSelection():
            selected_indexes = selection_model.selectedRows()
            if selected_indexes:
                selected_row = selected_indexes[0].row()

        # التحقق من وجود صف محدد
        if selected_row == -1:
            show_custom_message(self, "الرجاء تحديد مخالفة للحذف عن طريق النقر على الصف", "تنبيه", "warning")
            return

        try:
            # استخراج معرف المخالفة ورمز التلميذ من البيانات المخزنة
            violation_id = self.model.item(selected_row, 0).data(Qt.UserRole)
            student_name = self.model.item(selected_row, 1).text()
            student_code = self.model.item(selected_row, 0).data(Qt.UserRole + 1)
            date = self.model.item(selected_row, 0).text()

            if not violation_id:
                show_custom_message(self, "لم يتم العثور على معرف المخالفة", "تنبيه", "warning")
                return

            # عرض مربع حوار تأكيد للحذف
            msg = f"هل أنت متأكد أنك تريد حذف هذه المخالفة؟\n\nالتاريخ: {date}\nاسم التلميذ: {student_name}"

            # استخدام رسالة مخصصة للتأكيد
            reply = show_custom_message(
                self,
                msg,
                "تأكيد حذف المخالفة",
                "question"
            )

            if reply:
                # عرض مؤشر الانتظار
                QApplication.setOverrideCursor(Qt.WaitCursor)

                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                # استخدام معرف المخالفة للحذف الدقيق
                cursor.execute("""
                    DELETE FROM المخالفات
                    WHERE id = ?
                """, (violation_id,))

                deleted_count = cursor.rowcount

                # تحديث حقل عدد_المخالفات في جدول السجل العام
                if deleted_count > 0 and student_code:
                    # حساب عدد المخالفات الجديد للطالب
                    cursor.execute("""
                        SELECT COUNT(*) FROM المخالفات
                        WHERE رمز_التلميذ = ?
                    """, (student_code,))
                    violations_count = cursor.fetchone()[0]

                    # تحديث السجل العام بعدد المخالفات الجديد
                    cursor.execute("""
                        UPDATE السجل_العام
                        SET عدد_المخالفات = ?
                        WHERE الرمز = ?
                    """, (violations_count, student_code))

                    violations_updated = cursor.rowcount
                    if violations_updated > 0:
                        print(f"تم تحديث عدد المخالفات للطالب {student_code} إلى {violations_count}")
                    else:
                        print(f"لم يتم تحديث عدد المخالفات للطالب {student_code}")

                conn.commit()
                conn.close()

                # إزالة مؤشر الانتظار
                QApplication.restoreOverrideCursor()

                # عرض رسالة نجاح
                if deleted_count > 0:
                    show_custom_message(
                        self,
                        "تم حذف المخالفة بنجاح وتحديث السجل العام.",
                        "نجاح العملية",
                        "success"
                    )

                    # تحديث الجدول
                    self.load_violations()
                else:
                    show_custom_message(
                        self,
                        "لم يتم العثور على المخالفة المحددة في قاعدة البيانات.",
                        "تنبيه",
                        "warning"
                    )
        except Exception as e:
            QApplication.restoreOverrideCursor()
            show_custom_message(
                self,
                f"حدث خطأ أثناء محاولة حذف المخالفة:\n{str(e)}",
                "خطأ",
                "error"
            )

    def print_violation(self):
        """طباعة المخالفة المحددة"""
        # الحصول على الصف المحدد
        selected_row = -1
        selection_model = self.violations_table.selectionModel()

        if selection_model.hasSelection():
            selected_indexes = selection_model.selectedRows()
            if selected_indexes:
                selected_row = selected_indexes[0].row()

        # التحقق من وجود صف محدد
        if selected_row == -1:
            show_custom_message(self, "الرجاء تحديد مخالفة للطباعة عن طريق النقر على الصف", "تنبيه", "warning")
            return

        try:
            # استخراج معرف المخالفة ورمز التلميذ
            from PyQt5.QtCore import Qt
            violation_id = self.model.item(selected_row, 0).data(Qt.UserRole)
            student_code = self.model.item(selected_row, 0).data(Qt.UserRole + 1)

            if not violation_id:
                show_custom_message(self, "لم يتم العثور على معرف المخالفة", "تنبيه", "warning")
                return

            # استخراج بيانات المخالفة من قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT
                    id,
                    التاريخ,
                    اسم_التلميذ,
                    رمز_التلميذ,
                    القسم,
                    المستوى,
                    المادة,
                    الأستاذ,
                    الملاحظات,
                    الإجراءات,
                    إجراءات_الحراسة
                FROM المخالفات
                WHERE id = ?
            """, (violation_id,))

            violation_data_row = cursor.fetchone()

            if not violation_data_row:
                show_custom_message(self, "لم يتم العثور على بيانات المخالفة", "تنبيه", "warning")
                conn.close()
                return

            # تجميع بيانات المخالفة في قاموس
            violation_info = {
                "id": violation_data_row[0],
                "date": violation_data_row[1],
                "student_name": violation_data_row[2],
                "student_code": violation_data_row[3],
                "section": violation_data_row[4],
                "level": violation_data_row[5],
                "subject": violation_data_row[6],
                "teacher": violation_data_row[7],
                "notes": violation_data_row[8] or "",
                "procedures": violation_data_row[9] or "",
                "guard_actions": violation_data_row[10] or ""
            }

            # محاولة استرجاع المزيد من معلومات الطالب إذا كانت متاحة
            try:
                # التحقق من وجود الأعمدة قبل الاستعلام
                cursor.execute("PRAGMA table_info(السجل_العام)")
                columns_info = cursor.fetchall()
                columns = [col[1] for col in columns_info]

                # بناء الاستعلام بناءً على الأعمدة المتاحة
                select_fields = []

                # تجهيز القائمة بالحقول المطلوبة إذا كانت متوفرة في قاعدة البيانات
                if "الرمز_السري" in columns:
                    select_fields.append("الرمز_السري")
                if "تاريخ_الازدياد" in columns:
                    select_fields.append("تاريخ_الازدياد")
                if "مكان_الازدياد" in columns:
                    select_fields.append("مكان_الازدياد")
                if "الهاتف_الأول" in columns:
                    select_fields.append("الهاتف_الأول")
                if "الهاتف_الثاني" in columns:
                    select_fields.append("الهاتف_الثاني")

                # تنفيذ الاستعلام فقط إذا وجدت حقول للاستعلام عنها
                if select_fields:
                    query = f"SELECT {', '.join(select_fields)} FROM السجل_العام WHERE الرمز = ?"
                    cursor.execute(query, (student_code,))
                    student_data_row = cursor.fetchone()

                    if student_data_row:
                        field_mapping = {
                            "الرمز_السري": "student_secret",
                            "تاريخ_الازدياد": "birth_date",
                            "مكان_الازدياد": "birth_place",
                            "الهاتف_الأول": "phone1",
                            "الهاتف_الثاني": "phone2"
                        }

                        # إضافة معلومات الطالب الإضافية للقاموس
                        for i, field in enumerate(select_fields):
                            if i < len(student_data_row) and student_data_row[i]:
                                violation_info[field_mapping.get(field, field)] = student_data_row[i]
            except Exception as e:
                print(f"خطأ في استرجاع معلومات الطالب الإضافية: {e}")

            conn.close()

            # محاولة استخدام ملف print3.py أولاً
            try:
                # محاولة استيراد وحدة الطباعة المحسنة الجديدة (print3.py)
                from print3 import print_violation_details, open_pdf
                print("استخدام ملف print3.py للطباعة")

                # طباعة المخالفة باستخدام الوحدة المحسنة وتمرير مسار قاعدة البيانات
                # عدم فتح الملف تلقائياً (auto_open=False)
                success, pdf_path = print_violation_details(violation_info, db_path=self.db_path, auto_open=False)

                if success:
                    # عرض رسالة تأكيد مخصصة
                    try:
                        from PyQt5.QtWidgets import QMessageBox, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton
                        from PyQt5.QtGui import QFont, QIcon, QPixmap
                        from PyQt5.QtCore import Qt

                        # إنشاء نافذة حوار مخصصة للتقرير
                        custom_dialog = QDialog(self)
                        custom_dialog.setWindowTitle("تم إنشاء التقرير")
                        custom_dialog.setFixedSize(450, 250)
                        custom_dialog.setLayoutDirection(Qt.RightToLeft)

                        # إضافة أيقونة البرنامج
                        try:
                            app_icon = QIcon("01.ico")
                            custom_dialog.setWindowIcon(app_icon)
                        except Exception as e:
                            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

                        # تنسيق النافذة
                        custom_dialog.setStyleSheet("""
                            QDialog {
                                background-color: #f0f8ff;
                                border: 2px solid #0066cc;
                                border-radius: 10px;
                            }
                            QLabel {
                                color: #333333;
                                font-weight: bold;
                            }
                            QPushButton {
                                border-radius: 5px;
                                padding: 8px 15px;
                                font-weight: bold;
                                min-height: 35px;
                            }
                            QPushButton#preview_btn {
                                background-color: #27ae60;
                                color: white;
                            }
                            QPushButton#preview_btn:hover {
                                background-color: #2ecc71;
                                border: 2px solid #27ae60;
                            }
                            QPushButton#cancel_btn {
                                background-color: #e74c3c;
                                color: white;
                            }
                            QPushButton#cancel_btn:hover {
                                background-color: #c0392b;
                                border: 2px solid #e74c3c;
                            }
                        """)

                        # إنشاء تخطيط النافذة
                        layout = QVBoxLayout(custom_dialog)
                        layout.setContentsMargins(20, 20, 20, 20)
                        layout.setSpacing(15)

                        # إضافة أيقونة وعنوان
                        header_layout = QHBoxLayout()

                        # محاولة إضافة أيقونة البرنامج
                        icon_label = QLabel()
                        icon_label.setAlignment(Qt.AlignCenter)
                        try:
                            program_icon = QPixmap("01.ico")
                            if not program_icon.isNull():
                                program_icon = program_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                                icon_label.setPixmap(program_icon)
                                header_layout.addWidget(icon_label)
                        except Exception as e:
                            print(f"خطأ في تحميل أيقونة البرنامج للعنوان: {e}")

                        # إضافة عنوان النافذة
                        title_label = QLabel("تم إنشاء التقرير بنجاح")
                        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
                        title_label.setStyleSheet("color: #0066cc;")
                        title_label.setAlignment(Qt.AlignCenter)
                        header_layout.addWidget(title_label, 1)

                        layout.addLayout(header_layout)

                        # إضافة رسالة التأكيد
                        message_label = QLabel("تم إنشاء تقرير المخالفة بنجاح.\nهل ترغب في معاينة وطباعة التقرير الآن؟")
                        message_label.setFont(QFont("Calibri", 13, QFont.Bold))
                        message_label.setAlignment(Qt.AlignCenter)
                        message_label.setWordWrap(True)
                        layout.addWidget(message_label)

                        # إضافة أزرار المعاينة والإلغاء
                        buttons_layout = QHBoxLayout()

                        preview_btn = QPushButton("معاينة وطباعة")
                        preview_btn.setObjectName("preview_btn")
                        preview_btn.setFont(QFont("Calibri", 12, QFont.Bold))
                        preview_btn.setCursor(Qt.PointingHandCursor)
                        preview_btn.setFixedWidth(150)
                        preview_btn.clicked.connect(custom_dialog.accept)

                        cancel_btn = QPushButton("إلغاء")
                        cancel_btn.setObjectName("cancel_btn")
                        cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
                        cancel_btn.setCursor(Qt.PointingHandCursor)
                        cancel_btn.setFixedWidth(105)
                        cancel_btn.clicked.connect(custom_dialog.reject)

                        buttons_layout.addWidget(preview_btn)
                        buttons_layout.addWidget(cancel_btn)

                        layout.addLayout(buttons_layout)

                        # عرض النافذة وانتظار رد المستخدم
                        response = custom_dialog.exec_()

                        # فتح الملف فقط إذا اختار المستخدم "معاينة وطباعة"
                        if response == QDialog.Accepted:
                            try:
                                from print4 import open_pdf
                                if open_pdf(pdf_path):
                                    print(f"تم فتح ملف PDF بنجاح: {pdf_path}")
                                else:
                                    show_custom_message(self, "تم إنشاء التقرير بنجاح ولكن فشل في فتحه", "تنبيه", "warning")
                            except ImportError:
                                try:
                                    from print4 import open_html_file
                                    if open_html_file(pdf_path):
                                        print(f"تم فتح ملف HTML بنجاح: {pdf_path}")
                                    else:
                                        show_custom_message(self, "تم إنشاء التقرير بنجاح ولكن فشل في فتحه", "تنبيه", "warning")
                                except Exception as e:
                                    print(f"خطأ في فتح الملف: {e}")
                                    show_custom_message(self, "تم إنشاء التقرير بنجاح ولكن فشل في فتحه", "تنبيه", "warning")
                            except Exception as e:
                                print(f"خطأ في فتح الملف: {e}")
                                show_custom_message(self, "تم إنشاء التقرير بنجاح ولكن فشل في فتحه", "تنبيه", "warning")
                    except Exception as dialog_error:
                        print(f"خطأ في عرض نافذة التأكيد: {dialog_error}")
                        # استخدام رسالة تأكيد بسيطة كبديل
                        msg_box = QMessageBox()
                        msg_box.setIcon(QMessageBox.Information)
                        msg_box.setWindowTitle("تم إنشاء التقرير")
                        msg_box.setText("تم إنشاء تقرير المخالفة بنجاح.")
                        msg_box.setInformativeText("هل ترغب في فتح التقرير الآن؟")
                        msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
                        msg_box.setDefaultButton(QMessageBox.Yes)

                        if msg_box.exec_() == QMessageBox.Yes:
                            try:
                                from print4 import open_pdf
                                open_pdf(pdf_path)
                            except ImportError:
                                try:
                                    from print4 import open_html_file
                                    open_html_file(pdf_path)
                                except Exception as e:
                                    print(f"خطأ في فتح الملف: {e}")
                            except Exception as e:
                                print(f"خطأ في فتح الملف: {e}")

                    return
                else:
                    print("فشل في استخدام print3.py، محاولة استخدام الطرق البديلة")
            except ImportError as e:
                print(f"لم يتم العثور على ملف print3.py: {e}")
                print("محاولة استخدام الطرق البديلة")
            except Exception as e:
                print(f"خطأ في استخدام print3.py: {e}")
                print("محاولة استخدام الطرق البديلة")

            # محاولة استخدام ملف print_violation_report.py كخيار ثاني
            try:
                # محاولة استيراد وحدة الطباعة المحسنة القديمة (print_violation_report.py)
                from print_violation_report import print_violation_details
                print("استخدام ملف print_violation_report.py للطباعة")

                # طباعة المخالفة باستخدام الوحدة المحسنة وتمرير مسار قاعدة البيانات
                success = print_violation_details(violation_info, db_path=self.db_path)

                if success:
                    show_custom_message(self, "تمت طباعة المخالفة بنجاح بالتنسيق المحسن", "نجاح", "success")
                    return
                else:
                    print("فشل في استخدام print_violation_report.py، محاولة استخدام الطرق البديلة")
            except ImportError as e:
                print(f"لم يتم العثور على ملف print_violation_report.py: {e}")
                print("محاولة استخدام الطرق البديلة")
            except Exception as e:
                print(f"خطأ في استخدام print_violation_report.py: {e}")
                print("محاولة استخدام الطرق البديلة")

            # استخدام الدالة المحسنة المدمجة كخيار ثاني
            success = self._create_temp_violation_pdf(violation_info)

            if not success:
                # محاولة استخدام وحدات الطباعة الأخرى كبديل
                try:
                    # محاولة استيراد وحدة الطباعة (print_violations_pdf.py)
                    try:
                        from print4 import print_violation_details
                        print("Using print4 module for printing violations.")
                    except ImportError:
                        raise ImportError("The module 'print_violations_pdf' or 'print4' could not be found. Ensure it exists in the project.")
                    print("استخدام ملف print_violations_pdf.py للطباعة")

                    # طباعة المخالفة باستخدام الوحدة وتمرير مسار قاعدة البيانات
                    success = print_violation_details(violation_info, db_path=self.db_path)

                    if success:
                        show_custom_message(self, "تمت طباعة المخالفة بنجاح بصيغة PDF", "نجاح", "success")
                    else:
                        show_custom_message(self, "حدث خطأ أثناء طباعة المخالفة", "تنبيه", "warning")

                except ImportError:
                    # محاولة استخدام الوحدة القديمة (print5_test.py) كبديل أخير
                    try:
                        from print5_test import print_violation_details as old_print_5
                        print("تحذير: استخدام print5_test.py لطباعة المخالفات.")
                        # تمرير مسار قاعدة البيانات للوحدة القديمة أيضاً
                        success = old_print_5(violation_info, db_path=self.db_path)
                        if success:
                            show_custom_message(self, "تمت طباعة المخالفة بنجاح (باستخدام print5_test.py)", "نجاح", "success")
                        else:
                            show_custom_message(self, "حدث خطأ أثناء طباعة المخالفة (print5_test.py)", "تنبيه", "warning")
                    except ImportError:
                         show_custom_message(self, "ملفات الطباعة غير متوفرة.", "خطأ", "error")

        except Exception as e:
            show_custom_message(self, f"حدث خطأ أثناء محاولة طباعة المخالفة:\n{str(e)}", "خطأ", "error")
            traceback.print_exc()

    def _create_temp_violation_pdf(self, violation_info):
        """إنشاء ملف PDF للمخالفة بتصميم محسن

        Args:
            violation_info (dict): قاموس يحتوي على معلومات المخالفة

        Returns:
            bool: True إذا تم إنشاء الملف بنجاح، False في حالة حدوث خطأ
        """
        try:
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import A4
            from reportlab.lib.units import cm
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            import tempfile
            import os
            import webbrowser
            import sqlite3
            from datetime import datetime

            # استيراد مكتبات معالجة النصوص العربية
            try:
                import arabic_reshaper
                from bidi.algorithm import get_display
                ARABIC_SUPPORT = True
                print("تم تحميل مكتبات معالجة النصوص العربية بنجاح.")
            except ImportError:
                ARABIC_SUPPORT = False
                print("تنبيه: مكتبات معالجة النصوص العربية غير متوفرة. قم بتثبيت: pip install arabic-reshaper python-bidi")

            # دالة لإعادة تشكيل النص العربي
            def reshape_arabic(text):
                """إعادة تشكيل النص العربي للعرض الصحيح في PDF"""
                if not text:
                    return ""
                try:
                    if ARABIC_SUPPORT:
                        reshaped_text = arabic_reshaper.reshape(str(text))
                        bidi_text = get_display(reshaped_text)
                        return bidi_text
                    else:
                        return str(text)
                except Exception as e:
                    print(f"خطأ في إعادة تشكيل النص العربي: {e}")
                    return str(text)

            # تسجيل الخطوط العربية إذا كانت متوفرة
            try:
                font_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "fonts", "Arial.ttf")
                if os.path.exists(font_path):
                    pdfmetrics.registerFont(TTFont("Arabic", font_path))
                else:
                    # استخدام الخطوط الافتراضية
                    pdfmetrics.registerFont(TTFont("Arabic", "Helvetica"))
            except:
                pass

            # إنشاء ملف PDF مؤقت
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
            c = canvas.Canvas(temp_file.name, pagesize=A4)
            width, height = A4

            # --- إعدادات قابلة للتعديل ---
            settings = {
                "logo_top": 0.3 * cm,  # تعديل موضع الشعار ليكون في أعلى الصفحة
                "logo_width": 300,
                "logo_height": 70,

                "title_top": 2.5 * cm,  # تعديل موضع العنوان
                "institution_font_size": 18,
                "form_title_font_size": 16,

                "content_font_size": 13,  # تعديل حجم الخط
                "line_spacing": 0.7 * cm,

                "box_border_color": (0.2, 0.4, 0.6),  # لون أزرق داكن للإطارات
                "title_color": (0.2, 0.4, 0.6),       # لون أزرق داكن للعناوين
                "header_bg_color": (0.9, 0.9, 0.95),  # لون رمادي فاتح لخلفية العناوين
            }

            # الحصول على بيانات المؤسسة من قاعدة البيانات
            institution_data = {}
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                # محاولة قراءة من جدول بيانات_المؤسسة
                try:
                    cursor.execute("SELECT المؤسسة, السنة_الدراسية, ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
                    institution_row = cursor.fetchone()
                    if institution_row:
                        institution_data['name'] = institution_row[0]
                        institution_data['academic_year'] = institution_row[1]
                        logo_path = institution_row[2]
                        if logo_path and os.path.exists(logo_path):
                            institution_data['logo_path'] = logo_path
                except Exception as e:
                    print(f"خطأ في قراءة بيانات المؤسسة: {e}")

                conn.close()
            except Exception as db_error:
                print(f"خطأ في الاتصال بقاعدة البيانات: {db_error}")

            # إضافة شعار المؤسسة إذا كان متوفرًا
            logo_path = institution_data.get('logo_path')
            if logo_path and os.path.exists(logo_path):
                try:
                    # إضافة الشعار في أعلى الصفحة
                    logo_x = (width - settings["logo_width"]) / 2
                    logo_y = height - settings["logo_top"] - settings["logo_height"]
                    c.drawImage(logo_path, logo_x, logo_y, width=settings["logo_width"],
                               height=settings["logo_height"], preserveAspectRatio=True, mask='auto')
                except Exception as logo_error:
                    print(f"خطأ في إضافة الشعار: {logo_error}")

            # --- عنوان المؤسسة ---
            c.setFillColorRGB(*settings["title_color"])
            c.setFont("Arabic", settings["institution_font_size"])
            institution_name = institution_data.get('name', '')
            if institution_name:
                c.drawCentredString(width/2, height - settings["title_top"], reshape_arabic(institution_name))

            # --- عنوان التقرير ---
            c.setFont("Arabic", settings["form_title_font_size"])
            c.drawCentredString(width/2, height - (settings["title_top"] + 25), reshape_arabic("تقرير المخالفة"))

            # --- السنة الدراسية ---
            academic_year = institution_data.get('academic_year', '')
            if academic_year:
                c.setFont("Arabic", settings["form_title_font_size"] - 2)
                c.drawRightString(width - 2*cm, height - settings["title_top"] - 50,
                                 reshape_arabic(f"السنة الدراسية: {academic_year}"))

            # --- معلومات التلميذ ---
            box_top = height - 6 * cm
            box_left = 1 * cm
            box_width = 19 * cm
            box_height = 3.5 * cm

            # عنوان قسم معلومات التلميذ
            c.setFont("Arabic", settings["form_title_font_size"] - 2)
            c.drawString(box_left, box_top + 0.5 * cm, reshape_arabic("معلومات التلميذ"))

            # رسم إطار لمعلومات التلميذ
            c.setStrokeColorRGB(*settings["box_border_color"])
            c.setLineWidth(1.5)
            c.rect(box_left, box_top - box_height, box_width, box_height)

            # إضافة شريط عنوان داخل الإطار
            c.setFillColorRGB(*settings["header_bg_color"])
            c.rect(box_left, box_top - 0.8 * cm, box_width, 0.8 * cm, fill=1, stroke=0)

            # إضافة معلومات التلميذ
            right_x = width - 2 * cm
            y = box_top - 0.6 * cm

            # عناوين الأعمدة
            c.setFillColorRGB(*settings["title_color"])
            c.setFont("Arabic", settings["content_font_size"] - 1)
            c.drawRightString(right_x - 9 * cm, y, reshape_arabic("اسم التلميذ"))
            c.drawRightString(right_x, y, reshape_arabic("الرمز"))

            # بيانات الصف الأول
            y -= settings["line_spacing"]
            c.setFillColorRGB(0, 0, 0)  # لون أسود للنص
            c.setFont("Arabic", settings["content_font_size"])
            c.drawRightString(right_x - 9 * cm, y, reshape_arabic(violation_info.get('student_name', '')))
            c.drawRightString(right_x, y, reshape_arabic(violation_info.get('student_code', '')))

            # عناوين الأعمدة للصف الثاني
            y -= settings["line_spacing"] + 0.2 * cm
            c.setFillColorRGB(*settings["title_color"])
            c.setFont("Arabic", settings["content_font_size"] - 1)
            c.drawRightString(right_x - 9 * cm, y, reshape_arabic("المستوى"))
            c.drawRightString(right_x, y, reshape_arabic("القسم"))

            # بيانات الصف الثاني
            y -= settings["line_spacing"]
            c.setFillColorRGB(0, 0, 0)  # لون أسود للنص
            c.setFont("Arabic", settings["content_font_size"])
            c.drawRightString(right_x - 9 * cm, y, reshape_arabic(violation_info.get('level', '')))
            c.drawRightString(right_x, y, reshape_arabic(violation_info.get('section', '')))

            # --- معلومات المخالفة ---
            violation_box_top = box_top - box_height - 1.2 * cm

            # عنوان قسم تفاصيل المخالفة
            c.setFillColorRGB(*settings["title_color"])
            c.setFont("Arabic", settings["form_title_font_size"] - 2)
            c.drawString(box_left, violation_box_top + 0.5 * cm, reshape_arabic("تفاصيل المخالفة"))

            # رسم إطار لمعلومات المخالفة
            violation_box_height = 7 * cm
            c.setStrokeColorRGB(*settings["box_border_color"])
            c.rect(box_left, violation_box_top - violation_box_height, box_width, violation_box_height)

            # إضافة شريط عنوان داخل الإطار
            c.setFillColorRGB(*settings["header_bg_color"])
            c.rect(box_left, violation_box_top - 0.8 * cm, box_width, 0.8 * cm, fill=1, stroke=0)

            # إضافة معلومات المخالفة
            y = violation_box_top - 0.6 * cm

            # عناوين الأعمدة
            c.setFillColorRGB(*settings["title_color"])
            c.setFont("Arabic", settings["content_font_size"] - 1)
            c.drawRightString(right_x - 9 * cm, y, reshape_arabic("تاريخ المخالفة"))
            c.drawRightString(right_x, y, reshape_arabic("رقم المخالفة"))

            # بيانات الصف الأول
            y -= settings["line_spacing"]
            c.setFillColorRGB(0, 0, 0)  # لون أسود للنص
            c.setFont("Arabic", settings["content_font_size"])
            c.drawRightString(right_x - 9 * cm, y, reshape_arabic(violation_info.get('date', '')))
            c.drawRightString(right_x, y, reshape_arabic(str(violation_info.get('id', ''))))

            # المادة والأستاذ
            y -= settings["line_spacing"] + 0.2 * cm
            c.setFillColorRGB(*settings["title_color"])
            c.setFont("Arabic", settings["content_font_size"] - 1)
            c.drawRightString(right_x - 9 * cm, y, reshape_arabic("المادة"))
            c.drawRightString(right_x, y, reshape_arabic("الأستاذ(ة)"))

            y -= settings["line_spacing"]
            c.setFillColorRGB(0, 0, 0)  # لون أسود للنص
            c.setFont("Arabic", settings["content_font_size"])
            c.drawRightString(right_x - 9 * cm, y, reshape_arabic(violation_info.get('subject', '')))
            c.drawRightString(right_x, y, reshape_arabic(violation_info.get('teacher', '')))

            # وصف المخالفة (الملاحظات)
            y -= settings["line_spacing"] + 0.2 * cm
            c.setFillColorRGB(*settings["title_color"])
            c.setFont("Arabic", settings["content_font_size"] - 1)
            c.drawRightString(right_x, y, reshape_arabic("وصف المخالفة:"))

            y -= settings["line_spacing"]
            c.setFillColorRGB(0, 0, 0)  # لون أسود للنص
            c.setFont("Arabic", settings["content_font_size"])

            # تقسيم النص الطويل إلى أسطر متعددة إذا لزم الأمر
            notes = violation_info.get('notes', '') or "لا توجد"
            if len(notes) > 60:  # إذا كان النص طويلاً
                lines = []
                current_line = ""
                words = notes.split()
                for word in words:
                    if len(current_line + " " + word) <= 60:
                        current_line += " " + word if current_line else word
                    else:
                        lines.append(current_line)
                        current_line = word
                if current_line:
                    lines.append(current_line)

                for line in lines:
                    c.drawRightString(right_x, y, reshape_arabic(line))
                    y -= settings["line_spacing"] - 0.3 * cm
            else:
                c.drawRightString(right_x, y, reshape_arabic(notes))
                y -= settings["line_spacing"]

            # الإجراء المتخذ
            y -= 0.2 * cm
            c.setFillColorRGB(*settings["title_color"])
            c.setFont("Arabic", settings["content_font_size"] - 1)
            c.drawRightString(right_x, y, reshape_arabic("الإجراء المتخذ:"))

            y -= settings["line_spacing"]
            c.setFillColorRGB(0, 0, 0)  # لون أسود للنص
            c.setFont("Arabic", settings["content_font_size"])
            procedures = violation_info.get('procedures', '') or "لا توجد"
            c.drawRightString(right_x, y, reshape_arabic(procedures))

            # إجراءات الحراسة
            y -= settings["line_spacing"] + 0.2 * cm
            c.setFillColorRGB(*settings["title_color"])
            c.setFont("Arabic", settings["content_font_size"] - 1)
            c.drawRightString(right_x, y, reshape_arabic("إجراءات الحراسة:"))

            y -= settings["line_spacing"]
            c.setFillColorRGB(0, 0, 0)  # لون أسود للنص
            c.setFont("Arabic", settings["content_font_size"])
            guard_actions = violation_info.get('guard_actions', '') or "لا توجد"
            c.drawRightString(right_x, y, reshape_arabic(guard_actions))

            # --- التوقيعات ---
            signature_y = violation_box_top - violation_box_height - 2.5 * cm

            # رسم خطوط للتوقيعات
            c.setStrokeColorRGB(0.5, 0.5, 0.5)  # لون رمادي للخطوط
            c.setLineWidth(0.5)

            # توقيع الأستاذ(ة)
            c.line(2 * cm, signature_y, 7 * cm, signature_y)
            c.setFillColorRGB(*settings["title_color"])
            c.setFont("Arabic", settings["content_font_size"])
            c.drawString(2 * cm, signature_y - 0.7 * cm, reshape_arabic("توقيع الأستاذ(ة)"))

            # توقيع الحراسة العامة
            c.line(width - 7 * cm, signature_y, width - 2 * cm, signature_y)
            c.drawRightString(width - 2 * cm, signature_y - 0.7 * cm, reshape_arabic("توقيع الحراسة العامة"))

            # --- تاريخ الطباعة ---
            current_date = datetime.now().strftime("%Y-%m-%d")
            c.setFont("Arabic", 10)
            c.drawRightString(width - 2 * cm, 1 * cm, reshape_arabic(f"تاريخ الطباعة: {current_date}"))

            # --- رقم الصفحة ---
            c.setFont("Arabic", 8)
            c.drawCentredString(width/2, 0.7 * cm, reshape_arabic("صفحة 1 من 1"))

            # حفظ المستند
            c.save()

            # فتح الملف في متصفح PDF الافتراضي
            webbrowser.open(f'file:///{os.path.abspath(temp_file.name)}')

            # إنشاء مجلد للمخالفات إذا لم يكن موجودًا
            violations_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "المخالفات")
            os.makedirs(violations_dir, exist_ok=True)

            # حفظ نسخة دائمة من الملف
            student_name = violation_info.get('student_name', 'بدون_اسم').replace(' ', '_')
            student_code = violation_info.get('student_code', 'بدون_رمز')
            current_date = datetime.now().strftime("%Y%m%d")
            permanent_filename = os.path.join(violations_dir, f"مخالفة_{student_name}_{student_code}_{current_date}.pdf")

            # نسخ الملف المؤقت إلى الملف الدائم
            import shutil
            shutil.copy2(temp_file.name, permanent_filename)

            show_custom_message(
                self,
                f"تم إنشاء ملف PDF للمخالفة بنجاح وفتحه للطباعة.\n\nتم حفظ نسخة في:\n{permanent_filename}",
                "تمت معالجة الطباعة",
                "success"
            )

            return True

        except Exception as e:
            show_custom_message(
                self,
                f"تعذر إنشاء ملف PDF للطباعة:\n{str(e)}",
                "خطأ في الطباعة",
                "error"
            )
            return False

    def save_violation_to_pdf(self):
        """حفظ المخالفة كملف PDF"""
        # --- إزالة محتوى الدالة (لم تعد مستخدمة) ---
        pass

    def save_record_to_pdf(self):
        """طباعة سجل المخالفات كملف PDF"""
        try:
            if not self.student_code:
                show_custom_message(self, "لا يوجد رمز تلميذ محدد", "تنبيه", "warning")
                return

            # استخراج بيانات التلميذ
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # استعلام عن معلومات التلميذ (نفس الكود السابق)
            cursor.execute("""
                SELECT اسم_التلميذ, القسم, المستوى
                FROM المخالفات
                WHERE رمز_التلميذ = ?
                LIMIT 1
            """, (self.student_code,))
            student_data = cursor.fetchone()

            if not student_data:
                cursor.execute("""
                    SELECT الاسم_والنسب, القسم, المستوى
                    FROM السجل_العام
                    JOIN اللوائح ON السجل_العام.الرمز = اللوائح.الرمز
                    WHERE السجل_العام.الرمز = ? AND اللوائح.السنة_الدراسية = (SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1)
                    LIMIT 1
                """, (self.student_code,))
                student_data = cursor.fetchone()

            if not student_data:
                show_custom_message(self, "لم يتم العثور على بيانات التلميذ", "تنبيه", "warning")
                conn.close()
                return

            # تجهيز معلومات التلميذ للطباعة
            student_info = {
                'name': student_data[0],
                'code': self.student_code,
                'section': student_data[1] if student_data[1] else "",
                'level': student_data[2] if student_data[2] else ""
            }

            # استعلام عن جميع مخالفات التلميذ (نفس الكود السابق)
            cursor.execute("""
                SELECT id, التاريخ, المادة, الأستاذ, الملاحظات, الإجراءات
                FROM المخالفات
                WHERE رمز_التلميذ = ?
                ORDER BY التاريخ DESC
            """, (self.student_code,))
            violations_data = cursor.fetchall()
            conn.close()

            if not violations_data:
                show_custom_message(self, "لا توجد مخالفات مسجلة لهذا التلميذ", "تنبيه", "warning")
                return

            # تحويل البيانات إلى شكل قائمة من القواميس (نفس الكود السابق)
            violations_records = []
            for violation in violations_data:
                violations_records.append({
                    'id': violation[0],
                    'date': violation[1],
                    'subject': violation[2],
                    'teacher': violation[3],
                    'notes': violation[4],
                    'procedures': violation[5]
                })

            # عرض نافذة تأكيد مخصصة قبل الطباعة
            try:
                # استيراد وحدة رسائل التأكيد المخصصة
                from sub100_window import ConfirmationDialogs

                # إنشاء نافذة تأكيد طباعة سجل المخالفات
                confirm_dialog = ConfirmationDialogs.create_student_record_confirmation_dialog(self, student_info)

                # عرض النافذة وانتظار رد المستخدم
                if confirm_dialog.exec_() != QDialog.Accepted:
                    # إذا اختار المستخدم "إلغاء"، نخرج من الدالة
                    return
            except ImportError:
                # في حالة عدم وجود ملف sub100_window.py، نستخدم رسالة تأكيد بسيطة
                if not show_custom_message(self, f"هل ترغب في طباعة سجل مخالفات التلميذ(ة):\n{student_info['name']}؟", "تأكيد", "question"):
                    return
            except Exception as dialog_error:
                print(f"خطأ في عرض نافذة التأكيد: {dialog_error}")
                # نستمر بدون تأكيد في حالة حدوث خطأ

            # استخدام وحدة الطباعة الجديدة لطباعة سجل المخالفات
            try:
                # محاولة استيراد وحدة الطباعة المحسنة للمخالفات
                try:
                    from print4 import print_student_violations_record
                    print("تم استيراد ملف طباعة سجل المخالفات المحسن (print4.py) بنجاح")
                except ImportError:
                    try:
                        from print_student_record_improved import print_student_violations_record
                        print("تم استيراد ملف طباعة سجل المخالفات المحسن بنجاح")
                    except ImportError:
                        try:
                            # تغيير اسم الملف من print_student_record_pdf إلى print_student_record_improved
                            from print_student_record_improved import print_student_violations_record
                            print("تم استيراد ملف طباعة سجل المخالفات الجديد بنجاح")
                        except ImportError:
                            # في حالة عدم وجود الملف الجديد، نستخدم الملف القديم
                            from print5_test import print_student_violations_record
                            print("تم استيراد ملف طباعة سجل المخالفات القديم")

                # محاولة طباعة سجل المخالفات وتمرير مسار قاعدة البيانات
                # عدم فتح الملف تلقائياً (auto_open=False)
                success, pdf_path = print_student_violations_record(student_info, violations_records, db_path=self.db_path, auto_open=False)

                if success:
                    # تحديد مسار المجلد
                    folder_path = os.path.dirname(os.path.abspath(pdf_path))

                    # إنشاء رسالة نجاح مخصصة
                    success_message = f"تم إنشاء ملف PDF لسجل المخالفات بنجاح\n\nتم حفظ الملف في:\n{pdf_path}"

                    # استخدام رسالة مخصصة مع خيار فتح الملف أو المجلد
                    from PyQt5.QtWidgets import QMessageBox
                    msg_box = QMessageBox(self)
                    msg_box.setWindowTitle("تم إنشاء التقرير")
                    msg_box.setText(success_message)
                    msg_box.setIcon(QMessageBox.Information)

                    # إضافة أزرار مخصصة
                    open_file_button = msg_box.addButton("فتح الملف", QMessageBox.ActionRole)
                    open_folder_button = msg_box.addButton("فتح المجلد", QMessageBox.ActionRole)
                    close_button = msg_box.addButton("إغلاق", QMessageBox.RejectRole)

                    # تنسيق الأزرار
                    open_file_button.setStyleSheet("""
                        background-color: #27ae60;
                        color: white;
                        font-weight: bold;
                        border-radius: 5px;
                        padding: 8px 16px;
                        min-width: 100px;
                        min-height: 30px;
                    """)

                    open_folder_button.setStyleSheet("""
                        background-color: #3498db;
                        color: white;
                        font-weight: bold;
                        border-radius: 5px;
                        padding: 8px 16px;
                        min-width: 100px;
                        min-height: 30px;
                    """)

                    close_button.setStyleSheet("""
                        background-color: #e74c3c;
                        color: white;
                        font-weight: bold;
                        border-radius: 5px;
                        padding: 8px 16px;
                        min-width: 100px;
                        min-height: 30px;
                    """)

                    # عرض مربع الحوار وانتظار رد المستخدم
                    msg_box.exec_()

                    # معالجة رد المستخدم
                    clicked_button = msg_box.clickedButton()

                    if clicked_button == open_file_button:
                        # فتح الملف
                        try:
                            from print4 import open_pdf
                            open_pdf(pdf_path)
                        except ImportError:
                            try:
                                from print4 import open_html_file
                                open_html_file(pdf_path)
                            except Exception as file_err:
                                print(f"خطأ في فتح الملف: {file_err}")
                                show_custom_message(self, f"تعذر فتح الملف: {file_err}", "تنبيه", "warning")
                        except Exception as file_err:
                            print(f"خطأ في فتح الملف: {file_err}")
                            show_custom_message(self, f"تعذر فتح الملف: {file_err}", "تنبيه", "warning")

                    elif clicked_button == open_folder_button:
                        # فتح المجلد
                        try:
                            os.startfile(folder_path)
                        except Exception as folder_err:
                            print(f"خطأ في فتح المجلد: {folder_err}")
                            show_custom_message(self, f"تعذر فتح المجلد: {folder_err}", "تنبيه", "warning")


                else:
                    show_custom_message(self, "حدث خطأ أثناء إنشاء ملف PDF لسجل المخالفات", "تنبيه", "warning")

            except ImportError as ie:
                show_custom_message(self, f"ملفات الطباعة غير متوفرة: {str(ie)}", "خطأ", "error")
                traceback.print_exc()
            except Exception as print_err:
                 show_custom_message(self, f"حدث خطأ أثناء محاولة طباعة سجل المخالفات:\n{str(print_err)}", "خطأ طباعة", "error")
                 traceback.print_exc()


        except Exception as e:
            show_custom_message(self, f"حدث خطأ أثناء تجهيز طباعة سجل المخالفات:\n{str(e)}", "خطأ", "error")
            traceback.print_exc()

    def print_violations_records(self):
        """طباعة تقرير بجميع المخالفات للسنة الدراسية الحالية باستخدام print5_test.py"""
        try:
            # --- تحديد السنة الدراسية الحالية ---
            # محاولة الحصول عليها من قاعدة البيانات إذا لم تكن متوفرة
            current_academic_year = None
            try:
                conn_temp = sqlite3.connect(self.db_path)
                cursor_temp = conn_temp.cursor()
                cursor_temp.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
                result = cursor_temp.fetchone()
                if result and result[0]:
                    current_academic_year = result[0]
                conn_temp.close()
            except Exception as db_err:
                print(f"خطأ في قراءة السنة الدراسية من قاعدة البيانات: {db_err}")
                show_custom_message(self, "لم يتم العثور على السنة الدراسية في إعدادات المؤسسة.", "خطأ", "warning")
                return

            if not current_academic_year:
                show_custom_message(self, "السنة الدراسية الحالية غير محددة.", "تنبيه", "warning")
                return

            # --- استدعاء وظيفة الطباعة الجديدة ---
            try:
                from print5_test import print_violations_by_academic_year

                # استدعاء الدالة وتمرير السنة الدراسية ومسار قاعدة البيانات
                success = print_violations_by_academic_year(
                    academic_year=current_academic_year,
                    db_path=self.db_path
                )

                if success:
                    show_custom_message(self, f"تم إنشاء تقرير PDF لمخالفات السنة الدراسية {current_academic_year} بنجاح.", "نجاح", "success")
                elif success is False: # Handle the case where no violations were found
                     show_custom_message(self, f"لا توجد مخالفات مسجلة للسنة الدراسية {current_academic_year}.", "معلومة", "success")
                else: # Handle other errors during printing
                    show_custom_message(self, "حدث خطأ أثناء إنشاء تقرير PDF للمخالفات.", "تنبيه", "warning")

            except ImportError:
                show_custom_message(self, "ملف الطباعة (print5_test.py) أو الوظيفة المطلوبة غير متوفرة.", "خطأ", "error")
            except Exception as print_err:
                show_custom_message(self, f"حدث خطأ أثناء محاولة طباعة تقرير المخالفات السنوي:\n{str(print_err)}", "خطأ طباعة", "error")
                traceback.print_exc()

        except Exception as e:
            show_custom_message(self, f"حدث خطأ عام أثناء تجهيز طباعة تقرير المخالفات:\n{str(e)}", "خطأ", "error")
            traceback.print_exc()

    # إضافة وظيفة تجاوز حدث النقر على الإطار عندما تكون النافذة مدمجة
    def mousePressEvent(self, event):
        # تجاهل أحداث النقر على الإطار عندما تكون النافذة مدمجة
        if self.is_embedded:
            event.accept()
        else:
            super().mousePressEvent(event)

    def mouseDoubleClickEvent(self, event):
        # تجاهل أحداث النقر المزدوج عندما تكون النافذة مدمجة
        if self.is_embedded:
            event.accept()
        else:
            super().mouseDoubleClickEvent(event)

# إضافة فئة مساعدة للتنسيق
class NoBackgroundProxyStyle(QProxyStyle):
    """أسلوب وكيل لإزالة خلفية العناصر في العرض"""
    def __init__(self, style):
        super().__init__(style)

    def drawPrimitive(self, element, option, painter, widget=None):
        """رسم العناصر الأساسية مع تعديلات خاصة"""
        if (element == self.PE_PanelItemViewItem):
            # استخدام نسخة معدلة من الخيارات بدون خلفية
            modified_option = QStyleOptionViewItem(option)
            # إزالة StateFlag لإزالة الخلفية الافتراضية
            if not (option.state & QStyle.State_Selected):
                modified_option.backgroundBrush = QBrush(Qt.transparent)
            super().drawPrimitive(element, modified_option, painter, widget)
        else:
            super().drawPrimitive(element, option, painter, widget)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = ViolationsManagementWindow()
    window.show()
    sys.exit(app.exec_())

