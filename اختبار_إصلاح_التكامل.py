#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح تكامل sub4_window مع main_window
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon

def test_integration_fix():
    """اختبار إصلاح تكامل sub4_window"""
    print("🔧 اختبار إصلاح تكامل sub4_window مع main_window")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    
    try:
        from main_window import MainWindow
        print("✅ تم استيراد MainWindow")
        
        main_window = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # فحص إصلاح التكامل
        def check_integration_fix():
            print("\n🔍 فحص إصلاح التكامل:")
            
            # التحقق من اتصالات قاعدة البيانات
            print("\n📊 فحص اتصالات قاعدة البيانات:")
            
            if hasattr(main_window, 'db'):
                print(f"   ✅ sqlite3.Connection موجود: {type(main_window.db)}")
            else:
                print("   ❌ sqlite3.Connection غير موجود")
            
            if hasattr(main_window, 'qsql_db'):
                print(f"   ✅ QSqlDatabase موجود: {type(main_window.qsql_db)}")
                if main_window.qsql_db.isOpen():
                    print("   ✅ QSqlDatabase متصل")
                else:
                    print("   ❌ QSqlDatabase غير متصل")
            else:
                print("   ❌ QSqlDatabase غير موجود")
            
            # التحقق من تبويب اللوائح والأقسام
            print("\n📋 فحص تبويب اللوائح والأقسام:")
            
            if "lists_sections" in main_window.windows:
                window = main_window.windows["lists_sections"]
                print(f"   📄 نوع النافذة: {type(window).__name__}")
                
                # التحقق من أنها ليست PlaceholderWindow
                if "PlaceholderWindow" in type(window).__name__:
                    print("   ❌ النافذة هي PlaceholderWindow - فشل التكامل")
                    print("   💡 تحقق من رسائل الخطأ في وحدة التحكم")
                    
                else:
                    print("   ✅ النافذة من النوع الصحيح (Sub4Window)")
                    
                    # التحقق من نوع قاعدة البيانات المرسلة
                    if hasattr(window, 'db'):
                        print(f"   📊 نوع قاعدة البيانات في النافذة: {type(window.db)}")
                        
                        # التحقق من أنها QSqlDatabase
                        if "QSqlDatabase" in str(type(window.db)):
                            print("   ✅ النافذة تستخدم QSqlDatabase (صحيح)")
                            
                            # اختبار الاتصال
                            if window.db.isOpen():
                                print("   ✅ اتصال قاعدة البيانات مفتوح")
                            else:
                                print("   ❌ اتصال قاعدة البيانات مغلق")
                                
                        elif "sqlite3" in str(type(window.db)):
                            print("   ⚠️ النافذة تستخدم sqlite3.Connection (قد يسبب مشاكل)")
                        else:
                            print(f"   ❓ نوع غير معروف: {type(window.db)}")
                    else:
                        print("   ❌ النافذة لا تحتوي على اتصال قاعدة البيانات")
                    
                    # اختبار الوظائف الأساسية
                    print("\n🧪 اختبار الوظائف الأساسية:")
                    
                    # اختبار تحميل البيانات
                    try:
                        print("   🔄 اختبار تحميل البيانات...")
                        window.load_initial_data()
                        print("   ✅ تم تحميل البيانات بنجاح")
                    except Exception as e:
                        print(f"   ❌ خطأ في تحميل البيانات: {e}")
                    
                    # اختبار الجداول
                    if hasattr(window, 'table_lists'):
                        print("   ✅ جدول اللوائح موجود")
                        
                        # التحقق من النموذج
                        if hasattr(window, 'model_lists'):
                            row_count = window.model_lists.rowCount()
                            print(f"   📊 عدد الصفوف في النموذج: {row_count}")
                        else:
                            print("   ❌ نموذج البيانات غير موجود")
                    else:
                        print("   ❌ جدول اللوائح غير موجود")
                        
            else:
                print("   ❌ النافذة غير موجودة في قاموس النوافذ")
            
            # اختبار النقر على التبويب
            print("\n🎯 اختبار النقر على التبويب:")
            
            # البحث عن التبويب
            lists_sections_index = -1
            tab_count = main_window.tabWidget.count()
            
            for i in range(tab_count):
                tab_text = main_window.tabWidget.tabText(i)
                tab_data = main_window.tabWidget.tabData(i)
                
                if tab_text == "اللوائح والأقسام" or tab_data == "lists_sections":
                    lists_sections_index = i
                    print(f"   ✅ تم العثور على التبويب في الفهرس {i}")
                    break
            
            if lists_sections_index >= 0:
                print("   🎯 محاولة النقر على التبويب...")
                
                # محاكاة النقر على التبويب
                main_window.tabWidget.setCurrentIndex(lists_sections_index)
                current_index = main_window.tabWidget.currentIndex()
                
                if current_index == lists_sections_index:
                    print("   ✅ تم التنقل للتبويب بنجاح!")
                    print("   📋 راقب النافذة للتأكد من ظهور البيانات")
                else:
                    print("   ❌ فشل في التنقل للتبويب")
            else:
                print("   ❌ لم يتم العثور على التبويب")
        
        # فحص الإصلاح بعد ثانية واحدة
        QTimer.singleShot(1000, check_integration_fix)
        
        main_window.show()
        main_window.setWindowTitle("اختبار إصلاح التكامل - sub4_window")
        
        print("\n📋 تعليمات الاختبار:")
        print("1. راقب رسائل وحدة التحكم")
        print("2. تحقق من نوع قاعدة البيانات المستخدمة")
        print("3. انقر على تبويب 'اللوائح والأقسام'")
        print("4. تحقق من ظهور البيانات والجداول")
        print("5. جرب تحديد مستوى وقسم")
        
        print("\n🔧 الإصلاحات المطبقة:")
        print("   ✅ إضافة QSqlDatabase للنوافذ المتقدمة")
        print("   ✅ الاحتفاظ بـ sqlite3.Connection للنوافذ البسيطة")
        print("   ✅ تمرير QSqlDatabase لـ sub4_window")
        print("   ✅ إغلاق الاتصالات عند إنهاء البرنامج")
        
        print("\n📊 النتائج المتوقعة:")
        print("   ✅ النافذة من نوع Sub4Window وليس PlaceholderWindow")
        print("   ✅ النافذة تستخدم QSqlDatabase")
        print("   ✅ تحميل البيانات يعمل بدون أخطاء")
        print("   ✅ الجداول تظهر البيانات")
        print("   ✅ جميع الوظائف تعمل كما في النافذة المنفصلة")
        
        print("\n⚠️ ملاحظات:")
        print("   • sub4_window مصمم للعمل مع QSqlDatabase")
        print("   • sqlite3.Connection لا يتوافق مع QSqlQuery")
        print("   • الحل: استخدام QSqlDatabase للنوافذ المتقدمة")
        print("   • sqlite3.Connection للنوافذ البسيطة فقط")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return 1

def show_fix_details():
    """عرض تفاصيل الإصلاح"""
    print("\n" + "=" * 60)
    print("🔧 تفاصيل إصلاح التكامل")
    print("=" * 60)
    
    fix_details = [
        ("المشكلة", "sub4_window يعمل منفصلاً لكن لا يعمل مدمجاً"),
        ("السبب", "تمرير sqlite3.Connection بدلاً من QSqlDatabase"),
        ("الحل", "إنشاء QSqlDatabase منفصل للنوافذ المتقدمة"),
        ("النتيجة", "sub4_window يعمل بكامل وظائفه مدمجاً")
    ]
    
    print("\n🔧 تفاصيل الإصلاح:")
    for title, detail in fix_details:
        print(f"   {title}: {detail}")
    
    print("\n📝 التغييرات المطبقة:")
    print("   1. إضافة QSqlDatabase في main_window:")
    print("      self.qsql_db = QSqlDatabase.addDatabase('QSQLITE', 'main_connection')")
    print()
    print("   2. تمرير QSqlDatabase لـ sub4_window:")
    print("      Sub4Window(db=self.qsql_db, ...)")
    print()
    print("   3. إغلاق الاتصالات عند الإنهاء:")
    print("      self.qsql_db.close()")

if __name__ == "__main__":
    print("🔧 مرحباً بك في اختبار إصلاح التكامل!")
    
    # عرض تفاصيل الإصلاح
    show_fix_details()
    
    print("\n" + "=" * 60)
    print("🚀 بدء تشغيل الاختبار...")
    print("=" * 60)
    
    # تشغيل الاختبار
    exit_code = test_integration_fix()
    
    print(f"\n🔚 انتهى الاختبار بكود الخروج: {exit_code}")
    sys.exit(exit_code)
