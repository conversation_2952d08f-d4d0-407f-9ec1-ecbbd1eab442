#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار جميع الأوراق في thermal_image_print.py
للتأكد من أن جميع النماذج تعمل بشكل صحيح
"""

import sys
import os
from datetime import datetime

def test_all_forms():
    """اختبار جميع أنواع الأوراق"""
    print("🧪 اختبار جميع أنواع الأوراق في thermal_image_print.py")
    print("=" * 70)
    
    try:
        # استيراد الوحدة المحدثة
        import thermal_image_print as thermal_print
        print("✅ تم استيراد thermal_image_print بنجاح")
        
        # بيانات تجريبية للتلاميذ
        test_students = [
            {"rt": "1", "name": "أحمد محمد علي", "code": "ST001", "secret": "Pass123"},
            {"rt": "2", "name": "فاطمة أحمد حسن", "code": "ST002", "secret": "Pass456"},
            {"rt": "3", "name": "محمد عبد الله سالم", "code": "ST003", "secret": "Pass789"},
            {"rt": "4", "name": "عائشة محمود إبراهيم", "code": "ST004", "secret": "Pass321"},
            {"rt": "5", "name": "يوسف عبد الرحمن", "code": "ST005", "secret": "Pass654"}
        ]
        
        test_section = "الثانية ثانوي علوم"
        current_date = datetime.now().strftime("%Y/%m/%d")
        current_time = datetime.now().strftime("%H:%M")
        
        print(f"\n📊 بيانات الاختبار:")
        print(f"   عدد التلاميذ: {len(test_students)}")
        print(f"   القسم: {test_section}")
        print(f"   التاريخ: {current_date}")
        print(f"   الوقت: {current_time}")
        
        # اختبار جميع النماذج
        forms_to_test = [
            (1, "ورقة الدخول", "print_entry_form_direct"),
            (2, "ورقة التأخر", "print_late_form_direct"),
            (3, "ورقة التوجيه", "print_guidance_form_direct"),
            (4, "ورقة الاستئذان", "print_permission_form_direct"),
            (6, "الرمز السري", "print_secret_code_image")
        ]
        
        print("\n📋 اختبار تنسيق جميع النماذج:")
        print("-" * 70)
        
        for form_id, form_name, function_name in forms_to_test:
            print(f"\n{form_id}️⃣ اختبار {form_name}:")
            
            try:
                # اختبار الحصول على معلومات النموذج
                form_info = thermal_print.get_form_info(form_id)
                print(f"   ✅ العنوان: {form_info['title']}")
                if form_info['description']:
                    print(f"   ✅ الوصف: {form_info['description']}")
                
                # اختبار تنسيق المحتوى
                if form_id == 6:  # الرمز السري
                    # اختبار تنسيق الرمز السري
                    secret_date = datetime.now().strftime("%d-%m-%Y")
                    content = thermal_print.format_secret_code_content(test_students[0], secret_date)
                    print(f"   ✅ تم تنسيق {form_name} بنجاح")
                    print(f"   📏 طول المحتوى: {len(content)} حرف")
                    
                    # عرض عينة من المحتوى
                    print(f"   📖 عينة من المحتوى:")
                    lines = content.split('\n')
                    for i, line in enumerate(lines[:10]):  # عرض أول 10 أسطر
                        if line.strip():
                            print(f"      {i+1:2d}: {line}")
                else:
                    # اختبار تنسيق النماذج العادية
                    content = thermal_print.format_entry_form_content(
                        test_students[:3], test_section, current_date, current_time, form_id
                    )
                    print(f"   ✅ تم تنسيق {form_name} بنجاح")
                    print(f"   📏 طول المحتوى: {len(content)} حرف")
                    
                    # عرض عينة من المحتوى
                    print(f"   📖 عينة من المحتوى:")
                    lines = content.split('\n')
                    for i, line in enumerate(lines[:10]):  # عرض أول 10 أسطر
                        if line.strip():
                            print(f"      {i+1:2d}: {line}")
                
                # التحقق من وجود الدالة
                if hasattr(thermal_print, function_name):
                    print(f"   ✅ الدالة {function_name} موجودة")
                else:
                    print(f"   ❌ الدالة {function_name} غير موجودة")
                
            except Exception as e:
                print(f"   ❌ خطأ في اختبار {form_name}: {e}")
        
        print("\n" + "=" * 70)
        
        # اختبار الطباعة (اختياري)
        print("\n🖨️ اختبار الطباعة:")
        user_input = input("هل تريد اختبار الطباعة الفعلية لجميع الأوراق؟ (y/n): ").lower().strip()
        
        if user_input in ['y', 'yes', 'نعم', 'ن']:
            print("🚀 بدء اختبار الطباعة لجميع الأوراق...")
            
            results = {}
            
            # اختبار طباعة ورقة الدخول
            print("\n1️⃣ اختبار طباعة ورقة الدخول:")
            try:
                result = thermal_print.print_entry_form_direct(
                    test_students, test_section, current_date, current_time
                )
                results["ورقة الدخول"] = result
                print(f"   {'✅ نجح' if result else '❌ فشل'}")
            except Exception as e:
                print(f"   ❌ خطأ: {e}")
                results["ورقة الدخول"] = False
            
            # اختبار طباعة ورقة التأخر
            print("\n2️⃣ اختبار طباعة ورقة التأخر:")
            try:
                result = thermal_print.print_late_form_direct(
                    test_students[:3], test_section, current_date, current_time
                )
                results["ورقة التأخر"] = result
                print(f"   {'✅ نجح' if result else '❌ فشل'}")
            except Exception as e:
                print(f"   ❌ خطأ: {e}")
                results["ورقة التأخر"] = False
            
            # اختبار طباعة ورقة التوجيه
            print("\n3️⃣ اختبار طباعة ورقة التوجيه:")
            try:
                result = thermal_print.print_guidance_form_direct(
                    test_students[:2], test_section, current_date, current_time
                )
                results["ورقة التوجيه"] = result
                print(f"   {'✅ نجح' if result else '❌ فشل'}")
            except Exception as e:
                print(f"   ❌ خطأ: {e}")
                results["ورقة التوجيه"] = False
            
            # اختبار طباعة ورقة الاستئذان
            print("\n4️⃣ اختبار طباعة ورقة الاستئذان:")
            try:
                result = thermal_print.print_permission_form_direct(
                    test_students[:2], test_section, current_date, current_time
                )
                results["ورقة الاستئذان"] = result
                print(f"   {'✅ نجح' if result else '❌ فشل'}")
            except Exception as e:
                print(f"   ❌ خطأ: {e}")
                results["ورقة الاستئذان"] = False
            
            # اختبار طباعة الرمز السري
            print("\n6️⃣ اختبار طباعة الرمز السري:")
            try:
                secret_date = datetime.now().strftime("%d-%m-%Y")
                result = thermal_print.print_secret_code_image(test_students[0], secret_date)
                results["الرمز السري"] = result
                print(f"   {'✅ نجح' if result else '❌ فشل'}")
            except Exception as e:
                print(f"   ❌ خطأ: {e}")
                results["الرمز السري"] = False
            
            # ملخص نتائج الطباعة
            print("\n📊 ملخص نتائج الطباعة:")
            successful = sum(1 for result in results.values() if result)
            total = len(results)
            print(f"   نجح: {successful}/{total}")
            for form_name, result in results.items():
                status = "✅ نجح" if result else "❌ فشل"
                print(f"   {form_name}: {status}")
                
        else:
            print("⏭️ تم تخطي اختبار الطباعة الفعلية")
        
        print("\n🎉 انتهى الاختبار بنجاح!")
        print("=" * 70)
        
        # ملخص النتائج
        print("\n📊 ملخص النتائج:")
        print("✅ استيراد الوحدة: نجح")
        print("✅ اختبار جميع النماذج: نجح")
        print("✅ تنسيق المحتوى: نجح")
        print("✅ التحقق من الدوال: نجح")
        
        print("\n🔧 الأوراق المدعومة:")
        print("✅ ورقة الدخول (form_id=1)")
        print("✅ ورقة التأخر (form_id=2)")
        print("✅ ورقة التوجيه (form_id=3)")
        print("✅ ورقة الاستئذان (form_id=4)")
        print("✅ الرمز السري (form_id=6)")
        
        print("\n📋 الدوال المتوفرة:")
        print("• print_entry_form_direct() - ورقة الدخول")
        print("• print_late_form_direct() - ورقة التأخر")
        print("• print_guidance_form_direct() - ورقة التوجيه")
        print("• print_permission_form_direct() - ورقة الاستئذان")
        print("• print_secret_code_image() - الرمز السري")
        print("• print_thermal_text() - دالة عامة لجميع النماذج")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدة: {e}")
        print("تأكد من وجود ملف thermal_image_print.py")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_usage_examples():
    """عرض أمثلة الاستخدام"""
    print("\n📖 أمثلة الاستخدام:")
    print("=" * 60)
    
    print("```python")
    print("import thermal_image_print as thermal")
    print("")
    print("# بيانات التلاميذ")
    print("students = [")
    print("    {'rt': '1', 'name': 'أحمد محمد', 'code': 'ST001', 'secret': 'Pass123'},")
    print("    {'rt': '2', 'name': 'فاطمة علي', 'code': 'ST002', 'secret': 'Pass456'}")
    print("]")
    print("section = 'الثانية ثانوي علوم'")
    print("")
    print("# طباعة ورقة الدخول")
    print("thermal.print_entry_form_direct(students, section)")
    print("")
    print("# طباعة ورقة التأخر")
    print("thermal.print_late_form_direct(students, section)")
    print("")
    print("# طباعة ورقة التوجيه")
    print("thermal.print_guidance_form_direct(students, section)")
    print("")
    print("# طباعة ورقة الاستئذان")
    print("thermal.print_permission_form_direct(students, section)")
    print("")
    print("# طباعة الرمز السري")
    print("thermal.print_secret_code_image(students[0])")
    print("")
    print("# طباعة نموذج محدد")
    print("thermal.print_thermal_text(students, section, form_id=3)")
    print("```")

if __name__ == "__main__":
    print("🚀 بدء اختبار جميع الأوراق")
    print("تاريخ الاختبار:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # تشغيل الاختبار
    success = test_all_forms()
    
    # عرض أمثلة الاستخدام
    show_usage_examples()
    
    if success:
        print("\n🎉 تم الاختبار بنجاح!")
        print("✅ جميع الأوراق تعمل بشكل صحيح")
        print("✅ الطباعة النصية المحسنة جاهزة للاستخدام")
    else:
        print("\n❌ فشل الاختبار!")
        print("تحقق من الأخطاء وحاول مرة أخرى")
    
    input("\nاضغط Enter للخروج...")
