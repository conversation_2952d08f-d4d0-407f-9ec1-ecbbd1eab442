"""
النظام التعليمي الشامل - النوافذ المنفصلة
نافذة رئيسية بسيطة تفتح النوافذ المنفصلة

الميزات:
- نافذة رئيسية بسيطة وأنيقة
- فتح النوافذ المنفصلة حسب الحاجة
- أداء ممتاز وذاكرة أقل
- سهولة الصيانة والتطوير
- واجهة HTML جميلة ومتجاوبة
"""

import sys
import os
import json
import sqlite3
from datetime import datetime
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal, QTimer, QDateTime
from PyQt5.QtGui import QIcon

class MainSystemEngine(QObject):
    """محرك النظام الرئيسي - مسؤول عن فتح النوافذ المنفصلة"""

    # إشارات للتواصل مع واجهة HTML
    logUpdated = pyqtSignal(str, str, str)  # message, status, timestamp
    systemStatusUpdated = pyqtSignal(str)  # system status JSON

    def __init__(self):
        super().__init__()
        self.db_path = "data.db"
        self.main_window = None          # مراجع للنوافذ المنفصلة
        self.import_window = None
        self.settings_window = None
        self.institution_window = None
        self.statistics_window = None
        self.printer_settings_window = None  # نافذة إعدادات الطابعات
        self.data_edit_window = None  # نافذة تعديل المسميات
        self.sections_management_window = None  # نافذة البنية التربوية

    def emit_log(self, message, status="info"):
        """إرسال رسالة إلى واجهة HTML"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.logUpdated.emit(message, status, timestamp)

    @pyqtSlot(result=str)
    def getSystemStatus(self):
        """الحصول على حالة النظام"""
        try:
            status = {
                "database": {
                    "exists": os.path.exists(self.db_path),
                    "size_mb": self.get_file_size(self.db_path) if os.path.exists(self.db_path) else 0,
                    "tables_count": self.get_tables_count()
                },                "windows": {
                    "import_available": True,
                    "settings_available": True,
                    "institution_available": True,
                    "statistics_available": True,
                    "printer_settings_available": True
                },
                "system_info": {
                    "version": "3.0 - النوافذ المنفصلة",
                    "last_update": "يونيو 2025",
                    "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
                }
            }
            return json.dumps(status, ensure_ascii=False)
        except Exception as e:
            self.emit_log(f"خطأ في جمع حالة النظام: {str(e)}", "error")
            return json.dumps({"error": str(e)}, ensure_ascii=False)

    def get_file_size(self, file_path):
        """الحصول على حجم الملف بالميجابايت"""
        try:
            size_bytes = os.path.getsize(file_path)
            return round(size_bytes / (1024 * 1024), 2)
        except:
            return 0

    def get_tables_count(self):
        """عدد الجداول في قاعدة البيانات"""
        try:
            if not os.path.exists(self.db_path):
                return 0

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            count = cursor.fetchone()[0]
            conn.close()
            return count
        except:
            return 0

    @pyqtSlot(result=str)
    def getDatabaseStatistics(self):
        """الحصول على إحصائيات قاعدة البيانات"""
        try:
            if not os.path.exists(self.db_path):
                stats = {
                    "students_count": 0,
                    "teachers_count": 0,
                    "sections_count": 0,
                    "levels_count": 0,
                    "secret_codes_count": 0,
                    "current_year": "غير محدد"
                }
            else:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                stats = {}
                
                try:
                    # السنة الدراسية الحالية
                    cursor.execute("SELECT DISTINCT السنة_الدراسية FROM اللوائح ORDER BY السنة_الدراسية DESC LIMIT 1")
                    result = cursor.fetchone()
                    stats['current_year'] = result[0] if result and result[0] else "غير محدد"
                    
                    # إحصائيات الطلاب
                    cursor.execute("SELECT COUNT(*) FROM اللوائح")
                    stats['students_count'] = cursor.fetchone()[0]
                    
                    # إحصائيات الأساتذة
                    cursor.execute("SELECT COUNT(*) FROM الأساتذة")
                    stats['teachers_count'] = cursor.fetchone()[0]
                    
                    # إحصائيات الأقسام
                    cursor.execute("SELECT COUNT(DISTINCT القسم) FROM اللوائح")
                    stats['sections_count'] = cursor.fetchone()[0]
                    
                    # إحصائيات المستويات
                    cursor.execute("SELECT COUNT(DISTINCT المستوى) FROM اللوائح")
                    stats['levels_count'] = cursor.fetchone()[0]
                      # إحصائيات الرموز السرية
                    cursor.execute("SELECT COUNT(*) FROM الرمز_السري")
                    stats['secret_codes_count'] = cursor.fetchone()[0]
                    
                except Exception as e:
                    print(f"خطأ في جمع الإحصائيات: {e}")
                    stats = {
                        "students_count": 0,
                        "teachers_count": 0,
                        "sections_count": 0,
                        "levels_count": 0,
                        "secret_codes_count": 0,
                        "current_year": "غير محدد"
                    }
                finally:
                    conn.close()
            
            return json.dumps(stats, ensure_ascii=False)
        except Exception as e:
            self.emit_log(f"خطأ في جمع إحصائيات قاعدة البيانات: {str(e)}", "error")
            return json.dumps({"error": str(e)}, ensure_ascii=False)

    @pyqtSlot()
    def openImportWindow(self):
        """فتح نافذة الاستيراد المنفصلة الحقيقية - sub1_window_html.py"""
        self.emit_log("🔄 فتح نافذة استيراد البيانات...", "info")
        
        try:
            from sub1_window_html import ModernSub1Window
            
            # إنشاء نافذة الاستيراد إذا لم تكن موجودة أو إذا كانت مغلقة
            if not hasattr(self, 'import_window') or self.import_window is None or not self.import_window.isVisible():
                self.import_window = ModernSub1Window(parent=None)  # نافذة منفصلة
            
            # عرض النافذة في وسط الشاشة كمشروطة
            self.import_window.show()
            self.import_window.activateWindow()
            self.import_window.raise_()
            
            self.emit_log("✅ تم فتح نافذة استيراد البيانات بنجاح", "success")
            
        except ImportError:
            self.emit_log("❌ خطأ: ملف sub1_window_html.py غير موجود", "error")
        except Exception as e:
            self.emit_log(f"❌ خطأ في فتح نافذة الاستيراد: {str(e)}", "error")

    @pyqtSlot()
    def openSettingsWindow(self):
        """فتح نافذة الإعدادات المدمجة"""
        self.emit_log("🔄 فتح نافذة الإعدادات المدمجة...", "info")
        
        try:
            from sub8_window import Sub8Window
            
            # إنشاء نافذة الإعدادات كنافذة مدمجة
            if not hasattr(self, 'settings_window') or self.settings_window is None:
                # تمرير النافذة الأم كـ parent لجعلها نافذة مدمجة
                self.settings_window = Sub8Window(parent=self.main_window)
            
            # عرض النافذة كنافذة مدمجة
            if hasattr(self.settings_window, 'exec_'):
                # استخدام exec_ لنافذة modal مدمجة
                self.emit_log("✅ تم فتح نافذة الإعدادات المدمجة بنجاح", "success")
                self.settings_window.exec_()
            elif hasattr(self.settings_window, 'show'):
                self.settings_window.show()
                self.settings_window.activateWindow()
                self.settings_window.raise_()
                self.emit_log("✅ تم فتح نافذة الإعدادات المدمجة بنجاح", "success")
            else:
                self.emit_log("❌ خطأ: نافذة الإعدادات لا تحتوي على دالة عرض", "error")                
        except ImportError:
            self.emit_log("❌ خطأ: ملف sub8_window.py غير موجود", "error")
        except Exception as e:
            self.emit_log(f"❌ خطأ في فتح نافذة الإعدادات: {str(e)}", "error")

    @pyqtSlot()
    def openInstitutionWindow(self):
        """فتح نافذة بيانات المؤسسة المدمجة"""
        self.emit_log("🔄 فتح نافذة بيانات المؤسسة المدمجة...", "info")
        
        try:
            from sub2_window_html import InstitutionWindow
              # إنشاء نافذة المؤسسة كنافذة مدمجة
            if not hasattr(self, 'institution_window') or self.institution_window is None:
                # تمرير النافذة الأم كـ parent لجعلها نافذة مدمجة
                self.institution_window = InstitutionWindow(parent=self.main_window)
              # عرض النافذة كنافذة مدمجة
            if hasattr(self.institution_window, 'exec_'):
                # استخدام exec_ لنافذة modal مدمجة
                self.emit_log("✅ تم فتح نافذة بيانات المؤسسة المدمجة بنجاح", "success")
                self.institution_window.exec_()
            elif hasattr(self.institution_window, 'show'):
                self.institution_window.show()
                self.institution_window.activateWindow()
                self.institution_window.raise_()
                self.emit_log("✅ تم فتح نافذة بيانات المؤسسة المدمجة بنجاح", "success")
            else:
                self.emit_log("❌ خطأ: نافذة المؤسسة لا تحتوي على دالة عرض", "error")
                
        except ImportError:
            self.emit_log("❌ خطأ: ملف sub2_window_html.py غير موجود", "error")
        except Exception as e:
            self.emit_log(f"❌ خطأ في فتح نافذة بيانات المؤسسة: {str(e)}", "error")

    @pyqtSlot()
    def openStatisticsWindow(self):
        """فتح نافذة الإحصائيات المنفصلة في كامل الشاشة"""
        self.emit_log("🔄 فتح نافذة الإحصائيات المنفصلة...", "info")
        
        try:
            from sub5_window_html import StatisticsWindow
            
            # إنشاء نافذة الإحصائيات إذا لم تكن موجودة أو إذا كانت مغلقة
            if not hasattr(self, 'statistics_window') or self.statistics_window is None or not self.statistics_window.isVisible():
                self.statistics_window = StatisticsWindow(parent=None)  # نافذة منفصلة
            
            # عرض النافذة في كامل الشاشة
            self.statistics_window.showMaximized()
            self.statistics_window.activateWindow()
            self.statistics_window.raise_()
            
            self.emit_log("✅ تم فتح نافذة الإحصائيات المنفصلة بنجاح", "success")
            
        except ImportError:
            self.emit_log("❌ خطأ: ملف sub5_window_html.py غير موجود", "error")
        except Exception as e:
            self.emit_log(f"❌ خطأ في فتح نافذة الإحصائيات: {str(e)}", "error")

    @pyqtSlot()
    def openPrinterSettingsWindow(self):
        """فتح نافذة إعدادات الطابعات المنفصلة في وسط الشاشة"""
        self.emit_log("🔄 فتح نافذة إعدادات الطابعات المنفصلة...", "info")
        
        try:
            from sub7_window_html import PrinterSettingsWindow
            
            # إنشاء نافذة إعدادات الطابعات إذا لم تكن موجودة أو إذا كانت مغلقة
            if not hasattr(self, 'printer_settings_window') or self.printer_settings_window is None or not self.printer_settings_window.isVisible():
                self.printer_settings_window = PrinterSettingsWindow(parent=None)  # نافذة منفصلة
            
            # عرض النافذة في وسط الشاشة
            self.printer_settings_window.show()
            self.printer_settings_window.activateWindow()
            self.printer_settings_window.raise_()
            
            # تحريك النافذة إلى وسط الشاشة
            if hasattr(self.printer_settings_window, 'move') and hasattr(self, 'main_window'):
                screen = self.main_window.screen()
                screen_rect = screen.availableGeometry()
                window_rect = self.printer_settings_window.geometry()
                center_x = (screen_rect.width() - window_rect.width()) // 2
                center_y = (screen_rect.height() - window_rect.height()) // 2
                self.printer_settings_window.move(center_x, center_y)
            
            self.emit_log("✅ تم فتح نافذة إعدادات الطابعات المنفصلة بنجاح", "success")
            
        except ImportError:
            self.emit_log("❌ خطأ: ملف sub7_window_html.py غير موجود", "error")
        except Exception as e:
            self.emit_log(f"❌ خطأ في فتح نافذة إعدادات الطابعات: {str(e)}", "error")

    @pyqtSlot()
    def openDataEditWindow(self):
        """فتح نافذة تعديل المسميات المنفصلة"""
        self.emit_log("🔄 فتح نافذة تعديل المسميات...", "info")
        
        try:
            from sub6_window_html import DataEditWindow
            
            # إنشاء نافذة تعديل المسميات إذا لم تكن موجودة أو إذا كانت مغلقة
            if not hasattr(self, 'data_edit_window') or self.data_edit_window is None or not self.data_edit_window.isVisible():
                self.data_edit_window = DataEditWindow()
            
            # عرض النافذة في وسط الشاشة
            if hasattr(self.data_edit_window, 'show'):
                self.data_edit_window.show()
                self.data_edit_window.activateWindow()
                self.data_edit_window.raise_()
                
                # تحريك النافذة إلى وسط الشاشة
                if hasattr(self.data_edit_window, 'move'):
                    screen = self.main_window.screen()
                    screen_rect = screen.availableGeometry()
                    window_rect = self.data_edit_window.geometry()
                    center_x = (screen_rect.width() - window_rect.width()) // 2
                    center_y = (screen_rect.height() - window_rect.height()) // 2
                    self.data_edit_window.move(center_x, center_y)
                
                self.emit_log("✅ تم فتح نافذة تعديل المسميات بنجاح", "success")
            else:
                self.emit_log("❌ خطأ: نافذة تعديل المسميات لا تحتوي على دالة عرض", "error")
                
        except ImportError:
            self.emit_log("❌ خطأ: ملف sub6_window_html.py غير موجود", "error")
        except Exception as e:
            self.emit_log(f"❌ خطأ في فتح نافذة تعديل المسميات: {str(e)}", "error")

    @pyqtSlot()
    def openSectionsManagementWindow(self):
        """فتح نافذة البنية التربوية - إدارة الأقسام المسندة في كامل الشاشة"""
        self.emit_log("🔄 فتح نافذة البنية التربوية...", "info")

        try:
            print("🔧 محاولة استيراد SectionsManagementWindow...")
            from sub3_window_html import SectionsManagementWindow
            print("✅ تم استيراد SectionsManagementWindow بنجاح")

            # إنشاء نافذة البنية التربوية إذا لم تكن موجودة أو إذا كانت مغلقة
            if not hasattr(self, 'sections_management_window') or self.sections_management_window is None or not self.sections_management_window.isVisible():
                print("🔧 إنشاء نافذة جديدة...")
                self.sections_management_window = SectionsManagementWindow(parent=None)  # نافذة منفصلة
                print("✅ تم إنشاء النافذة بنجاح")

            # عرض النافذة في كامل الشاشة
            print("🔧 عرض النافذة...")
            self.sections_management_window.showMaximized()
            self.sections_management_window.activateWindow()
            self.sections_management_window.raise_()
            print("✅ تم عرض النافذة بنجاح")

            self.emit_log("✅ تم فتح نافذة البنية التربوية في كامل الشاشة بنجاح", "success")

        except ImportError as e:
            error_msg = f"❌ خطأ في الاستيراد: {str(e)}"
            print(error_msg)
            self.emit_log(error_msg, "error")
        except Exception as e:
            error_msg = f"❌ خطأ في فتح نافذة البنية التربوية: {str(e)}"
            print(error_msg)
            self.emit_log(error_msg, "error")

    @pyqtSlot()
    def showAbout(self):
        """عرض معلومات حول النظام"""
        about_text = """
🎓 النظام التعليمي الشامل - النوافذ المنفصلة

النسخة: 3.0
التطوير: يونيو 2025
المطور: نظم التعليم الحديثة

الميزات:
• نافذة رئيسية بسيطة وأنيقة
• نوافذ منفصلة لكل وحدة
• أداء ممتاز وذاكرة أقل
• سهولة الصيانة والتطوير
• واجهة HTML جميلة ومتجاوبة

الوحدات المتاحة:
📥 نافذة الاستيراد
⚙️ نافذة الإعدادات  
🏢 نافذة بيانات المؤسسة
📊 نافذة الإحصائيات العامة
🖨️ نافذة إعدادات الطابعات
📝 نافذة تعديل المسميات
        """
        self.emit_log(about_text, "info")

class MainSystemWindow(QMainWindow):
    """النافذة الرئيسية للنظام"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎓المعين في الحراسة العامة ")
        
        # فتح النافذة في كامل الشاشة
        self.showMaximized()

        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # إنشاء محرك النظام
        self.system_engine = MainSystemEngine()
        self.system_engine.main_window = self

        # إعداد الواجهة
        self.setup_ui()
        self.setup_web_channel()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء عرض الويب
        self.web_view = QWebEngineView()
        layout.addWidget(self.web_view)

        # تحميل واجهة HTML
        html_content = self.get_complete_html()
        self.web_view.setHtml(html_content)

    def setup_web_channel(self):
        """إعداد قناة التواصل مع JavaScript"""
        self.channel = QWebChannel()
        self.channel.registerObject("systemEngine", self.system_engine)
        self.web_view.page().setWebChannel(self.channel)        # انتظار تحميل الصفحة قبل إعداد القناة
        self.web_view.loadFinished.connect(self.on_page_loaded)
    
    def on_page_loaded(self):
        """استدعاء عند انتهاء تحميل الصفحة"""
        # إعادة تسجيل الكائن للتأكد من الربط
        self.channel.registerObject("systemEngine", self.system_engine)
        
        # تأخير إضافي لضمان اكتمال التسجيل
        QTimer.singleShot(500, self.ensure_object_registration)
        
        # طباعة أسماء الدوال المتاحة للتصحيح
        print("🔧 الدوال المتاحة في systemEngine:")
        for method_name in dir(self.system_engine):
            if not method_name.startswith('_') and callable(getattr(self.system_engine, method_name)):
                print(f"   - {method_name}")
        print("=" * 50)

    def ensure_object_registration(self):
        """ضمان تسجيل الكائن بشكل صحيح"""
        try:
            # إلغاء تسجيل الكائن أولاً
            self.channel.deregisterObject(self.system_engine)
        except:
            pass
        
        # إعادة تسجيل الكائن
        self.channel.registerObject("systemEngine", self.system_engine)
        print("🔄 تم إعادة تسجيل systemEngine للتأكد من الربط الصحيح")

    def get_complete_html(self):
        """HTML كامل مع CSS و JavaScript"""
        return """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8">
    <title>النظام التعليمي الشامل - النوافذ المنفصلة</title>
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0099CC;
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }        .header h1 {
            font-family: 'Calibri', sans-serif;
            font-size: 32pt;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            color: darkwhite;
        }        .header p {
            font-family: 'Calibri', sans-serif;
            font-size: 20pt;
            font-weight: bold;
            opacity: 1;
            color: darkblack;
        }.system-info {
            background: rgba(255,255,255,0.7);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
            color: #2c3e50;
            border: 1px solid rgba(52, 73, 94, 0.1);
        }        .system-info h3 {
            font-family: 'Calibri', sans-serif;
            font-size: 22pt;
            font-weight: bold;
            color: darkwhite;
            margin-bottom: 10px;
        }

        .system-info p {
            font-family: 'Calibri', sans-serif;
            font-size: 22pt;
            font-weight: bold;
            color: darkblack;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }        .stat-card {
            background: linear-gradient(135deg, #87ceeb 0%, #b0e0e6 100%);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            color: #2c3e50;
            border: 1px solid rgba(135, 206, 235, 0.3);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }        .year-card {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%) !important;
            border: 1px solid rgba(255, 154, 158, 0.4) !important;
            box-shadow: 0 8px 20px rgba(255, 154, 158, 0.3) !important;
        }

        .year-card .stat-number {
            color: #c0392b !important;
        }

        .year-card .stat-label {
            color: #2c3e50 !important;
        }.stat-number {
            display: block;
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
            color: #1e5799;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
            color: #2c3e50;
        }

        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }        .module-card {
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(52, 73, 94, 0.1);
        }        .module-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .module-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }        .module-description {
            color: #7f8c8d;
            line-height: 1.5;
        }        .footer {
            text-align: center;
            color: rgba(52, 73, 94, 0.8);
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid rgba(52, 73, 94, 0.2);
        }

        @media (max-width: 768px) {
            .modules-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- الرأس -->
        <div class="header">
            <h1> المعين في الحراسة العامة</h1>
            <p>نسخة متطورة -  3.0</p>
        </div>        <!-- معلومات النظام -->
        <div class="system-info">
            <h3>💡 الميزات الجديدة</h3>
            <p>نظام متكامل وذكي بواجهة حديثة لإدارة بيانات التلاميذ بكفاءة واحتراف</p>
        </div><!-- الإحصائيات -->
        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <span class="stat-number" id="studentsCount">0</span>
                <span class="stat-label">👥 الطلاب</span>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="teachersCount">0</span>
                <span class="stat-label">👨‍🏫 الأساتذة</span>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="sectionsCount">0</span>
                <span class="stat-label">🏫 الأقسام</span>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="levelsCount">0</span>
                <span class="stat-label">📚 المستويات</span>
            </div>            <div class="stat-card year-card">
                <span class="stat-number" id="currentYear">غير محدد</span>
                <span class="stat-label">📅 السنة الدراسية</span>
            </div>
        </div>

        <!-- وحدات النظام -->
        <div class="modules-grid">
            <div class="module-card" onclick="openImportWindow()">
                <div class="module-icon">📥</div>
                <div class="module-title">نظام الاستيراد</div>
                <div class="module-description">استيراد البيانات من ملفات Excel ومنظومة مسار</div>
            </div>

            <div class="module-card" onclick="openSettingsWindow()">
                <div class="module-icon">⚙️</div>
                <div class="module-title">الإعدادات</div>
                <div class="module-description">النسخ الاحتياطي والصيانة وإعدادات النظام</div>
            </div>

            <div class="module-card" onclick="openInstitutionWindow()">
                <div class="module-icon">🏢</div>
                <div class="module-title">بيانات المؤسسة</div>
                <div class="module-description">إدارة معلومات المؤسسة والشعار والبيانات</div>
            </div>            <div class="module-card" onclick="openStatisticsWindow()">
                <div class="module-icon">📊</div>
                <div class="module-title">الإحصائيات العامة</div>
                <div class="module-description">تقارير وإحصائيات شاملة مع بطاقات تفاعلية أنيقة</div>
            </div><div class="module-card" onclick="openPrinterSettingsWindow()">
                <div class="module-icon">🖨️</div>
                <div class="module-title">إعدادات الطابعات</div>
                <div class="module-description">إدارة وتكوين إعدادات الطباعة والطابعات المختلفة</div>
            </div>            <div class="module-card" onclick="openDataEditWindow()">
                <div class="module-icon">📝</div>
                <div class="module-title">تعديل المسميات</div>
                <div class="module-description">إدارة وتحرير بيانات المسميات والمخالفات والإجراءات</div>
            </div>            <div class="module-card" onclick="openSectionsManagementWindow()">
                <div class="module-icon">🏫</div>
                <div class="module-title">البنية التربوية</div>
                <div class="module-description">إدارة الأقسام المسندة والحراسات مع واجهة تفاعلية حديثة</div>
            </div>
        </div>

        <!-- التذييل -->        <div class="footer">
            <p>النظام التعليمي الشامل - النوافذ المنفصلة v3.0 | تطوير: نظم التعليم الحديثة</p>
            <p><a href="#" onclick="showAbout()" style="color: rgba(52, 73, 94, 0.8);">معلومات حول النظام</a> | 
               <a href="#" onclick="diagnoseSystemEngine()" style="color: rgba(52, 73, 94, 0.8);">تشخيص الاتصال</a></p>
        </div>
    </div>

    <script>
        let systemEngine = null;
        let isChannelReady = false;        // إعداد قناة التواصل مع Python
        function initializeChannel() {
            if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                new QWebChannel(qt.webChannelTransport, function(channel) {
                    systemEngine = channel.objects.systemEngine;
                    isChannelReady = true;

                    console.log('🚀 QWebChannel initialized successfully');

                    // ربط الإشارات
                    if (systemEngine) {
                        systemEngine.logUpdated.connect(addLogEntry);
                        systemEngine.systemStatusUpdated.connect(updateSystemStatus);

                        // تحميل الإحصائيات
                        loadDatabaseStatistics();

                        // تشخيص الاتصال
                        diagnoseSystemEngine();

                        console.log('✅ تم تهيئة النظام بنجاح');
                    }
                });
            } else {
                console.log('⚠️ QWebChannel not available, retrying...');
                setTimeout(initializeChannel, 100);
            }
        }

        // تحميل إحصائيات قاعدة البيانات
        function loadDatabaseStatistics() {
            if (systemEngine) {
                systemEngine.getDatabaseStatistics(function(result) {
                    try {
                        let stats;
                        if (typeof result === 'string') {
                            stats = JSON.parse(result);
                        } else {
                            stats = result;
                        }
                        
                        if (stats.error) {
                            console.error('Error loading statistics:', stats.error);
                            return;
                        }

                        updateStatistics(stats);
                    } catch (error) {
                        console.error('Error parsing statistics data:', error);
                    }
                });
            }
        }        // تحديث الإحصائيات في الواجهة
        function updateStatistics(stats) {
            document.getElementById('studentsCount').textContent = stats.students_count || 0;
            document.getElementById('teachersCount').textContent = stats.teachers_count || 0;
            document.getElementById('sectionsCount').textContent = stats.sections_count || 0;
            document.getElementById('levelsCount').textContent = stats.levels_count || 0;
            document.getElementById('currentYear').textContent = stats.current_year || 'Not specified';
        }        // إضافة رسالة للسجل
        function addLogEntry(message, status, timestamp) {
            // يمكن إضافة منطق معالجة الرسائل هنا لاحقاً
            console.log(`[${timestamp}] ${status}: ${message}`);
        }        // تحديث حالة النظام
        function updateSystemStatus(statusJson) {
            // يمكن إضافة منطق تحديث حالة النظام هنا
        }

        // تشخيص الاتصال مع systemEngine
        function diagnoseSystemEngine() {
            console.log('🔍 تشخيص systemEngine:');
            console.log('systemEngine موجود:', !!systemEngine);
            
            if (systemEngine) {
                console.log('systemEngine type:', typeof systemEngine);
                console.log('Available functions:', Object.getOwnPropertyNames(systemEngine).filter(name => typeof systemEngine[name] === 'function'));

                // Special test for sections management function
                console.log('openSectionsManagementWindow available:', typeof systemEngine.openSectionsManagementWindow === 'function');

                // Test direct access
                if (systemEngine.openSectionsManagementWindow) {
                    console.log('Direct access to openSectionsManagementWindow: OK');
                } else {
                    console.log('Direct access to openSectionsManagementWindow: FAILED');
                }
            }
        }

        // Helper function to check and call engine functions
        function callEngineFunction(functionName, ...args) {
            if (!systemEngine) {
                console.error('System engine not ready');
                alert('System not ready');
                return false;
            }

            try {
                // Check if function exists
                if (typeof systemEngine[functionName] === 'function') {
                    console.log('Calling function:', functionName);
                    systemEngine[functionName](...args);
                    return true;
                } else {
                    console.error('Function not found:', functionName);
                    console.log('Available functions:', Object.getOwnPropertyNames(systemEngine));
                    alert('Function not available: ' + functionName);
                    return false;
                }
            } catch (error) {
                console.error('Error calling function:', functionName, error);
                alert('Error calling function: ' + functionName + ' - ' + error.message);
                return false;
            }
        }

        // فتح نافذة الاستيراد
        function openImportWindow() {
            callEngineFunction('openImportWindow');
        }

        // فتح نافذة الإعدادات
        function openSettingsWindow() {
            callEngineFunction('openSettingsWindow');
        }

        // فتح نافذة بيانات المؤسسة
        function openInstitutionWindow() {
            callEngineFunction('openInstitutionWindow');
        }

        // فتح نافذة الإحصائيات
        function openStatisticsWindow() {
            callEngineFunction('openStatisticsWindow');
        }

        // فتح نافذة إعدادات الطابعات
        function openPrinterSettingsWindow() {
            callEngineFunction('openPrinterSettingsWindow');
        }

        // فتح نافذة تعديل المسميات
        function openDataEditWindow() {
            callEngineFunction('openDataEditWindow');
        }

        // فتح نافذة البنية التربوية
        function openSectionsManagementWindow() {
            console.log('Attempting to open sections management window...');

            if (!systemEngine) {
                console.error('systemEngine is not available');
                alert('System not ready');
                return;
            }

            console.log('systemEngine type:', typeof systemEngine);
            console.log('openSectionsManagementWindow type:', typeof systemEngine.openSectionsManagementWindow);

            if (typeof systemEngine.openSectionsManagementWindow === 'function') {
                try {
                    console.log('Calling openSectionsManagementWindow...');
                    systemEngine.openSectionsManagementWindow();
                    console.log('Successfully called openSectionsManagementWindow');
                } catch (error) {
                    console.error('Error calling openSectionsManagementWindow:', error);
                    alert('Error opening sections window: ' + error.message);
                }
            } else {
                console.error('openSectionsManagementWindow function not available');
                console.log('Available functions:', Object.getOwnPropertyNames(systemEngine).filter(name => typeof systemEngine[name] === 'function'));
                alert('Function not available');
            }
        }

        // عرض معلومات حول النظام
        function showAbout() {
            if (systemEngine) {
                systemEngine.showAbout();
            } else {
                alert('النظام التعليمي الشامل - النوافذ المنفصلة v3.0');
            }
        }

        // تحديث الإحصائيات كل 30 ثانية
        setInterval(function() {
            if (isChannelReady) {
                loadDatabaseStatistics();
            }
        }, 30000);

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeChannel();
        });
    </script>
</body>
</html>"""

def main():
    """تشغيل النظام الرئيسي"""
    app = QApplication(sys.argv)

    # إعداد التطبيق
    app.setApplicationName("النظام التعليمي الشامل - النوافذ المنفصلة")
    app.setApplicationVersion("3.0")
    app.setOrganizationName("Modern Education Systems")

    # إنشاء النافذة الرئيسية
    window = MainSystemWindow()
    window.show()

    # تشغيل التطبيق
    sys.exit(app.exec_())

if __name__ == '__main__':
    print("🌐 بدء تشغيل النظام التعليمي الشامل - النوافذ المنفصلة...")
    print("=" * 70)
    print("📋 الميزات الجديدة:")
    print("   🔹 نافذة رئيسية بسيطة وأنيقة")
    print("   🔹 فتح النوافذ المنفصلة حسب الحاجة")
    print("   🔹 أداء ممتاز وذاكرة أقل")
    print("   🔹 سهولة الصيانة والتطوير")
    print("   🔹 واجهة HTML جميلة ومتجاوبة")
    print("=" * 70)
    print("🎯 الوحدات المتاحة:")
    print("   📥 نافذة الاستيراد")
    print("   ⚙️ نافذة الإعدادات")
    print("   🏢 نافذة بيانات المؤسسة")
    print("   📊 نافذة الإحصائيات العامة")
    print("   🖨️ نافذة إعدادات الطابعات")
    print("   📝 نافذة تعديل المسميات")
    print("   🏫 نافذة البنية التربوية - إدارة الأقسام")
    print("=" * 70)
    print("🚀 جاري تشغيل النظام...")

    try:
        main()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("💡 تأكد من تثبيت PyQt5:")
        print("   pip install PyQt5 PyQtWebEngine")
