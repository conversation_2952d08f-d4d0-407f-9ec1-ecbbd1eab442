"""
محرك استيراد البيانات المصحح - نسخة محسنة
يحتوي على جميع دوال الاستيراد والمعالجة مع الطريقة الصحيحة
"""

import os
import json
import sqlite3
from pathlib import Path
from PyQt5.QtWidgets import QFileDialog
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal, QDateTime

# محاولة استيراد pandas مع معالجة الخطأ
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

class ImportEngine(QObject):
    """محرك استيراد البيانات - مسؤول عن كل المعالجة والتفاعل مع قاعدة البيانات"""
    
    # إشارات للتواصل مع واجهة HTML
    logUpdated = pyqtSignal(str, str, str)  # message, status, timestamp
    importProgressUpdated = pyqtSignal(int, str)  # progress percentage, status text
    statisticsUpdated = pyqtSignal(str)  # statistics JSON
    
    def __init__(self, parent_window=None):
        super().__init__()
        self.db_path = "data.db"
        self.parent_window = parent_window
        self.pandas_available = PANDAS_AVAILABLE
    
    def emit_log(self, message, status="info"):
        """إرسال رسالة إلى واجهة HTML"""
        timestamp = QDateTime.currentDateTime().toString("hh:mm:ss")
        self.logUpdated.emit(message, status, timestamp)
    
    @pyqtSlot(result=bool)
    def checkPandasAvailability(self):
        """التحقق من توفر pandas"""
        return self.pandas_available
    
    @pyqtSlot(result=str)
    def getDatabaseStatistics(self):
        """الحصول على إحصائيات قاعدة البيانات - الطريقة الصحيحة من sub1_window.py"""
        try:
            if not os.path.exists(self.db_path):
                return json.dumps({
                    "current_year": "غير محدد",
                    "sections_count": 0,
                    "students_count": 0,
                    "levels_count": 0,
                    "teachers_count": 0,
                    "secret_codes_count": 0,
                    "sections": []
                }, ensure_ascii=False)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # جمع الإحصائيات
            stats = {}
            
            # السنة الدراسية الحالية
            try:
                cursor.execute("SELECT DISTINCT السنة_الدراسية FROM اللوائح ORDER BY السنة_الدراسية DESC LIMIT 1")
                result = cursor.fetchone()
                stats['current_year'] = result[0] if result and result[0] else "غير محدد"
            except:
                stats['current_year'] = "غير محدد"
            
            # إحصائيات الأقسام والتلاميذ
            try:
                if stats['current_year'] != "غير محدد":
                    cursor.execute("""
                        SELECT 
                            COUNT(DISTINCT القسم) as sections_count,
                            COUNT(*) as students_count,
                            COUNT(DISTINCT المستوى) as levels_count
                        FROM اللوائح 
                        WHERE السنة_الدراسية = ?
                    """, (stats['current_year'],))
                    
                    db_stats = cursor.fetchone()
                    if db_stats:
                        stats['sections_count'] = db_stats[0]
                        stats['students_count'] = db_stats[1] 
                        stats['levels_count'] = db_stats[2]
                    else:
                        stats['sections_count'] = 0
                        stats['students_count'] = 0
                        stats['levels_count'] = 0
                else:
                    # إذا لم تكن هناك سنة دراسية، احصل على إجمالي البيانات
                    cursor.execute("""
                        SELECT 
                            COUNT(DISTINCT القسم) as sections_count,
                            COUNT(*) as students_count,
                            COUNT(DISTINCT المستوى) as levels_count
                        FROM اللوائح
                    """)
                    
                    db_stats = cursor.fetchone()
                    if db_stats:
                        stats['sections_count'] = db_stats[0]
                        stats['students_count'] = db_stats[1] 
                        stats['levels_count'] = db_stats[2]
                    else:
                        stats['sections_count'] = 0
                        stats['students_count'] = 0
                        stats['levels_count'] = 0
            except:
                stats['sections_count'] = 0
                stats['students_count'] = 0
                stats['levels_count'] = 0
            
            # قائمة الأقسام
            try:
                if stats['current_year'] != "غير محدد":
                    cursor.execute("SELECT DISTINCT القسم FROM اللوائح WHERE السنة_الدراسية = ? ORDER BY القسم", (stats['current_year'],))
                else:
                    cursor.execute("SELECT DISTINCT القسم FROM اللوائح ORDER BY القسم")
                sections = [row[0] for row in cursor.fetchall()]
                stats['sections'] = sections
            except:
                stats['sections'] = []
            
            # إحصائيات الأساتذة
            try:
                cursor.execute("SELECT COUNT(*) FROM الأساتذة")
                result = cursor.fetchone()
                stats['teachers_count'] = result[0] if result else 0
            except:
                stats['teachers_count'] = 0
            
            # إحصائيات الرموز السرية
            try:
                cursor.execute("SELECT COUNT(*) FROM الرمز_السري")
                result = cursor.fetchone()
                stats['secret_codes_count'] = result[0] if result else 0
            except:
                stats['secret_codes_count'] = 0
            
            conn.close()
            return json.dumps(stats, ensure_ascii=False)
            
        except Exception as e:
            self.emit_log(f"خطأ في جمع الإحصائيات: {str(e)}", "error")
            return json.dumps({
                "error": str(e),
                "current_year": "غير محدد",
                "sections_count": 0,
                "students_count": 0,
                "levels_count": 0,
                "teachers_count": 0,
                "secret_codes_count": 0,
                "sections": []
            }, ensure_ascii=False)
    
    @pyqtSlot()
    def selectMasarFile(self):
        """اختيار ملف لوائح منظومة مسار"""
        if not self.pandas_available:
            self.emit_log("خطأ: مكتبة pandas غير متوفرة", "error")
            self.emit_log("لتثبيت pandas، استخدم الأمر: pip install pandas", "info")
            return
        
        file_path, _ = QFileDialog.getOpenFileName(
            self.parent_window, "اختر ملف الإكسل", "", "Excel Files (*.xlsx *.xls)"
        )
        
        if file_path:
            self.importMasarData(file_path)

    def importMasarData(self, file_path):
        """استيراد بيانات منظومة مسار - الطريقة الصحيحة من sub1_window.py"""
        self.emit_log("بدء استيراد بيانات منظومة مسار...", "progress")
        self.importProgressUpdated.emit(0, "جاري تحضير الملف...")
        
        conn = None
        try:
            # التحقق من اسم الملف
            file_name = os.path.basename(file_path)
            if "ListEleve" not in file_name:
                self.emit_log(f"تحذير: الملف {file_name} قد لا يكون ملف لوائح منظومة مسار", "warning")
            
            # إنشاء اتصال بقاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # حذف السجلات الافتراضية
            self.importProgressUpdated.emit(10, "جاري حذف السجلات الافتراضية...")
            cursor.execute("DELETE FROM السجل_العام WHERE الرمز = 'A12345678'")
            cursor.execute("DELETE FROM اللوائح WHERE الرمز = 'A12345678'")
            cursor.execute("DROP TABLE IF EXISTS 'السجل الاولي'")
            
            # قراءة ملف Excel
            self.importProgressUpdated.emit(20, "جاري قراءة ملف Excel...")
            sheets_dict = pd.read_excel(file_path, sheet_name=None)
            
            # استخراج السنة الدراسية
            current_academic_year = None
            for sheet_name, df in sheets_dict.items():
                if "Unnamed: 6" in df.columns and len(df) > 5:
                    current_academic_year = df.iloc[5]["Unnamed: 6"]
                    break
            
            # تحضير البيانات
            self.importProgressUpdated.emit(30, "جاري تحضير البيانات...")
            total_sheets = len(sheets_dict)
            
            for i, (sheet_name, df) in enumerate(sheets_dict.items()):
                progress = 30 + int((i / total_sheets) * 20)
                self.importProgressUpdated.emit(progress, f"جاري معالجة القسم {sheet_name}...")
                
                level_rows = df[df["Unnamed: 0"].astype(str).str.strip() == ": المستوى"]
                level_value = level_rows.iloc[0]["Unnamed: 2"] if not level_rows.empty else None
                year_value = df.iloc[5]["Unnamed: 6"] if "Unnamed: 6" in df.columns and len(df) > 5 else None
                
                df["القسم"] = sheet_name
                df["المستوى"] = level_value
                df["السنة الدراسية"] = year_value
            
            # دمج وحفظ البيانات
            self.importProgressUpdated.emit(50, "جاري دمج وحفظ البيانات...")
            combined_df = pd.concat(sheets_dict.values(), ignore_index=True)
            combined_df.to_sql("السجل الاولي", conn, if_exists='append', index=False)
            
            # معالجة البيانات
            self.importProgressUpdated.emit(60, "جاري معالجة البيانات...")
            self.process_data_silently(cursor, current_academic_year)
            
            # حفظ التغييرات
            self.importProgressUpdated.emit(90, "جاري حفظ التغييرات...")
            conn.commit()
            conn.close()
            conn = None
            
            # عرض النتائج
            self.importProgressUpdated.emit(100, "تم الانتهاء بنجاح!")
            self.emit_log(f"✅ تم استيراد بيانات منظومة مسار بنجاح للسنة الدراسية {current_academic_year}", "success")
            
            # تحديث الإحصائيات
            self.statisticsUpdated.emit(self.getDatabaseStatistics())
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في استيراد البيانات: {str(e)}", "error")
            if conn:
                conn.close()
    
    def process_data_silently(self, cursor, academic_year):
        """معالجة البيانات بهدوء - الطريقة الصحيحة من sub1_window.py"""
        try:
            # تحديث بيانات المؤسسة
            self.update_school_info(cursor)
            
            # إنشاء وتحديث اللوائح
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS 'اللوائح' (
                    'السنة_الدراسية' TEXT, 
                    'القسم' TEXT, 
                    'المستوى' TEXT, 
                    'الرمز' TEXT, 
                    'رت' TEXT, 
                    'مجموع التلاميذ' INTEGER DEFAULT 0, 
                    PRIMARY KEY('السنة_الدراسية', 'الرمز')
                )
            """)
            
            # حذف البيانات القديمة للسنة الدراسية
            if academic_year:
                cursor.execute("DELETE FROM اللوائح WHERE السنة_الدراسية = ?", (academic_year,))
            
            # إضافة البيانات الجديدة
            cursor.execute("""
                INSERT OR IGNORE INTO "اللوائح" ("السنة_الدراسية", "القسم", "المستوى", "الرمز", "رت")
                SELECT "السنة الدراسية", "القسم", "المستوى", "Unnamed: 1", "Unnamed: 0"
                FROM "السجل الاولي"
            """)
            
            # تنظيف البيانات
            cursor.execute("""
                DELETE FROM "اللوائح" 
                WHERE "الرمز" IS NULL OR TRIM("الرمز") = '' OR "الرمز" = 'الرمز'
            """)
            
            # تحديث مجموع التلاميذ
            if academic_year:
                cursor.execute("""
                    UPDATE "اللوائح" as l1
                    SET "مجموع التلاميذ" = (
                        SELECT COUNT(*)
                        FROM "اللوائح" AS l2
                        WHERE l2."القسم" = l1."القسم"
                        AND l2."السنة_الدراسية" = l1."السنة_الدراسية"
                    )
                    WHERE l1."السنة_الدراسية" = ?
                """, (academic_year,))
            
            # تحديث السجل العام والبنية التربوية
            self.update_structure_tables(cursor, academic_year)
            
        except Exception as e:
            raise Exception(f"حدث خطأ أثناء معالجة البيانات: {str(e)}")
    
    def update_school_info(self, cursor):
        """تحديث بيانات المؤسسة - من sub1_window.py"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS بيانات_المؤسسة (
                الأكاديمية TEXT,
                المديرية TEXT,
                الجماعة TEXT,
                المؤسسة TEXT,
                السنة_الدراسية TEXT,
                البلدة TEXT,
                المدير TEXT,
                الحارس_العام TEXT,
                السلك TEXT,
                رقم_الحراسة TEXT,
                رقم_التسجيل TEXT,
                الأسدس TEXT,
                ImagePath1 TEXT
            )
        """)
        
        # استخراج بيانات المؤسسة من السجل الأولي
        cursor.execute("SELECT * FROM 'السجل الاولي' LIMIT 6")
        rows = cursor.fetchall()
        
        if len(rows) >= 6:
            # إضافة أو تحديث بيانات المؤسسة
            cursor.execute("DELETE FROM بيانات_المؤسسة")
            cursor.execute("""
                INSERT INTO بيانات_المؤسسة 
                (الأكاديمية, المديرية, الجماعة, المؤسسة, السنة_الدراسية, الأسدس)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                rows[3][2] if len(rows) > 3 else '',  # الأكاديمية
                rows[4][2] if len(rows) > 4 else '',  # المديرية  
                rows[3][6] if len(rows) > 3 else '',  # الجماعة
                rows[4][6] if len(rows) > 4 else '',  # المؤسسة
                rows[5][6] if len(rows) > 5 else '',  # السنة الدراسية
                'الأول'  # الأسدس
            ))
    
    def update_structure_tables(self, cursor, academic_year):
        """تحديث جداول البنية التربوية والسجل العام - من sub1_window.py"""
        # تحديث السجل العام
        cursor.execute("""
            INSERT OR IGNORE INTO "السجل_العام"
                ("الرمز", "الاسم_والنسب", "النوع", "تاريخ_الازدياد", "مكان_الازدياد")
            SELECT "Unnamed: 1",
                   "Unnamed: 2" || ' ' || "Unnamed: 3",
                   "Unnamed: 4",
                   "Unnamed: 5", 
                   "Unnamed: 6"
            FROM "السجل الاولي"
            WHERE "Unnamed: 1" IS NOT NULL
              AND TRIM("Unnamed: 1") <> ''
              AND "Unnamed: 1" <> 'الرمز'
        """)
        
        # تحديث البنية التربوية
        cursor.execute("""
            INSERT OR IGNORE INTO "البنية_التربوية" ("السنة_الدراسية", "القسم", "المستوى")
            SELECT DISTINCT "السنة_الدراسية", "القسم", "المستوى"
            FROM "اللوائح"
        """)
        
        # إضافة عمود مجموع التلاميذ إذا لم يكن موجوداً
        try:
            cursor.execute('ALTER TABLE "البنية_التربوية" ADD COLUMN "مجموع_التلاميذ" INTEGER DEFAULT 0')
        except sqlite3.OperationalError:
            pass
        
        # تحديث مجموع التلاميذ في البنية التربوية
        if academic_year:
            cursor.execute("""
                UPDATE "البنية_التربوية"
                SET "مجموع_التلاميذ" = (
                    SELECT COUNT(*)
                    FROM "اللوائح" AS l
                    WHERE l."السنة_الدراسية" = "البنية_التربوية"."السنة_الدراسية"
                      AND l."القسم" = "البنية_التربوية"."القسم"
                      AND l."المستوى" = "البنية_التربوية"."المستوى"
                )
                WHERE "السنة_الدراسية" = ?
            """, (academic_year,))

    @pyqtSlot()
    def selectTeachersFile(self):
        """اختيار ملف أسماء الأساتذة"""
        if not self.pandas_available:
            self.emit_log("خطأ: مكتبة pandas غير متوفرة", "error")
            return

        file_path, _ = QFileDialog.getOpenFileName(
            self.parent_window, "اختر ملف الأساتذة", "", "Excel Files (*.xlsx *.xls)"
        )

        if file_path:
            self.importTeachersData(file_path)

    def importTeachersData(self, file_path):
        """استيراد أسماء الأساتذة والمواد - الطريقة الصحيحة من sub1_window.py"""
        self.emit_log("بدء استيراد أسماء الأساتذة...", "progress")
        self.importProgressUpdated.emit(0, "جاري تحضير البيانات...")

        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إنشاء جدول الأساتذة
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS الأساتذة (
                    رقم_الأستاذ INTEGER PRIMARY KEY AUTOINCREMENT,
                    اسم_الأستاذ TEXT,
                    المادة TEXT,
                    الرمز TEXT
                )
            ''')

            # حذف البيانات السابقة
            self.importProgressUpdated.emit(20, "جاري حذف البيانات السابقة...")
            cursor.execute("DELETE FROM الأساتذة")

            # قراءة ملف Excel
            self.importProgressUpdated.emit(40, "جاري قراءة ملف Excel...")
            df = pd.read_excel(file_path, header=None)

            if df.empty:
                self.emit_log("الملف فارغ", "warning")
                return

            # معالجة البيانات
            self.importProgressUpdated.emit(60, "جاري معالجة البيانات...")
            df_selected = df[[1, 2, 3]].copy()  # B, C, D columns
            df_selected.columns = ['المادة', 'الرمز', 'اسم_الأستاذ']

            # ملء الفراغات في عمود المادة
            previous_subject = None
            for index, row in df_selected.iterrows():
                current_subject = row['المادة']
                if pd.isna(current_subject) or (isinstance(current_subject, str) and current_subject.strip() == ''):
                    if previous_subject is not None:
                        df_selected.at[index, 'المادة'] = previous_subject
                else:
                    previous_subject = current_subject

            # تنظيف البيانات
            df_selected = df_selected.dropna(subset=['اسم_الأستاذ'])
            df_selected = df_selected[~df_selected['اسم_الأستاذ'].str.contains('المجموع الاجمالي', na=False)]

            # إدراج البيانات
            self.importProgressUpdated.emit(80, "جاري إدراج البيانات...")
            records_inserted = 0

            for _, row in df_selected.iterrows():
                if row['اسم_الأستاذ'].strip() and row['المادة'].strip():
                    cursor.execute(
                        "INSERT INTO الأساتذة (اسم_الأستاذ, المادة, الرمز) VALUES (?, ?, ?)",
                        (row['اسم_الأستاذ'], row['المادة'], row['الرمز'])
                    )
                    records_inserted += 1

            conn.commit()
            conn.close()
            conn = None

            self.importProgressUpdated.emit(100, "تم الانتهاء!")
            self.emit_log(f"✅ تم استيراد {records_inserted} أستاذ بنجاح", "success")
            self.statisticsUpdated.emit(self.getDatabaseStatistics())

        except Exception as e:
            self.emit_log(f"❌ خطأ في استيراد الأساتذة: {str(e)}", "error")
            if conn:
                conn.close()

    @pyqtSlot()
    def selectSecretCodesFiles(self):
        """اختيار ملفات الرموز السرية"""
        if not self.pandas_available:
            self.emit_log("خطأ: مكتبة pandas غير متوفرة", "error")
            return

        file_paths, _ = QFileDialog.getOpenFileNames(
            self.parent_window, "اختر ملفات الرموز السرية", "", "Excel Files (*.xlsx *.xls)"
        )

        if file_paths:
            self.importSecretCodes(file_paths)

    def importSecretCodes(self, file_paths):
        """استيراد الرموز السرية من ملفات متعددة - الطريقة الصحيحة من sub1_window.py"""
        self.emit_log(f"بدء استيراد الرموز السرية من {len(file_paths)} ملف...", "progress")
        self.importProgressUpdated.emit(0, "جاري تحضير قاعدة البيانات...")

        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إنشاء جدول الرمز السري
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS الرمز_السري (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    الرمز TEXT UNIQUE,
                    الرمز_السري TEXT
                )
            ''')

            # حذف البيانات السابقة
            cursor.execute("DELETE FROM الرمز_السري")

            total_records_imported = 0

            # معالجة كل ملف
            for file_index, file_path in enumerate(file_paths):
                file_name = os.path.basename(file_path)
                progress = int((file_index / len(file_paths)) * 80)
                self.importProgressUpdated.emit(progress, f"جاري معالجة الملف {file_index + 1}/{len(file_paths)}: {file_name}")

                try:
                    df = pd.read_excel(file_path)
                    if df.empty:
                        continue

                    # تحديد الأعمدة المطلوبة (افتراض أول عمودين)
                    if df.shape[1] >= 2:
                        records_data = []
                        for _, row in df.iterrows():
                            code = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else ""
                            secret = str(row.iloc[1]).strip() if pd.notna(row.iloc[1]) else ""

                            if code and secret and len(code) > 2 and len(secret) > 2:
                                records_data.append((code, secret))

                        # إدراج البيانات
                        for code, secret in records_data:
                            try:
                                cursor.execute("INSERT OR REPLACE INTO الرمز_السري (الرمز, الرمز_السري) VALUES (?, ?)", (code, secret))
                                total_records_imported += 1
                            except Exception as e:
                                self.emit_log(f"خطأ في إدراج الرمز {code}: {str(e)}", "warning")

                except Exception as e:
                    self.emit_log(f"خطأ في معالجة الملف {file_name}: {str(e)}", "error")

            conn.commit()
            conn.close()
            conn = None

            self.importProgressUpdated.emit(100, "تم الانتهاء!")
            self.emit_log(f"✅ تم استيراد {total_records_imported} رمز سري بنجاح", "success")
            self.statisticsUpdated.emit(self.getDatabaseStatistics())

        except Exception as e:
            self.emit_log(f"❌ خطأ في استيراد الرموز السرية: {str(e)}", "error")
            if conn:
                conn.close()

    @pyqtSlot()
    def importArrivalsData(self):
        """استيراد بيانات الوافدين والمغادرين"""
        if not self.pandas_available:
            self.emit_log("خطأ: مكتبة pandas غير متوفرة", "error")
            return

        file_path, _ = QFileDialog.getOpenFileName(
            self.parent_window, "اختر ملف الوافدين والمغادرين", "", "Excel Files (*.xlsx *.xls)"
        )

        if file_path:
            self.processArrivalsData(file_path)

    def processArrivalsData(self, file_path):
        """معالجة بيانات الوافدين والمغادرين"""
        self.emit_log("بدء استيراد بيانات الوافدين والمغادرين...", "progress")
        self.importProgressUpdated.emit(0, "جاري قراءة الملف...")

        try:
            # قراءة الملف
            df = pd.read_excel(file_path)

            if df.empty:
                self.emit_log("الملف فارغ", "warning")
                return

            self.importProgressUpdated.emit(50, "جاري معالجة البيانات...")

            # معالجة البيانات حسب الحاجة
            # يمكن تخصيص هذا الجزء حسب تنسيق ملف الوافدين والمغادرين

            self.importProgressUpdated.emit(100, "تم الانتهاء!")
            self.emit_log(f"✅ تم استيراد بيانات الوافدين والمغادرين بنجاح", "success")

        except Exception as e:
            self.emit_log(f"❌ خطأ في استيراد بيانات الوافدين والمغادرين: {str(e)}", "error")

    @pyqtSlot()
    def refreshData(self):
        """تحديث البيانات والإحصائيات"""
        self.emit_log("🔄 جاري تحديث البيانات...", "progress")
        try:
            # تحديث الإحصائيات
            self.statisticsUpdated.emit(self.getDatabaseStatistics())
            self.emit_log("✅ تم تحديث البيانات بنجاح", "success")
        except Exception as e:
            self.emit_log(f"❌ خطأ في تحديث البيانات: {str(e)}", "error")

    @pyqtSlot()
    def showHelpGuide(self):
        """عرض دليل المساعدة"""
        help_text = """
📖 دليل استخدام نظام الاستيراد:

📚 استيراد اللوائح من منظومة مسار:
- اختر ملف Excel يحتوي على بيانات الطلاب
- تأكد أن الملف بصيغة .xlsx أو .xls
- يجب أن يحتوي على الأعمدة المطلوبة

🔐 استيراد الرموز السرية:
- يمكن اختيار ملفات متعددة
- كل ملف يجب أن يحتوي على عمودين: الرمز والرمز السري

👨‍🏫 استيراد الأساتذة:
- ملف Excel يحتوي على: المادة، الرمز، اسم الأستاذ
- يتم تجميع البيانات حسب المادة تلقائياً

🔄 الوافدين والمغادرين:
- ملف Excel لتحديث بيانات الطلاب الجدد أو المنقولين
        """
        self.emit_log(help_text, "info")
