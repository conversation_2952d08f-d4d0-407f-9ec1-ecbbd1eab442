#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف اختبار الإصلاحات الجديدة على sub4_window.py
اختبار:
1. مربع الحراسة يعمل
2. عرض الأزرار حسب النص
3. مربعات الاختيار بحجم مناسب للصفوف
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

def test_new_fixes():
    """اختبار الإصلاحات الجديدة"""
    print("=" * 60)
    print("🔧 اختبار الإصلاحات الجديدة على sub4_window.py")
    print("=" * 60)
    
    # إنشاء تطبيق Qt
    app = QApplication(sys.argv)
    app.setApplicationName("اختبار الإصلاحات الجديدة")
    
    # تعيين أيقونة التطبيق إذا كانت متوفرة
    if os.path.exists("01.ico"):
        app.setWindowIcon(QIcon("01.ico"))
    
    try:
        # استيراد النافذة المحسنة
        from sub4_window import Sub4Window
        
        print("✅ تم استيراد sub4_window بنجاح")
        
        # محاولة إنشاء اتصال بقاعدة البيانات
        from PyQt5.QtSql import QSqlDatabase
        
        db = QSqlDatabase.addDatabase('QSQLITE')
        db.setDatabaseName('data.db')
        
        if not db.open():
            print("⚠️  تحذير: لم يتم العثور على قاعدة البيانات")
            print("💡 سيتم تشغيل النافذة مع السنة الدراسية الافتراضية")
        else:
            print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # إنشاء النافذة مع الإصلاحات الجديدة
        print("\n🚀 إنشاء النافذة مع الإصلاحات الجديدة...")
        window = Sub4Window(db=db, academic_year="2024-2025")
        
        print("\n🔧 الإصلاحات الجديدة المطبقة:")
        print("   ✅ إصلاح مربع الحراسة - يحتوي الآن على البيانات")
        print("   ✅ تحسين عرض الأزرار - العرض حسب النص مع padding مناسب")
        print("   ✅ تحسين مربعات الاختيار - حجم مناسب للصفوف (22 بكسل كحد أقصى)")
        print("   ✅ توحيد ارتفاع الصفوف - 30 بكسل مناسب للمحتوى")
        print("   ✅ تحسين حجم خط علامة الصح - يتناسب مع حجم المربع")
        
        # عرض النافذة
        window.show()
        window.setWindowTitle("نافذة البحث - تم إصلاح المشاكل الجديدة")
        
        print("\n🌟 تم تشغيل النافذة بنجاح!")
        print("💡 اختبر الميزات التالية:")
        print("   • مربع الحراسة - يجب أن يحتوي على 'حراسة رقم 1' إلى 'حراسة رقم 5'")
        print("   • الأزرار - لاحظ أن عرض كل زر يناسب النص الموجود فيه")
        print("   • مربعات الاختيار - يجب أن تكون بحجم مناسب داخل الصفوف")
        print("   • ارتفاع الصفوف - 30 بكسل مناسب للمحتوى")
        
        # تشغيل التطبيق
        return app.exec_()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد sub4_window: {e}")
        QMessageBox.critical(None, "خطأ", f"فشل في استيراد النافذة:\n{e}")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        QMessageBox.critical(None, "خطأ", f"حدث خطأ غير متوقع:\n{e}")
        return 1

def show_fixes_details():
    """عرض تفاصيل الإصلاحات الجديدة"""
    print("\n📋 تفاصيل الإصلاحات الجديدة:")
    print("-" * 40)
    
    fixes = [
        ("🎯 إصلاح مربع الحراسة", [
            "إضافة البيانات المفقودة: حراسة رقم 1-5",
            "إزالة التعليق الذي كان يمنع تحميل البيانات",
            "التأكد من عمل القائمة المنسدلة بشكل صحيح",
            "ربط القائمة بدالة تصفية المستويات"
        ]),
        ("📏 تحسين عرض الأزرار", [
            "حساب العرض بناءً على طول النص الفعلي",
            "إضافة padding متغير حسب طول النص",
            "تقليل المرونة الإضافية من 20 إلى 10 بكسل",
            "ضمان عرض مناسب لجميع النصوص"
        ]),
        ("☑️ تحسين مربعات الاختيار", [
            "حساب حجم المربع بناءً على ارتفاع الصف",
            "حد أقصى 22 بكسل مع هامش 3 بكسل من كل جهة",
            "تحسين حجم خط علامة الصح ليناسب المربع",
            "ضمان عدم تجاوز المربع لحدود الصف"
        ]),
        ("📐 توحيد ارتفاع الصفوف", [
            "توحيد ارتفاع الصفوف إلى 30 بكسل",
            "إزالة التضارب بين 28 و 35 بكسل",
            "ارتفاع مناسب لمربعات الاختيار والنصوص",
            "تحسين المظهر العام للجداول"
        ]),
        ("🎨 تحسينات بصرية إضافية", [
            "تحسين نسبة حجم الخط لعلامة الصح",
            "ضمان وضوح المربعات في جميع الأحجام",
            "الحفاظ على الألوان الذهبية المميزة",
            "تحسين التناسق البصري العام"
        ])
    ]
    
    for title, items in fixes:
        print(f"\n{title}:")
        for item in items:
            print(f"   • {item}")

def show_testing_checklist():
    """قائمة فحص للاختبار"""
    print("\n" + "=" * 60)
    print("📝 قائمة فحص الاختبار")
    print("=" * 60)
    
    checklist = [
        ("1️⃣ اختبار مربع الحراسة", [
            "☐ التحقق من وجود 5 خيارات للحراسة",
            "☐ اختبار تغيير الحراسة وتأثيرها على المستويات",
            "☐ التأكد من عمل القائمة المنسدلة بسلاسة",
            "☐ فحص الاستجابة عند اختيار حراسة مختلفة"
        ]),
        ("2️⃣ اختبار عرض الأزرار", [
            "☐ فحص عرض زر 'ورقة الدخول' (نص قصير)",
            "☐ فحص عرض زر 'تعديل التاريخ والوقت' (نص طويل)",
            "☐ التأكد من عدم قطع النص في أي زر",
            "☐ فحص التناسق البصري بين الأزرار"
        ]),
        ("3️⃣ اختبار مربعات الاختيار", [
            "☐ التحقق من حجم المربعات داخل الصفوف",
            "☐ فحص وضوح علامة الصح",
            "☐ اختبار النقر على المربعات",
            "☐ التأكد من عدم تجاوز المربعات لحدود الصفوف"
        ]),
        ("4️⃣ اختبار الجداول", [
            "☐ فحص ارتفاع الصفوف (30 بكسل)",
            "☐ التحقق من وضوح النصوص",
            "☐ اختبار التمرير في الجداول",
            "☐ فحص التناسق بين جميع الجداول"
        ])
    ]
    
    for title, items in checklist:
        print(f"\n{title}:")
        for item in items:
            print(f"   {item}")

if __name__ == "__main__":
    print("🔧 مرحباً بك في اختبار الإصلاحات الجديدة!")
    
    # عرض تفاصيل الإصلاحات
    show_fixes_details()
    
    # عرض قائمة فحص الاختبار
    show_testing_checklist()
    
    print("\n" + "=" * 60)
    print("🚀 بدء تشغيل النافذة مع الإصلاحات الجديدة...")
    print("=" * 60)
    
    # تشغيل الاختبار
    exit_code = test_new_fixes()
    
    print(f"\n🔚 انتهى الاختبار بكود الخروج: {exit_code}")
    sys.exit(exit_code)
