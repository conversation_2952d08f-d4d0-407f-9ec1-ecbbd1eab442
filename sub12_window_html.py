#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة مسك المخالفات - النسخة الحديثة باستخدام Python + HTML
تستخدم QWebEngineView لعرض واجهة تفاعلية وأنيقة
"""

import sys
import os
import sqlite3
import json
from datetime import datetime
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtSql import *
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

# تصدير الكلاسات المتاحة للاستيراد
__all__ = ['StudentViolationsWindow']

class StudentViolationsWindow(QMainWindow):
    """نافذة مسك المخالفات - تستخدم منهجية Python + HTML الحديثة"""
    
    def __init__(self, student_code=None, student_name=None, db=None, parent=None):
        super().__init__(parent)
        
        # المتغيرات الأساسية
        self.student_code = student_code
        self.student_name = student_name or "غير محدد"
        self.db = db
        self.parent_window = parent
        
        # إعداد النافذة
        self.setupUI()
        
        # تحميل البيانات الأولية
        self.load_initial_data()
        
        # تحميل بيانات التلميذ إذا توفر
        if self.student_code:
            self.load_student_data()
    
    def setupUI(self):
        """إعداد واجهة المستخدم"""
        # إعدادات النافذة الأساسية
        self.setWindowTitle(f"⚠️ مسك المخالفات - {self.student_name}")
        self.setGeometry(100, 100, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إضافة أيقونة للنافذة
        try:
            self.setWindowIcon(QIcon("01.ico"))
        except:
            pass
        
        # تطبيق نمط احترافي للنافذة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #fff3e0,
                    stop: 1 #ffcc80
                );
            }
        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # العنوان الرئيسي
        title_label = QLabel(f"⚠️ مسك المخالفات: {self.student_name}")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setMaximumHeight(60)
        title_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #ff5722,
                    stop: 0.5 #f44336,
                    stop: 1 #ff5722
                );
                color: white;
                padding: 15px;
                border-radius: 12px;
                font-weight: bold;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # إنشاء منطقة عرض المحتوى HTML
        self.web_view = QWebEngineView()
        main_layout.addWidget(self.web_view)
        
        # إضافة أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        buttons_layout.setContentsMargins(10, 5, 10, 10)
        
        # زر حفظ المخالفة
        self.save_button = QPushButton("💾 حفظ المخالفة")
        self.save_button.setFont(QFont("Calibri", 12, QFont.Bold))
        self.save_button.setMinimumHeight(40)
        self.save_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #4CAF50,
                    stop: 1 #45a049
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #5cbf60,
                    stop: 1 #4CAF50
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3d8b40,
                    stop: 1 #2e7d32
                );
            }
        """)
        self.save_button.clicked.connect(self.save_violation)
        
        # زر مسح البيانات
        self.clear_button = QPushButton("🧹 مسح البيانات")
        self.clear_button.setFont(QFont("Calibri", 12, QFont.Bold))
        self.clear_button.setMinimumHeight(40)
        self.clear_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #FF9800,
                    stop: 1 #f57c00
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #FFB74D,
                    stop: 1 #FF9800
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e65100,
                    stop: 1 #bf360c
                );
            }
        """)
        self.clear_button.clicked.connect(self.clear_form)
        
        # زر طباعة المخالفة
        self.print_button = QPushButton("🖨️ طباعة المخالفة")
        self.print_button.setFont(QFont("Calibri", 12, QFont.Bold))
        self.print_button.setMinimumHeight(40)
        self.print_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #2196F3,
                    stop: 1 #1976d2
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #42A5F5,
                    stop: 1 #2196F3
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #1565c0,
                    stop: 1 #0d47a1
                );
            }
        """)
        self.print_button.clicked.connect(self.print_violation)
        
        # زر إغلاق النافذة
        self.close_button = QPushButton("❌ إغلاق")
        self.close_button.setFont(QFont("Calibri", 12, QFont.Bold))
        self.close_button.setMinimumHeight(40)
        self.close_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #9E9E9E,
                    stop: 1 #757575
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #BDBDBD,
                    stop: 1 #9E9E9E
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #616161,
                    stop: 1 #424242
                );
            }
        """)
        self.close_button.clicked.connect(self.close)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.clear_button)
        buttons_layout.addWidget(self.print_button)
        buttons_layout.addWidget(self.close_button)
        
        main_layout.addLayout(buttons_layout)
        
        # إنشاء شريط الحالة
        self.status_bar = self.statusBar()
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #f5f5f5;
                border-top: 1px solid #ddd;
                padding: 5px;
                font-weight: bold;
            }
        """)
        self.status_bar.showMessage("جاهز...")
    
    def load_initial_data(self):
        """تحميل البيانات الأولية للنافذة"""
        try:
            # تحميل البيانات من قاعدة البيانات
            levels = self.get_levels()
            subjects = self.get_subjects()
            violations = self.get_violations()
            procedures = self.get_procedures()
            
            # إنشاء محتوى HTML وعرضه
            html_content = self.create_html_content(levels, subjects, violations, procedures)
            self.web_view.setHtml(html_content)
            
            self.status_bar.showMessage("تم تحميل البيانات الأولية بنجاح")
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات الأولية: {e}")
            self.status_bar.showMessage(f"خطأ: {str(e)}")
    
    def get_levels(self):
        """جلب المستويات من قاعدة البيانات"""
        try:
            if not self.db:
                return []
            
            query = QSqlQuery(self.db)
            query.exec_("SELECT DISTINCT المستوى FROM البنية_التربوية ORDER BY المستوى")
            
            levels = []
            while query.next():
                levels.append(query.value(0))
            
            return levels
        except Exception as e:
            print(f"خطأ في جلب المستويات: {e}")
            return []
    
    def get_subjects(self):
        """جلب المواد من قاعدة البيانات"""
        try:
            if not self.db:
                return []
            
            query = QSqlQuery(self.db)
            query.exec_("SELECT DISTINCT المادة FROM الأساتذة ORDER BY المادة")
            
            subjects = []
            while query.next():
                subjects.append(query.value(0))
            
            return subjects
        except Exception as e:
            print(f"خطأ في جلب المواد: {e}")
            return []
    
    def get_violations(self):
        """جلب المخالفات من قاعدة البيانات"""
        try:
            if not self.db:
                return [
                    "تأخر عن الطابور",
                    "عدم إحضار الكتب",
                    "عدم أداء الواجبات",
                    "سوء السلوك",
                    "عدم ارتداء الزي المدرسي",
                    "استخدام الهاتف المحمول",
                    "عدم احترام المعلم",
                    "الفوضى في الفصل",
                    "مخالفة أخرى"
                ]
            
            query = QSqlQuery(self.db)
            query.exec_("SELECT العنوان FROM تعديل_المسميات WHERE ID BETWEEN 7 AND 16 ORDER BY ID")
            
            violations = []
            while query.next():
                violations.append(query.value(0))
            
            return violations if violations else [
                "تأخر عن الطابور",
                "عدم إحضار الكتب",
                "عدم أداء الواجبات",
                "سوء السلوك",
                "عدم ارتداء الزي المدرسي",
                "استخدام الهاتف المحمول",
                "عدم احترام المعلم",
                "الفوضى في الفصل",
                "مخالفة أخرى"
            ]
        except Exception as e:
            print(f"خطأ في جلب المخالفات: {e}")
            return [
                "تأخر عن الطابور",
                "عدم إحضار الكتب",
                "عدم أداء الواجبات",
                "سوء السلوك",
                "عدم ارتداء الزي المدرسي",
                "استخدام الهاتف المحمول",
                "عدم احترام المعلم",
                "الفوضى في الفصل",
                "مخالفة أخرى"
            ]
    
    def get_procedures(self):
        """جلب الإجراءات من قاعدة البيانات"""
        try:
            if not self.db:
                return [
                    "تنبيه شفوي",
                    "تنبيه كتابي",
                    "استدعاء ولي الأمر",
                    "إحالة للإدارة",
                    "تكليف بأعمال إضافية",
                    "منع من النشاط الرياضي",
                    "الحرمان من الاستراحة",
                    "إجراء آخر"
                ]
            
            query = QSqlQuery(self.db)
            query.exec_("SELECT العنوان FROM تعديل_المسميات WHERE ID BETWEEN 17 AND 20 ORDER BY ID")
            
            procedures = []
            while query.next():
                procedures.append(query.value(0))
            
            return procedures if procedures else [
                "تنبيه شفوي",
                "تنبيه كتابي",
                "استدعاء ولي الأمر",
                "إحالة للإدارة",
                "تكليف بأعمال إضافية",
                "منع من النشاط الرياضي",
                "الحرمان من الاستراحة",
                "إجراء آخر"
            ]
        except Exception as e:
            print(f"خطأ في جلب الإجراءات: {e}")
            return [
                "تنبيه شفوي",
                "تنبيه كتابي",
                "استدعاء ولي الأمر",
                "إحالة للإدارة",
                "تكليف بأعمال إضافية",
                "منع من النشاط الرياضي",
                "الحرمان من الاستراحة",
                "إجراء آخر"
            ]
    
    def load_student_data(self):
        """تحميل بيانات التلميذ المحدد"""
        try:
            if not self.db or not self.student_code:
                return
            
            # الحصول على بيانات التلميذ
            query = QSqlQuery(self.db)
            query.prepare("""
                SELECT s.الرمز, s.رت, s.الاسم, s.النسب, l.المستوى, l.القسم
                FROM السجل_العام s
                JOIN اللوائح l ON s.الرمز = l.الرمز
                WHERE s.الرمز = ?
                ORDER BY l.السنة_الدراسية DESC
                LIMIT 1
            """)
            query.addBindValue(self.student_code)
            
            if query.exec_() and query.next():
                student_data = {
                    'code': query.value(0),
                    'rt': query.value(1),
                    'name': f"{query.value(2)} {query.value(3)}",
                    'level': query.value(4),
                    'section': query.value(5)
                }
                
                # تحديث البيانات في النموذج HTML باستخدام JavaScript
                js_code = f"""
                document.getElementById('student_code').value = '{student_data['code']}';
                document.getElementById('student_rt').value = '{student_data['rt']}';
                document.getElementById('student_name').value = '{student_data['name']}';
                
                // تحديد المستوى
                var levelSelect = document.getElementById('level_select');
                for (var i = 0; i < levelSelect.options.length; i++) {{
                    if (levelSelect.options[i].value === '{student_data['level']}') {{
                        levelSelect.selectedIndex = i;
                        break;
                    }}
                }}
                
                // تحديث الأقسام وتحديد القسم
                updateSections('{student_data['level']}', '{student_data['section']}');
                """
                
                self.web_view.page().runJavaScript(js_code)
                self.status_bar.showMessage(f"تم تحميل بيانات التلميذ: {student_data['name']}")
                
        except Exception as e:
            print(f"خطأ في تحميل بيانات التلميذ: {e}")
            self.status_bar.showMessage(f"خطأ في تحميل بيانات التلميذ: {str(e)}")
    
    def create_html_content(self, levels, subjects, violations, procedures):
        """إنشاء محتوى HTML للنافذة"""
        
        # تحويل القوائم إلى خيارات HTML
        levels_options = "\n".join([f'<option value="{level}">{level}</option>' for level in levels])
        subjects_options = "\n".join([f'<option value="{subject}">{subject}</option>' for subject in subjects])
        violations_options = "\n".join([f'<option value="{violation}">{violation}</option>' for violation in violations])
        procedures_options = "\n".join([f'<option value="{procedure}">{procedure}</option>' for procedure in procedures])
        
        html_content = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>مسك المخالفات</title>
            <style>
                * {{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }}
                
                body {{
                    font-family: 'Calibri', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);
                    padding: 20px;
                    direction: rtl;
                    color: #333;
                    min-height: 100vh;
                }}
                
                .container {{
                    max-width: 1200px;
                    margin: 0 auto;
                    background: white;
                    border-radius: 15px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                    overflow: hidden;
                }}
                
                .header {{
                    background: linear-gradient(135deg, #ff5722 0%, #f44336 100%);
                    color: white;
                    padding: 20px;
                    text-align: center;
                }}
                
                .header h1 {{
                    font-size: 2em;
                    margin-bottom: 5px;
                    font-weight: bold;
                }}
                
                .header .subtitle {{
                    font-size: 1.1em;
                    opacity: 0.9;
                }}
                
                .form-container {{
                    padding: 30px;
                }}
                
                .form-section {{
                    background: #f8f9fa;
                    border-radius: 10px;
                    padding: 20px;
                    margin-bottom: 20px;
                    border-right: 4px solid #ff5722;
                }}
                
                .form-section h3 {{
                    color: #ff5722;
                    font-size: 1.3em;
                    margin-bottom: 15px;
                    font-weight: bold;
                }}
                
                .form-row {{
                    display: flex;
                    gap: 20px;
                    margin-bottom: 15px;
                    align-items: center;
                    flex-wrap: wrap;
                }}
                
                .form-group {{
                    flex: 1;
                    min-width: 200px;
                }}
                
                .form-group label {{
                    display: block;
                    margin-bottom: 5px;
                    font-weight: bold;
                    color: #333;
                }}
                
                .form-control {{
                    width: 100%;
                    padding: 12px;
                    border: 2px solid #ddd;
                    border-radius: 8px;
                    font-size: 14px;
                    font-family: 'Calibri';
                    transition: border-color 0.3s ease;
                }}
                
                .form-control:focus {{
                    outline: none;
                    border-color: #ff5722;
                    box-shadow: 0 0 0 3px rgba(255, 87, 34, 0.1);
                }}
                
                .form-control[readonly] {{
                    background-color: #f5f5f5;
                    color: #666;
                }}
                
                textarea.form-control {{
                    min-height: 120px;
                    resize: vertical;
                }}
                
                .btn {{
                    background: linear-gradient(135deg, #ff5722, #f44336);
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-family: 'Calibri';
                    font-weight: bold;
                    transition: all 0.3s ease;
                }}
                
                .btn:hover {{
                    background: linear-gradient(135deg, #e64a19, #d32f2f);
                    transform: translateY(-2px);
                    box-shadow: 0 4px 10px rgba(0,0,0,0.2);
                }}
                
                .btn-secondary {{
                    background: linear-gradient(135deg, #607d8b, #455a64);
                }}
                
                .btn-secondary:hover {{
                    background: linear-gradient(135deg, #546e7a, #37474f);
                }}
                
                .dropdown-actions {{
                    display: flex;
                    gap: 10px;
                    margin-bottom: 10px;
                    flex-wrap: wrap;
                }}
                
                .current-date {{
                    background: #e8f5e8;
                    padding: 10px;
                    border-radius: 8px;
                    border-right: 4px solid #4CAF50;
                    margin-bottom: 20px;
                    font-weight: bold;
                }}
                
                @media (max-width: 768px) {{
                    .form-row {{
                        flex-direction: column;
                    }}
                    
                    .form-group {{
                        min-width: 100%;
                    }}
                    
                    .dropdown-actions {{
                        flex-direction: column;
                    }}
                }}
                
                .alert {{
                    padding: 15px;
                    border-radius: 8px;
                    margin-bottom: 20px;
                    font-weight: bold;
                }}
                
                .alert-success {{
                    background-color: #d4edda;
                    border: 1px solid #c3e6cb;
                    color: #155724;
                }}
                
                .alert-error {{
                    background-color: #f8d7da;
                    border: 1px solid #f5c6cb;
                    color: #721c24;
                }}
                
                .hidden {{
                    display: none;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>⚠️ نظام مسك المخالفات</h1>
                    <div class="subtitle">تسجيل ومتابعة مخالفات التلاميذ</div>
                </div>
                
                <div class="form-container">
                    <!-- عرض التاريخ الحالي -->
                    <div class="current-date">
                        📅 التاريخ الحالي: <span id="current_date">{datetime.now().strftime('%Y-%m-%d | %A')}</span>
                    </div>
                    
                    <!-- منطقة عرض الرسائل -->
                    <div id="message_area" class="hidden"></div>
                    
                    <!-- بيانات التلميذ -->
                    <div class="form-section">
                        <h3>👤 بيانات التلميذ</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="student_code">رمز التلميذ:</label>
                                <input type="text" id="student_code" class="form-control" readonly>
                            </div>
                            <div class="form-group">
                                <label for="student_rt">الرقم الترتيبي:</label>
                                <input type="text" id="student_rt" class="form-control" readonly>
                            </div>
                            <div class="form-group">
                                <label for="student_name">الاسم والنسب:</label>
                                <input type="text" id="student_name" class="form-control" readonly>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="level_select">المستوى:</label>
                                <select id="level_select" class="form-control" onchange="updateSections(this.value)">
                                    <option value="">اختر المستوى...</option>
                                    {levels_options}
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="section_select">القسم:</label>
                                <select id="section_select" class="form-control">
                                    <option value="">اختر القسم...</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- معلومات المادة والأستاذ -->
                    <div class="form-section">
                        <h3>📚 معلومات المادة والأستاذ</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="subject_select">المادة:</label>
                                <select id="subject_select" class="form-control" onchange="updateTeachers(this.value)">
                                    <option value="">اختر المادة...</option>
                                    {subjects_options}
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="teacher_select">الأستاذ:</label>
                                <select id="teacher_select" class="form-control">
                                    <option value="">اختر الأستاذ...</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- مسك المخالفات -->
                    <div class="form-section">
                        <h3>⚠️ تفاصيل المخالفات</h3>
                        
                        <!-- قائمة المخالفات الجاهزة -->
                        <div class="form-row">
                            <div class="form-group">
                                <label for="violations_select">قائمة المخالفات الجاهزة:</label>
                                <div class="dropdown-actions">
                                    <select id="violations_select" class="form-control">
                                        <option value="">اختر مخالفة...</option>
                                        {violations_options}
                                    </select>
                                    <button type="button" class="btn btn-secondary" onclick="addViolation()">إضافة للقائمة</button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- المخالفات المرتكبة -->
                        <div class="form-row">
                            <div class="form-group">
                                <label for="violations_text">المخالفات المرتكبة:</label>
                                <textarea id="violations_text" class="form-control" placeholder="اكتب تفاصيل المخالفات المرتكبة هنا..."></textarea>
                            </div>
                        </div>
                        
                        <!-- قائمة الإجراءات الجاهزة -->
                        <div class="form-row">
                            <div class="form-group">
                                <label for="procedures_select">قائمة الإجراءات الجاهزة:</label>
                                <div class="dropdown-actions">
                                    <select id="procedures_select" class="form-control">
                                        <option value="">اختر إجراء...</option>
                                        {procedures_options}
                                    </select>
                                    <button type="button" class="btn btn-secondary" onclick="addProcedure()">إضافة للقائمة</button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- الإجراءات المطلوبة -->
                        <div class="form-row">
                            <div class="form-group">
                                <label for="procedures_text">الإجراءات المطلوبة:</label>
                                <textarea id="procedures_text" class="form-control" placeholder="اكتب الإجراءات المطلوبة هنا..."></textarea>
                            </div>
                        </div>
                        
                        <!-- إجراءات الحراسة العامة -->
                        <div class="form-row">
                            <div class="form-group">
                                <label for="guard_actions_text">الإجراءات التي اتخذتها الحراسة العامة:</label>
                                <textarea id="guard_actions_text" class="form-control" placeholder="اكتب الإجراءات التي اتخذتها الحراسة العامة هنا..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <script>
                // دالة تحديث الأقسام عند تغيير المستوى
                function updateSections(level, selectedSection = '') {{
                    const sectionSelect = document.getElementById('section_select');
                    sectionSelect.innerHTML = '<option value="">اختر القسم...</option>';
                    
                    if (level) {{
                        // استدعاء Python لجلب الأقسام
                        window.pybridge = window.pybridge || {{}};
                        if (window.pybridge.getSections) {{
                            window.pybridge.getSections(level, selectedSection);
                        }}
                    }}
                }}
                
                // دالة تحديث الأساتذة عند تغيير المادة
                function updateTeachers(subject, selectedTeacher = '') {{
                    const teacherSelect = document.getElementById('teacher_select');
                    teacherSelect.innerHTML = '<option value="">اختر الأستاذ...</option>';
                    
                    if (subject) {{
                        // استدعاء Python لجلب الأساتذة
                        if (window.pybridge && window.pybridge.getTeachers) {{
                            window.pybridge.getTeachers(subject, selectedTeacher);
                        }}
                    }}
                }}
                
                // دالة إضافة مخالفة من القائمة المنسدلة
                function addViolation() {{
                    const violationsSelect = document.getElementById('violations_select');
                    const violationsText = document.getElementById('violations_text');
                    
                    if (violationsSelect.value) {{
                        const currentText = violationsText.value;
                        if (currentText) {{
                            violationsText.value = currentText + '\\n- ' + violationsSelect.value;
                        }} else {{
                            violationsText.value = '- ' + violationsSelect.value;
                        }}
                        violationsSelect.selectedIndex = 0;
                    }}
                }}
                
                // دالة إضافة إجراء من القائمة المنسدلة
                function addProcedure() {{
                    const proceduresSelect = document.getElementById('procedures_select');
                    const proceduresText = document.getElementById('procedures_text');
                    
                    if (proceduresSelect.value) {{
                        const currentText = proceduresText.value;
                        if (currentText) {{
                            proceduresText.value = currentText + '\\n- ' + proceduresSelect.value;
                        }} else {{
                            proceduresText.value = '- ' + proceduresSelect.value;
                        }}
                        proceduresSelect.selectedIndex = 0;
                    }}
                }}
                
                // دالة عرض الرسائل
                function showMessage(message, type = 'success') {{
                    const messageArea = document.getElementById('message_area');
                    messageArea.className = `alert alert-${{type}}`;
                    messageArea.textContent = message;
                    messageArea.classList.remove('hidden');
                    
                    // إخفاء الرسالة بعد 5 ثواني
                    setTimeout(() => {{
                        messageArea.classList.add('hidden');
                    }}, 5000);
                }}
                
                // دالة جمع بيانات النموذج
                function getFormData() {{
                    return {{
                        student_code: document.getElementById('student_code').value,
                        student_rt: document.getElementById('student_rt').value,
                        student_name: document.getElementById('student_name').value,
                        level: document.getElementById('level_select').value,
                        section: document.getElementById('section_select').value,
                        subject: document.getElementById('subject_select').value,
                        teacher: document.getElementById('teacher_select').value,
                        violations: document.getElementById('violations_text').value,
                        procedures: document.getElementById('procedures_text').value,
                        guard_actions: document.getElementById('guard_actions_text').value
                    }};
                }}
                
                // دالة مسح النموذج
                function clearForm() {{
                    document.getElementById('violations_text').value = '';
                    document.getElementById('procedures_text').value = '';
                    document.getElementById('guard_actions_text').value = '';
                    document.getElementById('violations_select').selectedIndex = 0;
                    document.getElementById('procedures_select').selectedIndex = 0;
                    showMessage('تم مسح البيانات بنجاح');
                }}
                
                // إعداد التاريخ العربي
                document.addEventListener('DOMContentLoaded', function() {{
                    const now = new Date();
                    const days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
                    const dayName = days[now.getDay()];
                    const dateString = now.toLocaleDateString('ar-EG') + ' | ' + dayName;
                    document.getElementById('current_date').textContent = dateString;
                }});
            </script>
        </body>
        </html>
        """
        
        return html_content
    
    def save_violation(self):
        """حفظ المخالفة في قاعدة البيانات"""
        try:
            # جمع البيانات من النموذج HTML
            js_code = """
            (function() {
                return getFormData();
            })();
            """
            
            def handle_form_data(result):
                try:
                    if not result:
                        self.show_message("فشل في جمع بيانات النموذج", "error")
                        return
                    
                    form_data = result
                    
                    # التحقق من صحة البيانات
                    if not form_data.get('student_code'):
                        self.show_message("يرجى التأكد من وجود رمز التلميذ", "error")
                        return
                    
                    if not form_data.get('violations'):
                        self.show_message("يرجى كتابة المخالفات المرتكبة", "error")
                        return
                    
                    # حفظ المخالفة في قاعدة البيانات
                    success = self.save_violation_to_db(form_data)
                    
                    if success:
                        self.show_message("تم حفظ المخالفة بنجاح! ✅", "success")
                        # مسح الحقول النصية فقط
                        self.clear_form()
                    else:
                        self.show_message("فشل في حفظ المخالفة", "error")
                        
                except Exception as e:
                    print(f"خطأ في معالجة بيانات النموذج: {e}")
                    self.show_message(f"خطأ في معالجة البيانات: {str(e)}", "error")
            
            self.web_view.page().runJavaScript(js_code, handle_form_data)
            
        except Exception as e:
            print(f"خطأ في حفظ المخالفة: {e}")
            self.show_message(f"خطأ في حفظ المخالفة: {str(e)}", "error")
    
    def save_violation_to_db(self, form_data):
        """حفظ المخالفة في قاعدة البيانات"""
        try:
            if not self.db:
                print("لا يوجد اتصال بقاعدة البيانات")
                return False
            
            # الحصول على السنة الدراسية والأسدس الحاليين
            query = QSqlQuery(self.db)
            query.exec_("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
            
            current_school_year = "2024/2025"
            current_semester = "الأول"
            
            if query.next():
                current_school_year = query.value(0) or current_school_year
                current_semester = query.value(1) or current_semester
            
            # إدراج المخالفة في قاعدة البيانات
            insert_query = QSqlQuery(self.db)
            insert_query.prepare("""
                INSERT INTO المخالفات (
                    التاريخ, الرمز, الاسم_والنسب, المستوى, القسم,
                    المادة, الأستاذ, المخالفات_المرتكبة, الإجراءات_المطلوبة,
                    إجراءات_الحراسة_العامة, السنة_الدراسية, الأسدس
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """)
            
            insert_query.addBindValue(datetime.now().strftime('%Y-%m-%d'))
            insert_query.addBindValue(form_data.get('student_code', ''))
            insert_query.addBindValue(form_data.get('student_name', ''))
            insert_query.addBindValue(form_data.get('level', ''))
            insert_query.addBindValue(form_data.get('section', ''))
            insert_query.addBindValue(form_data.get('subject', ''))
            insert_query.addBindValue(form_data.get('teacher', ''))
            insert_query.addBindValue(form_data.get('violations', ''))
            insert_query.addBindValue(form_data.get('procedures', ''))
            insert_query.addBindValue(form_data.get('guard_actions', ''))
            insert_query.addBindValue(current_school_year)
            insert_query.addBindValue(current_semester)
            
            if insert_query.exec_():
                print("تم حفظ المخالفة بنجاح في قاعدة البيانات")
                return True
            else:
                print(f"خطأ في حفظ المخالفة: {insert_query.lastError().text()}")
                return False
                
        except Exception as e:
            print(f"خطأ في حفظ المخالفة في قاعدة البيانات: {e}")
            return False
    
    def clear_form(self):
        """مسح بيانات النموذج"""
        try:
            js_code = """
            clearForm();
            """
            self.web_view.page().runJavaScript(js_code)
            
        except Exception as e:
            print(f"خطأ في مسح النموذج: {e}")
    
    def show_message(self, message, msg_type="success"):
        """عرض رسالة في النافذة"""
        try:
            js_code = f"""
            showMessage('{message}', '{msg_type}');
            """
            self.web_view.page().runJavaScript(js_code)
            self.status_bar.showMessage(message)
            
        except Exception as e:
            print(f"خطأ في عرض الرسالة: {e}")
    
    def print_violation(self):
        """طباعة المخالفة"""
        try:
            # جمع البيانات من النموذج HTML
            js_code = """
            (function() {
                return getFormData();
            })();
            """
            
            def handle_print_data(result):
                try:
                    if not result:
                        self.show_message("فشل في جمع بيانات المخالفة للطباعة", "error")
                        return
                    
                    form_data = result
                    
                    # التحقق من وجود بيانات كافية للطباعة
                    if not form_data.get('student_code') or not form_data.get('violations'):
                        self.show_message("يرجى التأكد من وجود بيانات التلميذ والمخالفات قبل الطباعة", "error")
                        return
                    
                    # إنشاء تقرير HTML للطباعة
                    print_html = self.create_print_html(form_data)
                    
                    # إنشاء نافذة طباعة
                    self.print_window = QWebEngineView()
                    self.print_window.setWindowTitle("طباعة المخالفة")
                    self.print_window.resize(800, 600)
                    self.print_window.setHtml(print_html)
                    self.print_window.show()
                    
                    # طباعة المستند
                    def print_page():
                        try:
                            printer = QPrinter(QPrinter.HighResolution)
                            printer.setPageSize(QPrinter.A4)
                            printer.setOrientation(QPrinter.Portrait)
                            
                            print_dialog = QPrintDialog(printer, self)
                            print_dialog.setWindowTitle("طباعة المخالفة")
                            
                            if print_dialog.exec_() == QPrintDialog.Accepted:
                                self.print_window.page().print(printer, lambda success: 
                                    self.show_message("تمت الطباعة بنجاح ✅" if success else "فشلت الطباعة ❌", 
                                                    "success" if success else "error")
                                )
                        except Exception as e:
                            print(f"خطأ في الطباعة: {e}")
                            self.show_message(f"خطأ في الطباعة: {str(e)}", "error")
                    
                    # تأخير الطباعة لضمان تحميل المحتوى
                    QTimer.singleShot(1000, print_page)
                    
                except Exception as e:
                    print(f"خطأ في معالجة بيانات الطباعة: {e}")
                    self.show_message(f"خطأ في الطباعة: {str(e)}", "error")
            
            self.web_view.page().runJavaScript(js_code, handle_print_data)
            
        except Exception as e:
            print(f"خطأ في طباعة المخالفة: {e}")
            self.show_message(f"خطأ في الطباعة: {str(e)}", "error")
    
    def create_print_html(self, form_data):
        """إنشاء محتوى HTML للطباعة"""
        return f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تقرير مخالفة</title>
            <style>
                @page {{ size: A4 portrait; margin: 2cm; }}
                body {{ font-family: 'Calibri'; font-size: 14pt; text-align: right; }}
                h1 {{ text-align: center; color: #ff5722; margin-bottom: 20px; }}
                .header {{ text-align: center; margin-bottom: 30px; }}
                .section {{ margin-bottom: 20px; padding: 10px; border: 1px solid #ddd; }}
                .section h3 {{ color: #ff5722; margin-bottom: 10px; }}
                table {{ width: 100%; border-collapse: collapse; margin-bottom: 20px; }}
                th, td {{ border: 1px solid #000; padding: 8px; text-align: right; }}
                th {{ background-color: #f2f2f2; }}
                .footer {{ text-align: center; margin-top: 30px; font-size: 12pt; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>⚠️ تقرير مخالفة</h1>
                <p>المملكة المغربية - وزارة التربية الوطنية</p>
                <p>التاريخ: {datetime.now().strftime('%Y-%m-%d')}</p>
            </div>
            
            <div class="section">
                <h3>معلومات التلميذ</h3>
                <table>
                    <tr>
                        <th>رمز التلميذ</th>
                        <td>{form_data.get('student_code', '')}</td>
                        <th>الرقم الترتيبي</th>
                        <td>{form_data.get('student_rt', '')}</td>
                    </tr>
                    <tr>
                        <th>الاسم والنسب</th>
                        <td colspan="3">{form_data.get('student_name', '')}</td>
                    </tr>
                    <tr>
                        <th>المستوى</th>
                        <td>{form_data.get('level', '')}</td>
                        <th>القسم</th>
                        <td>{form_data.get('section', '')}</td>
                    </tr>
                </table>
            </div>
            
            <div class="section">
                <h3>معلومات المادة</h3>
                <table>
                    <tr>
                        <th>المادة</th>
                        <td>{form_data.get('subject', '')}</td>
                        <th>الأستاذ</th>
                        <td>{form_data.get('teacher', '')}</td>
                    </tr>
                </table>
            </div>
            
            <div class="section">
                <h3>المخالفات المرتكبة</h3>
                <p>{form_data.get('violations', '').replace(chr(10), '<br>')}</p>
            </div>
            
            <div class="section">
                <h3>الإجراءات المطلوبة</h3>
                <p>{form_data.get('procedures', '').replace(chr(10), '<br>')}</p>
            </div>
            
            <div class="section">
                <h3>إجراءات الحراسة العامة</h3>
                <p>{form_data.get('guard_actions', '').replace(chr(10), '<br>')}</p>
            </div>
            
            <div class="footer">
                <p>وقت الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
        </body>
        </html>
        """
        
        return html_content


def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    try:
        print("🚀 بدء تشغيل نافذة المخالفات...")
        
        # التحقق من وجود QApplication
        if QApplication.instance() is None:
            app = QApplication(sys.argv)
            print("✅ تم إنشاء QApplication جديد")
        else:
            app = QApplication.instance()
            print("✅ استخدام QApplication موجود")
            
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء النافذة للاختبار
        print("📝 إنشاء نافذة المخالفات...")
        window = StudentViolationsWindow(
            student_code="12345",
            student_name="أحمد محمد علي",
            db=None
        )
        
        print("🖥️ عرض النافذة...")
        window.show()
        
        print("✅ تم فتح نافذة المخالفات بنجاح!")
        print("💡 اضغط Ctrl+C للخروج من الاختبار")
        
        # تشغيل التطبيق
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النافذة: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
