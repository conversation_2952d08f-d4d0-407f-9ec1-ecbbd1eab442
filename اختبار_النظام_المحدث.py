#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام المحدث - إعدادات البرنامج مع 6 أزرار
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon

def test_updated_system():
    """اختبار النظام المحدث"""
    print("🔄 اختبار النظام المحدث - إعدادات البرنامج مع 6 أزرار")
    print("=" * 70)
    
    app = QApplication(sys.argv)
    
    try:
        from main_window import MainWindow
        print("✅ تم استيراد MainWindow")
        
        main_window = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # فحص النظام المحدث
        def check_updated_system():
            print("\n🔍 فحص النظام المحدث:")
            
            # فحص التبويبات
            tab_count = main_window.tabWidget.count()
            print(f"📋 عدد التبويبات: {tab_count}")
            
            for i in range(tab_count):
                tab_text = main_window.tabWidget.tabText(i)
                tab_data = main_window.tabWidget.tabData(i)
                print(f"   {i}: {tab_text} ({tab_data})")
                if tab_text == "إعدادات البرنامج":
                    print("   ✅ تم العثور على تبويب إعدادات البرنامج")
            
            # فحص القائمة المنسدلة المحدثة
            if hasattr(main_window, 'dropdown_widget'):
                print("✅ القائمة المنسدلة موجودة")
                dropdown = main_window.dropdown_widget
                print(f"   الحجم: {dropdown.size()}")
                print(f"   الارتفاع: {dropdown.height()}")
                print(f"   مرئية: {dropdown.isVisible()}")
                
                # فحص الأزرار المحدثة في القائمة
                if hasattr(main_window, 'dropdown_items'):
                    print("📋 أزرار القائمة المنسدلة المحدثة:")
                    for text, window_key, color in main_window.dropdown_items:
                        print(f"   {text} -> {window_key} ({color})")
                        
                        # التحقق من وجود النافذة
                        if window_key in main_window.windows:
                            print(f"      ✅ النافذة موجودة")
                        else:
                            print(f"      ❌ النافذة غير موجودة")
                
            else:
                print("❌ القائمة المنسدلة غير موجودة")
            
            # اختبار إظهار القائمة
            print("\n🎯 اختبار إظهار القائمة المحدثة...")
            if hasattr(main_window, '_show_dropdown'):
                main_window._show_dropdown()
                print(f"   بعد الإظهار - الارتفاع: {dropdown.height()}")
                print(f"   بعد الإظهار - مرئية: {dropdown.isVisible()}")
                
                # إخفاء بعد 5 ثوان
                QTimer.singleShot(5000, lambda: main_window._hide_dropdown())
                
            else:
                print("❌ دالة _show_dropdown غير موجودة")
        
        # فحص النظام بعد ثانية واحدة
        QTimer.singleShot(1000, check_updated_system)
        
        main_window.show()
        main_window.setWindowTitle("النظام المحدث - إعدادات البرنامج مع 6 أزرار")
        
        print("\n📋 تعليمات الاختبار:")
        print("1. ابحث عن تبويب 'إعدادات البرنامج' (بدلاً من بيانات المؤسسة)")
        print("2. انقر على تبويب 'إعدادات البرنامج'")
        print("3. يجب أن تظهر قائمة منسدلة بـ 6 أزرار:")
        print("   🏢 بيانات المؤسسة (أزرق)")
        print("   ⚙️ تهيئة البرنامج (برتقالي)")
        print("   🏫 البنية التربوية (بنفسجي)")
        print("   📊 الإحصائيات (أخضر)")
        print("   📥 استيراد البيانات وتحيينها (وردي)")
        print("   ✏️ تعديل المسميات (بني)")
        print("4. انقر على أي زر في القائمة")
        print("5. يجب أن ينقلك مباشرة للنافذة المطلوبة")
        
        print("\n🆕 التحديثات الجديدة:")
        print("   ✅ تغيير اسم التبويب إلى 'إعدادات البرنامج'")
        print("   ✅ إضافة زر 'استيراد البيانات وتحيينها'")
        print("   ✅ إضافة زر 'تعديل المسميات'")
        print("   ✅ إزالة هذين التبويبين من شريط التبويبات")
        print("   ✅ 6 أزرار في القائمة المنسدلة بدلاً من 4")
        
        print("\n🎨 الألوان الجديدة:")
        print("   📥 استيراد البيانات: وردي (#E91E63)")
        print("   ✏️ تعديل المسميات: بني (#795548)")
        
        print("\n🔄 التغييرات المطبقة:")
        print("   • تبويب 'بيانات المؤسسة' → 'إعدادات البرنامج'")
        print("   • إزالة تبويب 'استيراد البيانات وتحيينها'")
        print("   • إزالة تبويب 'تعديل المسميات'")
        print("   • إضافة الاثنين للقائمة المنسدلة")
        print("   • تقليل التبويبات من 8 إلى 6")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return 1

def show_updated_comparison():
    """مقارنة النظام قبل وبعد التحديث"""
    print("\n" + "=" * 70)
    print("📊 مقارنة النظام قبل وبعد التحديث")
    print("=" * 70)
    
    comparison = [
        ("اسم التبويب الرئيسي", "بيانات المؤسسة", "إعدادات البرنامج"),
        ("عدد أزرار القائمة", "4 أزرار", "6 أزرار"),
        ("عدد التبويبات", "8 تبويبات", "6 تبويبات"),
        ("استيراد البيانات", "تبويب منفصل", "زر في القائمة"),
        ("تعديل المسميات", "تبويب منفصل", "زر في القائمة"),
        ("شريط التبويبات", "مزدحم نسبياً", "أكثر نظافة"),
        ("التنظيم", "جيد", "ممتاز")
    ]
    
    print(f"{'الخاصية':<25} {'قبل التحديث':<20} {'بعد التحديث':<20}")
    print("-" * 70)
    for feature, before, after in comparison:
        print(f"{feature:<25} {before:<20} {after:<20}")

if __name__ == "__main__":
    print("🔄 مرحباً بك في اختبار النظام المحدث!")
    
    # عرض مقارنة التحديثات
    show_updated_comparison()
    
    print("\n" + "=" * 70)
    print("🚀 بدء تشغيل النظام المحدث...")
    print("=" * 70)
    
    # تشغيل الاختبار
    exit_code = test_updated_system()
    
    print(f"\n🔚 انتهى الاختبار بكود الخروج: {exit_code}")
    sys.exit(exit_code)
