#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
import traceback
from datetime import datetime, date
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtSql import *

# تم إزالة جميع الاستيرادات الخارجية لجعل الملف مستقلاً

# فحص توفر قاعدة البيانات قبل تشغيل البرنامج
def check_database_availability():
    """التحقق من وجود قاعدة البيانات ومدى إمكانية الوصول إليها"""
    db_path = "data.db"
    if not os.path.exists(db_path):
        print(f"تحذير: ملف قاعدة البيانات {db_path} غير موجود")
        return False
    
    try:
        # محاولة الاتصال بقاعدة البيانات للتأكد من صحتها
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود الجداول المطلوبة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='students'")
        if not cursor.fetchone():
            print("تحذير: جدول students غير موجود في قاعدة البيانات")
            conn.close()
            return False
        
        conn.close()
        return True
    except Exception as e:
        print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        return False

class StudentCardWindow(QMainWindow):
    """نافذة بطاقة التلميذ - مستقلة تماماً"""
    
    def __init__(self, external_db=None, external_academic_year=None):
        super().__init__()
        
        # تخزين المعاملات الخارجية
        self.external_db = external_db
        self.external_academic_year = external_academic_year
        
        # متغيرات النافذة
        self.db = None
        self.current_record_id = None
        self.current_academic_year = None
        
        # إعداد النافذة
        self.setupUI()
        
        # إعداد قاعدة البيانات والسنة الدراسية
        if self.connect_to_database():
            self.load_current_academic_year()
        
    def setupUI(self):
        """إعداد واجهة المستخدم"""
        # Configure the main window
        self.setWindowTitle("📚 بطاقة تلميذ")
        self.setFixedSize(900, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إضافة أيقونة للنافذة
        try:
            from PyQt5.QtGui import QIcon
            # يمكن إضافة أيقونة هنا إذا كانت متوفرة
            # self.setWindowIcon(QIcon("icon.png"))
        except:
            pass
        
        # تطبيق نمط احترافي للنافذة الرئيسية مثل sub262_window
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f0f4f8,
                    stop: 1 #e8f2f7
                );
            }
        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # العنوان الرئيسي
        title_label = QLabel("📚 بطاقة تلميذ")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setMaximumHeight(60)
        title_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #1e88e5,
                    stop: 0.5 #1976d2,
                    stop: 1 #1e88e5
                );
                color: white;
                padding: 15px;
                border-radius: 12px;
                font-weight: bold;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # إنشاء تخطيط أفقي لحقول البحث
        info_layout = QHBoxLayout()
        info_layout.setSpacing(10)
        
        # حقل اسم الطالب - بنفس تصميم sub262_window
        self.name_field = QLineEdit("الاسم والنسب: ")
        self.name_field.setFont(QFont("Calibri", 14, QFont.Bold))
        self.name_field.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #1976d2,
                    stop: 1 #1565c0
                );
                color: white;
                border: 2px solid #1565c0;
                border-radius: 8px;
                padding: 8px 12px;
                font-weight: bold;
            }
        """)
        self.name_field.setMinimumWidth(400)
        self.name_field.setReadOnly(True)

        # حقل رمز الطالب - بنفس تصميم sub262_window
        self.code_field = QLineEdit("الرمز: ")
        self.code_field.setFont(QFont("Calibri", 14, QFont.Bold))
        self.code_field.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #1976d2,
                    stop: 1 #1565c0
                );
                color: white;
                border: 2px solid #1565c0;
                border-radius: 8px;
                padding: 8px 12px;
                font-weight: bold;
            }
        """)
        self.code_field.setMinimumWidth(200)
        self.code_field.setReadOnly(True)
        
        info_layout.addWidget(self.name_field)
        info_layout.addSpacing(4)
        info_layout.addWidget(self.code_field)

        main_layout.addLayout(info_layout)

        # إنشاء محتوى البيانات الأساسية مباشرة بدون تبويبات
        self.create_student_data_content(main_layout)
        
        # إضافة متغير لتخزين السنة الدراسية الحالية
        self.current_academic_year = None

        # Connect to database
        self.connect_to_database()

        # تحميل السنة الدراسية قبل تحميل البيانات
        self.load_current_academic_year()

        # Load initial data only if no external record is expected
        # تم تعطيل تحميل أول سجل تلقائياً لتجنب إعادة تعيين current_record_id
        # self.current_record_id = None
        # self.load_first_record()

        # عرض النافذة بالحجم الكامل
        # تم نقل show() خارج __init__ ليتم استدعاؤه من الخارج
        # self.show()

    def create_student_data_content(self, main_layout):
        """إنشاء محتوى بيانات التلميذ مباشرة بدون تبويبات"""
        # إنشاء منطقة تمرير للبيانات
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setContentsMargins(10, 10, 10, 10)
        scroll_layout.setSpacing(15)

        # إضافة عنوان للبيانات الشخصية
        personal_title = QLabel("👤 البيانات الشخصية")
        personal_title.setFont(QFont("Calibri", 14, QFont.Bold))
        personal_title.setStyleSheet("""
            QLabel {
                color: #1976d2;
                padding: 8px;
                background-color: #e3f2fd;
                border-radius: 8px;
                margin: 5px 0px;
            }
        """)
        scroll_layout.addWidget(personal_title)

        # Personal data section
        self.personal_data_frame = QFrame()
        self.personal_data_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #1976d2;
                border-radius: 10px;
                padding: 10px;
                margin: 5px;
            }
        """)

        # تخطيط أفقي للبيانات الشخصية
        self.personal_data_layout = QVBoxLayout(self.personal_data_frame)
        self.personal_data_layout.setContentsMargins(10, 10, 10, 10)
        self.personal_data_layout.setSpacing(8)

        # الصف الأول: الرمز والاسم
        row1_layout = QHBoxLayout()
        row1_layout.setSpacing(15)

        # الرمز
        code_label = QLabel("الرمز:")
        code_label.setFont(QFont("Calibri", 13, QFont.Bold))
        code_label.setStyleSheet("color: #1976d2; min-width: 80px;")
        self.code_display = QLineEdit()
        self.code_display.setFont(QFont("Calibri", 13))
        self.code_display.setReadOnly(True)
        self.code_display.setFixedWidth(150)
        self.code_display.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 6px;
                background-color: #f8f9fa;
            }
        """)

        # الاسم
        name_label = QLabel("الاسم والنسب:")
        name_label.setFont(QFont("Calibri", 13, QFont.Bold))
        name_label.setStyleSheet("color: #1976d2; min-width: 100px;")
        self.name_display = QLineEdit()
        self.name_display.setFont(QFont("Calibri", 13))
        self.name_display.setReadOnly(True)
        self.name_display.setMinimumWidth(300)
        self.name_display.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 6px;
                background-color: #f8f9fa;
            }
        """)

        row1_layout.addWidget(code_label)
        row1_layout.addWidget(self.code_display)
        row1_layout.addWidget(name_label)
        row1_layout.addWidget(self.name_display)
        row1_layout.addStretch()

        # الصف الثاني: النوع وتاريخ الازدياد
        row2_layout = QHBoxLayout()
        row2_layout.setSpacing(15)

        # النوع
        gender_label = QLabel("النوع:")
        gender_label.setFont(QFont("Calibri", 13, QFont.Bold))
        gender_label.setStyleSheet("color: #1976d2; min-width: 80px;")
        self.gender_display = QLineEdit()
        self.gender_display.setFont(QFont("Calibri", 13))
        self.gender_display.setReadOnly(True)
        self.gender_display.setFixedWidth(100)
        self.gender_display.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 6px;
                background-color: #f8f9fa;
            }
        """)

        # تاريخ الازدياد
        birth_date_label = QLabel("تاريخ الازدياد:")
        birth_date_label.setFont(QFont("Calibri", 13, QFont.Bold))
        birth_date_label.setStyleSheet("color: #1976d2; min-width: 100px;")
        self.birth_date_display = QLineEdit()
        self.birth_date_display.setFont(QFont("Calibri", 13))
        self.birth_date_display.setReadOnly(True)
        self.birth_date_display.setFixedWidth(150)
        self.birth_date_display.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 6px;
                background-color: #f8f9fa;
            }
        """)

        row2_layout.addWidget(gender_label)
        row2_layout.addWidget(self.gender_display)
        row2_layout.addWidget(birth_date_label)
        row2_layout.addWidget(self.birth_date_display)
        row2_layout.addStretch()

        # الصف الثالث: مكان الازدياد
        row3_layout = QHBoxLayout()
        row3_layout.setSpacing(15)

        birth_place_label = QLabel("مكان الازدياد:")
        birth_place_label.setFont(QFont("Calibri", 13, QFont.Bold))
        birth_place_label.setStyleSheet("color: #1976d2; min-width: 100px;")
        self.birth_place_display = QLineEdit()
        self.birth_place_display.setFont(QFont("Calibri", 13))
        self.birth_place_display.setReadOnly(True)
        self.birth_place_display.setMinimumWidth(250)
        self.birth_place_display.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 6px;
                background-color: #f8f9fa;
            }
        """)

        row3_layout.addWidget(birth_place_label)
        row3_layout.addWidget(self.birth_place_display)
        row3_layout.addStretch()

        # إضافة الصفوف إلى التخطيط الرئيسي
        self.personal_data_layout.addLayout(row1_layout)
        self.personal_data_layout.addLayout(row2_layout)
        self.personal_data_layout.addLayout(row3_layout)

        scroll_layout.addWidget(self.personal_data_frame)

        # إضافة عنوان للبيانات المدرسية
        school_title = QLabel("🏫 البيانات المدرسية")
        school_title.setFont(QFont("Calibri", 14, QFont.Bold))
        school_title.setStyleSheet("""
            QLabel {
                color: #1976d2;
                padding: 8px;
                background-color: #e3f2fd;
                border-radius: 8px;
                margin: 5px 0px;
            }
        """)
        scroll_layout.addWidget(school_title)

        # School data section
        self.school_data_frame = QFrame()
        self.school_data_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #1976d2;
                border-radius: 10px;
                padding: 10px;
                margin: 5px;
            }
        """)

        # تخطيط أفقي للبيانات المدرسية
        self.school_data_layout = QVBoxLayout(self.school_data_frame)
        self.school_data_layout.setContentsMargins(10, 10, 10, 10)
        self.school_data_layout.setSpacing(8)

        # الصف الأول: السنة الدراسية والمستوى
        school_row1_layout = QHBoxLayout()
        school_row1_layout.setSpacing(15)

        # السنة الدراسية
        school_year_label = QLabel("السنة الدراسية:")
        school_year_label.setFont(QFont("Calibri", 13, QFont.Bold))
        school_year_label.setStyleSheet("color: #1976d2; min-width: 100px;")
        self.school_year_display = QLineEdit()
        self.school_year_display.setFont(QFont("Calibri", 13))
        self.school_year_display.setReadOnly(True)
        self.school_year_display.setFixedWidth(150)
        self.school_year_display.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 6px;
                background-color: #f8f9fa;
            }
        """)

        # المستوى
        level_label = QLabel("المستوى:")
        level_label.setFont(QFont("Calibri", 13, QFont.Bold))
        level_label.setStyleSheet("color: #1976d2; min-width: 80px;")
        self.level_display = QLineEdit()
        self.level_display.setFont(QFont("Calibri", 13))
        self.level_display.setReadOnly(True)
        self.level_display.setFixedWidth(200)
        self.level_display.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 6px;
                background-color: #f8f9fa;
            }
        """)

        school_row1_layout.addWidget(school_year_label)
        school_row1_layout.addWidget(self.school_year_display)
        school_row1_layout.addWidget(level_label)
        school_row1_layout.addWidget(self.level_display)
        school_row1_layout.addStretch()

        # الصف الثاني: القسم ورت
        school_row2_layout = QHBoxLayout()
        school_row2_layout.setSpacing(15)

        # القسم
        class_label = QLabel("القسم:")
        class_label.setFont(QFont("Calibri", 13, QFont.Bold))
        class_label.setStyleSheet("color: #1976d2; min-width: 80px;")
        self.class_display = QLineEdit()
        self.class_display.setFont(QFont("Calibri", 13))
        self.class_display.setReadOnly(True)
        self.class_display.setFixedWidth(150)
        self.class_display.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 6px;
                background-color: #f8f9fa;
            }
        """)

        # رت
        rt_label = QLabel("رت:")
        rt_label.setFont(QFont("Calibri", 13, QFont.Bold))
        rt_label.setStyleSheet("color: #1976d2; min-width: 50px;")
        self.rt_display = QLineEdit()
        self.rt_display.setFont(QFont("Calibri", 13))
        self.rt_display.setReadOnly(True)
        self.rt_display.setFixedWidth(80)
        self.rt_display.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 6px;
                background-color: #f8f9fa;
            }
        """)

        school_row2_layout.addWidget(class_label)
        school_row2_layout.addWidget(self.class_display)
        school_row2_layout.addWidget(rt_label)
        school_row2_layout.addWidget(self.rt_display)
        school_row2_layout.addStretch()

        # إضافة الصفوف إلى التخطيط الرئيسي
        self.school_data_layout.addLayout(school_row1_layout)
        self.school_data_layout.addLayout(school_row2_layout)

        scroll_layout.addWidget(self.school_data_frame)

        # إضافة عنوان لمعلومات الاتصال
        contact_title = QLabel("📞 معلومات الاتصال")
        contact_title.setFont(QFont("Calibri", 14, QFont.Bold))
        contact_title.setStyleSheet("""
            QLabel {
                color: #1976d2;
                padding: 8px;
                background-color: #e3f2fd;
                border-radius: 8px;
                margin: 5px 0px;
            }
        """)
        scroll_layout.addWidget(contact_title)

        # Contact Information section
        self.contact_frame = QFrame()
        self.contact_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #1976d2;
                border-radius: 10px;
                padding: 10px;
                margin: 5px;
            }
        """)

        # تخطيط أفقي لمعلومات الاتصال
        self.contact_layout = QVBoxLayout(self.contact_frame)
        self.contact_layout.setContentsMargins(10, 10, 10, 10)
        self.contact_layout.setSpacing(8)

        # الصف الأول: أرقام الهواتف
        contact_row_layout = QHBoxLayout()
        contact_row_layout.setSpacing(15)

        # الهاتف 1
        phone1_label = QLabel("الهاتف 1:")
        phone1_label.setFont(QFont("Calibri", 13, QFont.Bold))
        phone1_label.setStyleSheet("color: #1976d2; min-width: 80px;")
        self.phone1_field = QLineEdit()
        self.phone1_field.setFont(QFont("Calibri", 13))
        self.phone1_field.setReadOnly(False)
        self.phone1_field.setFixedWidth(150)
        self.phone1_field.setStyleSheet("""
            QLineEdit {
                border: 2px solid #4CAF50;
                border-radius: 6px;
                padding: 6px;
                background-color: #ffffff;
            }
            QLineEdit:focus {
                border: 2px solid #2196F3;
                background-color: #f0f8ff;
            }
        """)

        # الهاتف 2
        phone2_label = QLabel("الهاتف 2:")
        phone2_label.setFont(QFont("Calibri", 13, QFont.Bold))
        phone2_label.setStyleSheet("color: #1976d2; min-width: 80px;")
        self.phone2_field = QLineEdit()
        self.phone2_field.setFont(QFont("Calibri", 13))
        self.phone2_field.setReadOnly(False)
        self.phone2_field.setFixedWidth(150)
        self.phone2_field.setStyleSheet("""
            QLineEdit {
                border: 2px solid #4CAF50;
                border-radius: 6px;
                padding: 6px;
                background-color: #ffffff;
            }
            QLineEdit:focus {
                border: 2px solid #2196F3;
                background-color: #f0f8ff;
            }
        """)

        contact_row_layout.addWidget(phone1_label)
        contact_row_layout.addWidget(self.phone1_field)
        contact_row_layout.addWidget(phone2_label)
        contact_row_layout.addWidget(self.phone2_field)
        contact_row_layout.addStretch()

        # إضافة الصف إلى التخطيط الرئيسي
        self.contact_layout.addLayout(contact_row_layout)

        # الصف الثاني: حقل الملاحظات
        notes_row_layout = QHBoxLayout()
        notes_row_layout.setSpacing(15)

        notes_label = QLabel("ملاحظات:")
        notes_label.setFont(QFont("Calibri", 13, QFont.Bold))
        notes_label.setStyleSheet("color: #1976d2; min-width: 80px;")
        notes_label.setAlignment(Qt.AlignRight | Qt.AlignTop)

        self.notes_field = QTextEdit()
        self.notes_field.setFont(QFont("Calibri", 13))
        self.notes_field.setReadOnly(False)
        self.notes_field.setFixedHeight(80)
        self.notes_field.setMinimumWidth(400)
        self.notes_field.setStyleSheet("""
            QTextEdit {
                border: 2px solid #4CAF50;
                border-radius: 6px;
                padding: 6px;
                background-color: #ffffff;
            }
            QTextEdit:focus {
                border: 2px solid #2196F3;
                background-color: #f0f8ff;
            }
        """)

        notes_row_layout.addWidget(notes_label)
        notes_row_layout.addWidget(self.notes_field)
        notes_row_layout.addStretch()

        # إضافة الصف إلى التخطيط الرئيسي
        self.contact_layout.addLayout(notes_row_layout)

        # إضافة أزرار الحفظ والتحديث
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        save_button = self.create_styled_button("💾 حفظ التغييرات", "#4CAF50")
        save_button.clicked.connect(self.save_contact_info)

        refresh_button = self.create_styled_button("🔄 تحديث البيانات", "#2196F3")
        refresh_button.clicked.connect(self.load_student_data)

        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(refresh_button)
        buttons_layout.addStretch()

        # إضافة التخطيط الأفقي إلى تخطيط الاتصال
        self.contact_layout.addLayout(buttons_layout)

        scroll_layout.addWidget(self.contact_frame)

        # إعداد منطقة التمرير
        scroll_area.setWidget(scroll_widget)

        # إضافة منطقة التمرير إلى التخطيط الرئيسي
        main_layout.addWidget(scroll_area)

    def connect_to_database(self):
        """الاتصال بقاعدة البيانات"""
        try:
            if self.external_db:
                self.db = self.external_db
                print("تم استخدام قاعدة البيانات الخارجية")
            else:
                self.db = QSqlDatabase.addDatabase("QSQLITE", "student_card_connection")
                self.db.setDatabaseName("data.db")

                if not self.db.open():
                    QMessageBox.critical(self, "خطأ في قاعدة البيانات",
                                       f"فشل في الاتصال بقاعدة البيانات: {self.db.lastError().text()}")
                    return False
                print("تم الاتصال بقاعدة البيانات المحلية")
            return True
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في الاتصال بقاعدة البيانات: {str(e)}")
            return False

    def load_current_academic_year(self):
        """تحميل السنة الدراسية الحالية"""
        try:
            if self.external_academic_year:
                self.current_academic_year = self.external_academic_year
                print(f"تم استخدام السنة الدراسية الخارجية: {self.current_academic_year}")
            else:
                # محاولة تحميل السنة الدراسية من قاعدة البيانات
                query = QSqlQuery(self.db)
                query.exec_("SELECT academic_year FROM settings LIMIT 1")
                if query.next():
                    self.current_academic_year = query.value(0)
                    print(f"تم تحميل السنة الدراسية: {self.current_academic_year}")
                else:
                    # استخدام السنة الحالية كافتراضي
                    current_year = datetime.now().year
                    self.current_academic_year = f"{current_year}-{current_year + 1}"
                    print(f"تم استخدام السنة الدراسية الافتراضية: {self.current_academic_year}")
        except Exception as e:
            print(f"خطأ في تحميل السنة الدراسية: {e}")
            current_year = datetime.now().year
            self.current_academic_year = f"{current_year}-{current_year + 1}"

    def load_first_record(self):
        """تحميل أول سجل من قاعدة البيانات"""
        try:
            result_id = None
            base_query = """
                SELECT DISTINCT s.الرمز
                FROM students s
                WHERE s.السنة_الدراسية = ?
                ORDER BY s.الرمز ASC
                LIMIT 1
            """

            query = QSqlQuery(self.db)
            query.prepare(base_query)
            query.addBindValue(self.current_academic_year)

            if query.exec_():
                if query.next():
                    result_id = query.value(0)
                    print(f"تم العثور على أول سجل: {result_id}")
                else:
                    print("لا توجد سجلات في قاعدة البيانات")
                    return
            else:
                print(f"خطأ في تنفيذ الاستعلام: {query.lastError().text()}")
                return

            if result_id:
                self.current_record_id = result_id
                self.load_student_data()

        except Exception as e:
            print(f"خطأ في تحميل أول سجل: {e}")
            traceback.print_exc()

    def load_student_data(self):
        """تحميل بيانات الطالب"""
        try:
            print(f"التشخيص load_student_data: current_record_id = {self.current_record_id}")
            print(f"التشخيص load_student_data: current_academic_year = {self.current_academic_year}")
            
            if not self.current_record_id:
                print("التشخيص: current_record_id فارغ - لن يتم تحميل البيانات")
                return

            # تحميل البيانات الأساسية
            query = QSqlQuery(self.db)
            query.prepare("""
                SELECT الرمز, الاسم_والنسب, النوع, تاريخ_الازدياد, مكان_الازدياد,
                       السنة_الدراسية, المستوى, القسم, رت, الهاتف_1, الهاتف_2, ملاحظات
                FROM students
                WHERE الرمز = ? AND السنة_الدراسية = ?
            """)
            query.addBindValue(self.current_record_id)
            query.addBindValue(self.current_academic_year)
            
            print(f"التشخيص: تنفيذ الاستعلام للرمز: {self.current_record_id}, السنة: {self.current_academic_year}")

            if query.exec_():
                print("التشخيص: تم تنفيذ الاستعلام بنجاح")
                if query.next():
                    print("التشخيص: تم العثور على بيانات الطالب")
                    # تحديث الحقول
                    self.code_display.setText(query.value(0) or "")
                    self.name_display.setText(query.value(1) or "")
                    self.gender_display.setText(query.value(2) or "")
                    self.birth_date_display.setText(query.value(3) or "")
                    self.birth_place_display.setText(query.value(4) or "")
                    self.school_year_display.setText(query.value(5) or "")
                    self.level_display.setText(query.value(6) or "")
                    self.class_display.setText(query.value(7) or "")
                    self.rt_display.setText(query.value(8) or "")
                    self.phone1_field.setText(query.value(9) or "")
                    self.phone2_field.setText(query.value(10) or "")
                    self.notes_field.setPlainText(query.value(11) or "")

                    # تحديث حقول البحث
                    self.name_field.setText(f"الاسم والنسب: {query.value(1) or ''}")
                    self.code_field.setText(f"الرمز: {query.value(0) or ''}")
                    
                    print(f"التشخيص: تم تحديث البيانات - الاسم: {query.value(1)}")
                else:
                    print(f"التشخيص: لم يتم العثور على بيانات للطالب: {self.current_record_id}")
                    # إضافة استعلام تشخيصي للتحقق من وجود البيانات
                    debug_query = QSqlQuery(self.db)
                    debug_query.prepare("SELECT COUNT(*) FROM students WHERE الرمز = ?")
                    debug_query.addBindValue(self.current_record_id)
                    if debug_query.exec_() and debug_query.next():
                        count = debug_query.value(0)
                        print(f"التشخيص: عدد السجلات للرمز {self.current_record_id}: {count}")
                    
                    # التحقق من السنة الدراسية
                    year_query = QSqlQuery(self.db)
                    year_query.prepare("SELECT DISTINCT السنة_الدراسية FROM students WHERE الرمز = ?")
                    year_query.addBindValue(self.current_record_id)
                    if year_query.exec_():
                        years = []
                        while year_query.next():
                            years.append(year_query.value(0))
                        print(f"التشخيص: السنوات الدراسية المتاحة للرمز {self.current_record_id}: {years}")
            else:
                print(f"التشخيص: فشل في تنفيذ الاستعلام: {query.lastError().text()}")

        except Exception as e:
            print(f"خطأ في تحميل بيانات الطالب: {e}")
            import traceback
            traceback.print_exc()

    def save_contact_info(self):
        """حفظ معلومات الاتصال والملاحظات"""
        try:
            if not self.current_record_id:
                QMessageBox.warning(self, "تنبيه", "لا يوجد طالب محدد للحفظ")
                return

            query = QSqlQuery(self.db)
            query.prepare("""
                UPDATE students
                SET الهاتف_1 = ?, الهاتف_2 = ?, ملاحظات = ?
                WHERE الرمز = ? AND السنة_الدراسية = ?
            """)
            query.addBindValue(self.phone1_field.text())
            query.addBindValue(self.phone2_field.text())
            query.addBindValue(self.notes_field.toPlainText())
            query.addBindValue(self.current_record_id)
            query.addBindValue(self.current_academic_year)

            if query.exec_():
                QMessageBox.information(self, "نجح", "تم حفظ التغييرات بنجاح")
            else:
                QMessageBox.critical(self, "خطأ", f"فشل في حفظ التغييرات: {query.lastError().text()}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ البيانات: {str(e)}")

    def create_styled_button(self, text, color="#2196F3"):
        """إنشاء زر منسق بنفس تصميم sub262_window"""
        button = QPushButton(text)
        button.setFont(QFont("Calibri", 13, QFont.Bold))
        button.setMinimumHeight(35)
        button.setMinimumWidth(120)

        dark_color = self.darken_color(color, 30)
        light_color = self.lighten_color(color, 20)

        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color},
                    stop: 1 {dark_color}
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 15px;
            }}
            QPushButton:hover {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {light_color},
                    stop: 1 {color}
                );
            }}
            QPushButton:pressed {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {dark_color},
                    stop: 1 {self.darken_color(dark_color, 20)}
                );
            }}
        """)
        return button

    def get_groupbox_style(self):
        """نمط المجموعات بنفس تصميم sub262_window"""
        return """
            QGroupBox {
                color: #1976d2;
                border: 2px solid #1976d2;
                border-radius: 10px;
                padding-top: 15px;
                margin-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: white;
                color: #1976d2;
            }
        """

    def get_table_style(self):
        """نمط الجداول بنفس تصميم sub262_window"""
        return """
            QTableWidget {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                gridline-color: #f0f0f0;
                selection-background-color: #e3f2fd;
            }

            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
                font-size: 16px;
            }

            QTableWidget::item:selected {
                background-color: #bbdefb;
                color: #1976d2;
            }

            QHeaderView::section {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #1976d2,
                    stop: 1 #1565c0
                );
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """

    def darken_color(self, color, amount):
        """تغميق اللون"""
        color = color.lstrip('#')
        rgb = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
        darkened = tuple(max(0, c - amount) for c in rgb)
        return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"

    def lighten_color(self, color, amount):
        """تفتيح اللون"""
        color = color.lstrip('#')
        rgb = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
        lightened = tuple(min(255, c + amount) for c in rgb)
        return f"#{lightened[0]:02x}{lightened[1]:02x}{lightened[2]:02x}"

    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        try:
            # إغلاق اتصال قاعدة البيانات إذا كان محلياً
            if hasattr(self, 'db') and self.db and not self.external_db:
                self.db.close()
                print("تم إغلاق اتصال قاعدة البيانات")
        except Exception as e:
            print(f"خطأ في إغلاق قاعدة البيانات: {e}")

        # قبول حدث الإغلاق
        event.accept()


def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    # التحقق من توفر قاعدة البيانات قبل تشغيل التطبيق
    if not check_database_availability():
        print("تعذر تشغيل التطبيق بسبب مشاكل في قاعدة البيانات")
        return

    app = QApplication(sys.argv)

    # تعيين اتجاه التطبيق من اليمين إلى اليسار
    app.setLayoutDirection(Qt.RightToLeft)

    # إنشاء النافذة الرئيسية
    window = StudentCardWindow()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    # تشغيل التطبيق للاختبار
    main()
