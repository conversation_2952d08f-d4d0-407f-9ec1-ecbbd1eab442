#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة قاعدة البيانات مع sub4_window
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon

def test_database_fix():
    """اختبار إصلاح مشكلة قاعدة البيانات"""
    print("🔧 اختبار إصلاح مشكلة قاعدة البيانات مع sub4_window")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    
    try:
        from main_window import MainWindow
        print("✅ تم استيراد MainWindow")
        
        main_window = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # فحص إصلاح قاعدة البيانات
        def check_database_fix():
            print("\n🔍 فحص إصلاح قاعدة البيانات:")
            
            # التحقق من وجود self.db
            if hasattr(main_window, 'db'):
                print("   ✅ تم العثور على self.db")
                print(f"   📄 نوع الاتصال: {type(main_window.db)}")
                
                # اختبار الاتصال
                try:
                    cursor = main_window.db.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                    tables = cursor.fetchall()
                    print(f"   📋 عدد الجداول: {len(tables)}")
                    print("   ✅ اتصال قاعدة البيانات يعمل بشكل صحيح")
                except Exception as e:
                    print(f"   ❌ خطأ في اختبار الاتصال: {e}")
            else:
                print("   ❌ لم يتم العثور على self.db")
            
            # التحقق من وجود self.db_path
            if hasattr(main_window, 'db_path'):
                print("   ✅ تم العثور على self.db_path")
                print(f"   📁 مسار قاعدة البيانات: {main_window.db_path}")
                
                # التحقق من وجود الملف
                if os.path.exists(main_window.db_path):
                    print("   ✅ ملف قاعدة البيانات موجود")
                    file_size = os.path.getsize(main_window.db_path) / 1024  # KB
                    print(f"   📊 حجم الملف: {file_size:.2f} KB")
                else:
                    print("   ❌ ملف قاعدة البيانات غير موجود")
            else:
                print("   ❌ لم يتم العثور على self.db_path")
            
            # فحص تبويب اللوائح والأقسام
            print("\n🔍 فحص تبويب اللوائح والأقسام:")
            
            # البحث عن التبويب
            lists_sections_found = False
            lists_sections_index = -1
            
            tab_count = main_window.tabWidget.count()
            print(f"   📋 عدد التبويبات: {tab_count}")
            
            for i in range(tab_count):
                tab_text = main_window.tabWidget.tabText(i)
                tab_data = main_window.tabWidget.tabData(i)
                
                if tab_text == "اللوائح والأقسام" or tab_data == "lists_sections":
                    lists_sections_found = True
                    lists_sections_index = i
                    print(f"   ✅ تم العثور على تبويب اللوائح والأقسام في الفهرس {i}")
                    
                    # التحقق من حالة التبويب
                    is_enabled = main_window.tabWidget.isTabEnabled(i)
                    print(f"   📌 حالة التفعيل: {'مفعل' if is_enabled else 'غير مفعل'}")
                    break
            
            if not lists_sections_found:
                print("   ❌ لم يتم العثور على تبويب اللوائح والأقسام")
                return
            
            # التحقق من النافذة المرتبطة
            print("\n🔍 فحص النافذة المرتبطة:")
            if "lists_sections" in main_window.windows:
                print("   ✅ النافذة المرتبطة موجودة في قاموس النوافذ")
                window = main_window.windows["lists_sections"]
                print(f"   📄 نوع النافذة: {type(window).__name__}")
                
                # التحقق من أنها ليست PlaceholderWindow
                if "PlaceholderWindow" in type(window).__name__:
                    print("   ⚠️ النافذة هي PlaceholderWindow (نافذة مؤقتة)")
                    print("   💡 هذا يعني أن sub4_window لم يتم تحميله بشكل صحيح")
                else:
                    print("   ✅ النافذة من النوع الصحيح (Sub4Window)")
                    
            else:
                print("   ❌ النافذة المرتبطة غير موجودة في قاموس النوافذ")
            
            # اختبار النقر على التبويب
            print("\n🎯 اختبار النقر على التبويب:")
            if lists_sections_index >= 0:
                print(f"   محاولة النقر على التبويب {lists_sections_index}...")
                
                # محاكاة النقر على التبويب
                main_window.tabWidget.setCurrentIndex(lists_sections_index)
                current_index = main_window.tabWidget.currentIndex()
                print(f"   الفهرس الحالي بعد النقر: {current_index}")
                
                if current_index == lists_sections_index:
                    print("   ✅ تم التنقل للتبويب بنجاح!")
                else:
                    print("   ❌ فشل في التنقل للتبويب")
        
        # فحص الإصلاح بعد ثانية واحدة
        QTimer.singleShot(1000, check_database_fix)
        
        main_window.show()
        main_window.setWindowTitle("اختبار إصلاح قاعدة البيانات - sub4_window")
        
        print("\n📋 تعليمات الاختبار:")
        print("1. راقب رسائل التشخيص أعلاه")
        print("2. ابحث عن تبويب 'اللوائح والأقسام'")
        print("3. انقر على التبويب")
        print("4. تحقق من عدم ظهور رسائل خطأ")
        print("5. تأكد من أن النافذة ليست PlaceholderWindow")
        
        print("\n🔧 الإصلاح المطبق:")
        print("   ✅ إضافة self.db = sqlite3.connect(self.db_path)")
        print("   ✅ إنشاء اتصال قاعدة البيانات للنوافذ")
        print("   ✅ الاحتفاظ بـ self.db_path للمسار")
        print("   ✅ رسائل تشخيصية مفصلة")
        
        print("\n📊 النتائج المتوقعة:")
        print("   ✅ لا توجد رسائل خطأ عن 'db' غير موجود")
        print("   ✅ تبويب اللوائح والأقسام مفعل")
        print("   ✅ النافذة من نوع Sub4Window وليس PlaceholderWindow")
        print("   ✅ اتصال قاعدة البيانات يعمل")
        
        print("\n⚠️ ملاحظات:")
        print("   • إذا كانت النافذة PlaceholderWindow، فهناك مشكلة في sub4_window.py")
        print("   • تأكد من وجود ملف sub4_window.py وكلاس Sub4Window")
        print("   • تحقق من معاملات دالة التهيئة في Sub4Window")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return 1

def show_fix_details():
    """عرض تفاصيل الإصلاح"""
    print("\n" + "=" * 60)
    print("🔧 تفاصيل إصلاح مشكلة قاعدة البيانات")
    print("=" * 60)
    
    fix_details = [
        ("المشكلة الأصلية", "'MainWindow' object has no attribute 'db'"),
        ("السبب", "sub4_window يحتاج معامل db لكن MainWindow لا يحتوي عليه"),
        ("الحل", "إضافة self.db = sqlite3.connect(self.db_path)"),
        ("الموقع", "في دالة __init__ بعد التحقق من وجود قاعدة البيانات"),
        ("الفائدة", "جميع النوافذ يمكنها الآن استخدام self.db")
    ]
    
    print("\n🔧 تفاصيل الإصلاح:")
    for title, detail in fix_details:
        print(f"   {title}: {detail}")
    
    print("\n📝 الكود المضاف:")
    print("   self.db = sqlite3.connect(self.db_path)")
    print("   print('INFO: تم إنشاء اتصال قاعدة البيانات للنوافذ')")

if __name__ == "__main__":
    print("🔧 مرحباً بك في اختبار إصلاح قاعدة البيانات!")
    
    # عرض تفاصيل الإصلاح
    show_fix_details()
    
    print("\n" + "=" * 60)
    print("🚀 بدء تشغيل الاختبار...")
    print("=" * 60)
    
    # تشغيل الاختبار
    exit_code = test_database_fix()
    
    print(f"\n🔚 انتهى الاختبار بكود الخروج: {exit_code}")
    sys.exit(exit_code)
