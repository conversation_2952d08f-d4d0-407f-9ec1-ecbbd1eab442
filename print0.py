"""
print0.py - وحدة إنشاء تقرير الإحصائيات العامة للمؤسسة

هذا الملف مسؤول عن إنشاء تقارير PDF للإحصائيات العامة للمؤسسة.
يتم استدعاؤه من ملف sub5_window.py عند الضغط على زر "طباعة التقرير".

تاريخ الإنشاء: 2023-10-01
آخر تحديث: 2023-10-15
"""

import os
import sqlite3
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.colors import HexColor
import arabic_reshaper
from bidi.algorithm import get_display

class StatisticsReport:
    """فئة مسؤولة عن إنشاء تقرير الإحصائيات العامة للمؤسسة"""

    def __init__(self, db_path="data.db"):
        """تهيئة الفئة مع مسار قاعدة البيانات"""
        self.db_path = db_path

    def get_academic_year(self):
        """استخراج السنة الدراسية الحالية من جدول بيانات_المؤسسة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # استخراج السنة الدراسية من جدول بيانات_المؤسسة
            cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            conn.close()

            if result and result[0]:
                return result[0]
            return ""

        except Exception as e:
            print(f"خطأ في استخراج السنة الدراسية: {e}")
            return ""

    def get_statistics(self, academic_year=""):
        """استخراج البيانات الإحصائية من قاعدة البيانات"""
        stats = {
            "levels": 0,
            "sections": 0,
            "students": 0,
            "males": 0,
            "females": 0,
            "age_groups": {}  # سيتم تخزين كل عمر كمفتاح مع عدد التلاميذ كقيمة
        }

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # استخراج الإحصائيات العامة
            year_condition = " WHERE السنة_الدراسية = ?" if academic_year else ""
            params = [academic_year] if academic_year else []

            # استعلامات الإحصائيات الأساسية
            cursor.execute(f"SELECT COUNT(DISTINCT المستوى) FROM البنية_التربوية{year_condition}", params)
            stats["levels"] = cursor.fetchone()[0] or 0

            cursor.execute(f"SELECT COUNT(DISTINCT القسم) FROM البنية_التربوية{year_condition}", params)
            stats["sections"] = cursor.fetchone()[0] or 0

            cursor.execute(f"SELECT SUM(مجموع_التلاميذ) FROM البنية_التربوية{year_condition}", params)
            stats["students"] = cursor.fetchone()[0] or 0

            # استعلام النوع
            try:
                gender_query = """
                    SELECT النوع, COUNT(*)
                    FROM السجل_العام s
                    JOIN اللوائح l ON s.الرمز = l.الرمز
                """
                if academic_year:
                    gender_query += " WHERE l.السنة_الدراسية = ?"
                    gender_query += " GROUP BY النوع"
                    cursor.execute(gender_query, [academic_year])
                else:
                    gender_query += " GROUP BY النوع"
                    cursor.execute(gender_query)

                gender_stats = cursor.fetchall()
                for gender, count in gender_stats:
                    if gender and gender.strip().lower() in ['ذكر', 'ذ', 'm', 'male']:
                        stats["males"] += count
                    elif gender and gender.strip().lower() in ['أنثى', 'ا', 'f', 'female']:
                        stats["females"] += count

            except sqlite3.Error as e:
                print(f"خطأ في استرجاع إحصائيات النوع: {e}")

            conn.close()

        except Exception as e:
            print(f"خطأ في استرجاع الإحصائيات: {e}")

        return stats

    def calculate_age_stats(self, academic_year, reference_date):
        """حساب إحصائيات الأعمار بناءً على تاريخ مرجعي محدد"""
        age_stats = {}  # قاموس لتخزين كل عمر مع عدد التلاميذ

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            age_query = """
                SELECT تاريخ_الازدياد
                FROM السجل_العام s
                JOIN اللوائح l ON s.الرمز = l.الرمز
                WHERE l.السنة_الدراسية = ?
            """

            cursor.execute(age_query, [academic_year])
            birth_dates = cursor.fetchall()

            for (birth_date,) in birth_dates:
                try:
                    if isinstance(birth_date, str):
                        birth_date = birth_date.strip()

                        if '/' in birth_date:
                            day, month, year = map(int, birth_date.split('/'))
                        elif '-' in birth_date:
                            year, month, day = map(int, birth_date.split('-'))
                        else:
                            year = int(birth_date)
                            month, day = 1, 1

                        if year < 100:
                            year += 2000 if year < 30 else 1900

                        try:
                            birth_date = datetime(year, month, day)
                        except ValueError as e:
                            if "day is out of range for month" in str(e):
                                if month == 2:
                                    day = 29 if year % 4 == 0 and (year % 100 != 0 or year % 400 == 0) else 28
                                elif month in [4, 6, 9, 11]:
                                    day = 30
                                else:
                                    day = 31
                                birth_date = datetime(year, month, day)
                            else:
                                raise
                    else:
                        birth_date = datetime(int(birth_date), 1, 1)

                    # حساب العمر
                    age = reference_date.year - birth_date.year
                    if reference_date.month < birth_date.month or \
                       (reference_date.month == birth_date.month and \
                        reference_date.day < birth_date.day):
                        age -= 1

                    # إضافة العمر إلى القاموس مع زيادة العداد
                    age_stats[age] = age_stats.get(age, 0) + 1

                except Exception as e:
                    print(f"خطأ في معالجة تاريخ الميلاد: {birth_date}, {str(e)}")
                    continue

            conn.close()

        except Exception as e:
            print(f"خطأ في حساب إحصائيات الأعمار: {str(e)}")

        return age_stats

    def get_school_info(self):
        """استخراج بيانات المؤسسة من قاعدة البيانات"""
        school_info = {}
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT * FROM بيانات_المؤسسة LIMIT 1")
            columns = [column[0] for column in cursor.description]
            result = cursor.fetchone()

            if result:
                school_info = {columns[i]: result[i] for i in range(len(columns))}

                # إضافة مسار الشعار إذا كان موجوداً
                school_info["logo_path"] = school_info.get("ImagePath1", "")

            conn.close()

        except Exception as e:
            print(f"خطأ في استخراج بيانات المؤسسة: {e}")

        return school_info

    def create_pdf_report(self, filepath, stats_data, reference_date):
        """إنشاء ملف PDF للتقرير

        المعلمات:
            filepath (str): مسار ملف PDF الذي سيتم إنشاؤه
            stats_data (dict): بيانات الإحصائيات التي سيتم عرضها في التقرير
            reference_date (datetime): تاريخ المرجع المستخدم لحساب أعمار التلاميذ

        العائد:
            bool: True إذا تم إنشاء التقرير بنجاح، False في حالة حدوث خطأ
        """
        try:
            # محاولة تسجيل الخط العربي من عدة مصادر
            font_paths = [
                os.path.join(os.path.dirname(__file__), "fonts", "arial.ttf"),
                r"C:\Windows\Fonts\arial.ttf",
                r"C:\Windows\Fonts\arialbd.ttf",
                r"C:\Windows\Fonts\tahoma.ttf"
            ]

            font_registered = False
            for font_path in font_paths:
                try:
                    if os.path.exists(font_path):
                        pdfmetrics.registerFont(TTFont("Arabic", font_path))
                        font_registered = True
                        break
                except:
                    continue

            # إنشاء ملف PDF
            c = canvas.Canvas(filepath, pagesize=A4)
            width, height = A4

            def reshape_arabic(text):
                return get_display(arabic_reshaper.reshape(str(text)))

            # استخراج البيانات
            school_info = self.get_school_info()

            # تعيين الخط المناسب
            font_name = "Arabic" if font_registered else "Helvetica"

            # رسم شعار المؤسسة إذا وجد
            logo_path = school_info.get("logo_path")
            if (logo_path and os.path.exists(logo_path)):
                try:
                    # ضبط موقع وحجم الشعار
                    logo_width = 250  # عرض أكبر للشعار
                    logo_height = 170  # ارتفاع أكبر للشعار
                    logo_x = width/2 - logo_width/2  # توسيط الشعار
                    logo_y = height - 130  # رفع موقع الشعار قليلاً

                    # رسم الشعار مع المحافظة على نسب أبعاده
                    c.drawImage(
                        logo_path,
                        logo_x,
                        logo_y,
                        width=logo_width,
                        height=logo_height,
                        mask='auto',
                        preserveAspectRatio=True
                    )

                    # تعديل موقع بداية العنوان ليكون تحت الشعار
                    y = height - 150
                except Exception as e:
                    print(f"خطأ في رسم الشعار: {e}")
                    y = height - 50  # في حالة فشل رسم الشعار، نبدأ من الأعلى
            else:
                y = height - 50  # في حالة عدم وجود شعار، نبدأ من الأعلى

            # تحريك العنوان والمحتوى للأسفل قليلاً
            # إضافة خلفية مميزة للعنوان
            c.setFillColor(HexColor('#1976D2'))
            c.roundRect(80, y-55, width-160, 45, 10, fill=True)

            # إضافة ظل للعنوان
            c.setFillColor(HexColor('#0D47A1'))
            c.roundRect(83, y-52, width-160, 45, 10, fill=True)

            # كتابة العنوان
            c.setFillColor(HexColor('#FFFFFF'))
            c.setFont(font_name, 26)
            c.drawCentredString(width/2, y-35, reshape_arabic("تقرير إحصائي عام"))

            # تحديث موقع بداية معلومات المؤسسة
            y = y - 100

            # معلومات المؤسسة في صندوق منفصل
            y = height - 100
            c.setFillColor(HexColor('#FFFFFF'))
            c.setStrokeColor(HexColor('#CCCCCC'))
            c.rect(100, y-50, width-200, 50, fill=False)

            c.setFillColor(HexColor('#000000'))
            c.setFont(font_name, 14)
            info_y = y - 20
            for key, value in [
                ("المؤسسة", school_info.get('المؤسسة', '')),
                ("السنة الدراسية", school_info.get('السنة_الدراسية', ''))
            ]:
                text = f"{key}: {value}"
                c.drawRightString(width-200, info_y, reshape_arabic(text))
                info_y -= 20

            # الإحصائيات العامة في بطاقات ملونة
            y = y - 150
            stats_cards = [
                ("إجمالي التلاميذ", str(stats_data["students"]), "#2196F3"),
                ("عدد المستويات", str(stats_data["levels"]), "#4CAF50"),
                ("عدد الأقسام", str(stats_data["sections"]), "#FF9800"),
                ("معدل التلاميذ/قسم",
                 str(int(stats_data["students"] / stats_data["sections"]) if stats_data["sections"] > 0 else 0),
                 "#9C27B0")
            ]

            # رسم البطاقات
            card_width = (width-150)/4
            for i, (label, value, color) in enumerate(stats_cards):
                x = 50 + (i * (card_width + 10))

                # رسم خلفية البطاقة
                c.setFillColor(HexColor('#FFFFFF'))
                c.setStrokeColor(HexColor(color))
                c.rect(x, y-80, card_width, 80, fill=True)

                # رسم القيمة
                c.setFillColor(HexColor(color))
                c.setFont(font_name, 20)
                c.drawCentredString(x + card_width/2, y-30, reshape_arabic(value))

                # رسم العنوان
                c.setFont(font_name, 12)
                c.drawCentredString(x + card_width/2, y-60, reshape_arabic(label))

                # رسم خط ملون أسفل البطاقة
                c.setFillColor(HexColor(color))
                c.rect(x, y-80, card_width, 4, fill=True)

            # إحصائيات النوع
            y = y - 100
            total_gender = stats_data["males"] + stats_data["females"]
            female_percentage = round((stats_data["females"] / total_gender) * 100, 1) if total_gender > 0 else 0

            gender_cards = [
                ("عدد الذكور", str(stats_data["males"]), "#3F51B5"),
                ("عدد الإناث", str(stats_data["females"]), "#E91E63"),
                ("المجموع", str(total_gender), "#009688"),
                ("نسبة الإناث", f"{female_percentage}%", "#FFC107")
            ]

            # رسم بطاقات النوع
            for i, (label, value, color) in enumerate(gender_cards):
                x = 50 + (i * (card_width + 10))

                # رسم خلفية البطاقة
                c.setFillColor(HexColor('#FFFFFF'))
                c.setStrokeColor(HexColor(color))
                c.rect(x, y-80, card_width, 80, fill=True)

                # رسم القيمة
                c.setFillColor(HexColor(color))
                c.setFont(font_name, 20)
                c.drawCentredString(x + card_width/2, y-30, reshape_arabic(value))

                # رسم العنوان
                c.setFont(font_name, 12)
                c.drawCentredString(x + card_width/2, y-60, reshape_arabic(label))

                # رسم خط ملون أسفل البطاقة
                c.setFillColor(HexColor(color))
                c.rect(x, y-80, card_width, 4, fill=True)

            # إضافة إحصائيات الأعمار
            y = y - 130

            # عنوان إحصائيات العمر
            c.setFillColor(HexColor('#1976D2'))
            c.rect(50, y, width-100, 30, fill=True)
            c.setFillColor(HexColor('#FFFFFF'))
            c.setFont(font_name, 23)
            c.drawCentredString(width/2, y+8, reshape_arabic("توزيع التلاميذ حسب السن"))

            # تحضير بيانات الأعمار مرتبة تصاعدياً
            sorted_ages = sorted(stats_data["age_groups"].items())

            # حساب عدد الأعمدة في كل صف
            items_per_row = 5
            rows = (len(sorted_ages) + items_per_row - 1) // items_per_row

            card_colors = ["#E91E63", "#9C27B0", "#673AB7", "#3F51B5", "#2196F3"]
            y -= 30

            # رسم بطاقات الأعمار
            for row in range(rows):
                items_in_row = min(items_per_row, len(sorted_ages) - row * items_per_row)
                card_width = (width-150) / items_per_row

                for i in range(items_in_row):
                    index = row * items_per_row + i
                    age, count = sorted_ages[index]
                    color = card_colors[i % len(card_colors)]
                    x = 50 + (i * (card_width + 10))

                    # رسم خلفية البطاقة
                    c.setFillColor(HexColor('#FFFFFF'))
                    c.setStrokeColor(HexColor(color))
                    c.rect(x, y-80, card_width, 80, fill=True)

                    # رسم القيمة (عدد التلاميذ)
                    c.setFillColor(HexColor(color))
                    c.setFont(font_name, 20)
                    c.drawCentredString(x + card_width/2, y-30, reshape_arabic(str(count)))

                    # رسم العنوان (العمر)
                    c.setFont(font_name, 12)
                    c.drawCentredString(x + card_width/2, y-60, reshape_arabic(f"{age} سنة"))

                    # رسم خط ملون أسفل البطاقة
                    c.setFillColor(HexColor(color))
                    c.rect(x, y-80, card_width, 4, fill=True)

                y -= 100  # الانتقال للصف التالي

            # تاريخ المرجع وتاريخ إنشاء التقرير
            c.setFillColor(HexColor('#666666'))
            c.setFont(font_name, 10)
            current_date = datetime.now().strftime("%Y/%m/%d %H:%M")
            reference_date_str = reference_date.strftime("%Y/%m/%d")
            c.drawString(50, 40, reshape_arabic(f"تاريخ المرجع لحساب الأعمار: {reference_date_str}"))
            c.drawString(50, 25, reshape_arabic(f"تاريخ إنشاء التقرير: {current_date}"))

            c.save()
            return True

        except Exception as e:
            print(f"خطأ في إنشاء التقرير PDF: {str(e)}")
            return False
