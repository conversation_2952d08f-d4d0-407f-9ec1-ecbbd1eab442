#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف اختبار بسيط للقائمة المنسدلة في main_window.py
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

def test_main_window():
    """اختبار النافذة الرئيسية والقائمة المنسدلة"""
    print("=" * 60)
    print("🧪 اختبار القائمة المنسدلة في النافذة الرئيسية")
    print("=" * 60)
    
    # إنشاء تطبيق Qt
    app = QApplication(sys.argv)
    app.setApplicationName("اختبار القائمة المنسدلة")
    
    # تعيين أيقونة التطبيق إذا كانت متوفرة
    if os.path.exists("01.ico"):
        app.setWindowIcon(QIcon("01.ico"))
    
    try:
        print("📥 استيراد main_window...")
        from main_window import MainWindow
        print("✅ تم استيراد main_window بنجاح")
        
        print("\n🏗️ إنشاء النافذة الرئيسية...")
        main_window = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # التحقق من وجود القائمة المنسدلة
        if hasattr(main_window, 'institution_dropdown_button'):
            print("✅ تم العثور على زر القائمة المنسدلة")
            button = main_window.institution_dropdown_button
            print(f"   📏 حجم الزر: {button.size()}")
            print(f"   👁️ مرئي: {button.isVisible()}")
            print(f"   📝 النص: {button.text()}")
        else:
            print("❌ لم يتم العثور على زر القائمة المنسدلة")
            
        if hasattr(main_window, 'institution_dropdown_widget'):
            print("✅ تم العثور على ويدجيت القائمة المنسدلة")
            widget = main_window.institution_dropdown_widget
            print(f"   📏 حجم الويدجيت: {widget.size()}")
            print(f"   👁️ مرئي: {widget.isVisible()}")
        else:
            print("❌ لم يتم العثور على ويدجيت القائمة المنسدلة")
            
        if hasattr(main_window, 'institution_dropdown_menu'):
            print("✅ تم العثور على القائمة المنسدلة")
            menu = main_window.institution_dropdown_menu
            actions = menu.actions()
            print(f"   📋 عدد العناصر: {len(actions)}")
            for i, action in enumerate(actions):
                if action.isSeparator():
                    print(f"   {i+1}. [فاصل]")
                else:
                    print(f"   {i+1}. {action.text()}")
        else:
            print("❌ لم يتم العثور على القائمة المنسدلة")
        
        print("\n🖥️ عرض النافذة...")
        main_window.show()
        main_window.setWindowTitle("اختبار القائمة المنسدلة - بيانات المؤسسة")
        
        print("\n🎯 تعليمات الاختبار:")
        print("   1. ابحث عن زر 'بيانات المؤسسة ▼' في أعلى النافذة")
        print("   2. إذا لم تجده، تحقق من وحدة التحكم للرسائل التشخيصية")
        print("   3. إذا وجدته، انقر عليه لفتح القائمة")
        print("   4. جرب النقر على العناصر المختلفة")
        
        print("\n🚀 تشغيل التطبيق...")
        return app.exec_()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد main_window: {e}")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    print("🧪 بدء اختبار القائمة المنسدلة...")
    exit_code = test_main_window()
    print(f"\n🔚 انتهى الاختبار بكود الخروج: {exit_code}")
    sys.exit(exit_code)
