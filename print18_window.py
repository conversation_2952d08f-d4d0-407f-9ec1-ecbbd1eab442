"""
ملف طباعة المهام والنشاطات - مستخرج من print_test.py
يستخدم طريقة الطباعة المحسنة مثل monthly_duties_window.py
حجم الورق: 75mm عرض، ارتفاع متغير حسب المحتوى
"""

import os
import tempfile
import sqlite3
import time
from datetime import datetime

# إعدادات الطباعة - مأخوذة من monthly_duties_window.py
PAPER_WIDTH = 75  # 75mm عرض (نفس monthly_duties_window.py)
PAPER_MARGINS = 0.4  # 0.4mm هوامش من جميع الجهات
FONT_NAME = "Calibri"
FONT_SIZE = 12
LINE_WIDTH = 70  # عدد الأحرف في كل سطر (مناسب لعرض 75mm)

def get_printer_name():
    """الحصول على اسم الطابعة من قاعدة البيانات أو الافتراضية"""
    try:
        conn = sqlite3.connect("data.db")
        cursor = conn.cursor()

        # محاولة الحصول على الطابعة من جدول إعدادات_الطابعة
        cursor.execute("SELECT الطابعة_الحرارية FROM إعدادات_الطابعة LIMIT 1")
        result = cursor.fetchone()
        conn.close()

        if result and result[0]:
            return result[0]
    except:
        pass

    # الحصول على الطابعة الافتراضية إذا لم تكن موجودة في قاعدة البيانات
    try:
        if os.name == 'nt':  # Windows
            import win32print
            return win32print.GetDefaultPrinter()
    except:
        pass

    return None

def direct_print(content):
    """طباعة المحتوى مباشرة باستخدام PyQt5 والطابعة الحرارية - نفس نظام monthly_duties_window.py"""
    printer_name = get_printer_name()

    if not printer_name:
        print("لم يتم العثور على طابعة متاحة")
        return False

    try:
        # استخدام PyQt5 للطباعة المباشرة
        from PyQt5.QtPrintSupport import QPrinter
        from PyQt5.QtGui import QPainter, QFont, QFontMetrics, QPen
        from PyQt5.QtCore import QSizeF, QRect, Qt
        from PyQt5.QtWidgets import QApplication

        # التأكد من وجود QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])

        # إعداد الطابعة
        printer = QPrinter()
        printer.setPrinterName(printer_name)

        # إعداد حجم الورقة للطابعة الحرارية (75mm عرض، ارتفاع متغير)
        printer.setPageSize(QPrinter.Custom)
        
        # حساب الارتفاع المطلوب حسب المحتوى
        estimated_height = calculate_content_height(content)
        printer.setPageSizeMM(QSizeF(PAPER_WIDTH, estimated_height))

        # إعداد الهوامش - نفس monthly_duties_window.py
        printer.setPageMargins(PAPER_MARGINS, PAPER_MARGINS, PAPER_MARGINS, PAPER_MARGINS, QPrinter.Millimeter)

        # إعداد الرسام
        painter = QPainter()
        if painter.begin(printer):
            # إعداد الخط Calibri 12 أسود غامق - نفس monthly_duties_window.py
            font = QFont(FONT_NAME, FONT_SIZE, QFont.Bold)
            painter.setFont(font)
            painter.setPen(Qt.black)

            # حساب مقاييس الخط
            font_metrics = QFontMetrics(font)
            line_height = font_metrics.height() + 4  # إضافة مسافة بين الأسطر

            # تقسيم المحتوى إلى أسطر
            lines = content.split('\n')

            # رسم المحتوى باستخدام نظام الجداول المحسن من monthly_duties_window.py
            y_position = 20  # البدء من الأعلى
            page_width = printer.pageRect().width() - 20  # عرض الصفحة مع الهوامش

            # حساب عرض الجدول (نفس العرض المستخدم في draw_table)
            table_width = int(page_width)  # عرض الجدول
            table_x = 10  # موضع بداية الجدول

            # متغيرات لتجميع بيانات الجدول
            table_data = []
            table_start_y = None
            in_table_section = False

            # رسم العناوين (في الوسط) - تجاهل الخطوط المزخرفة
            for i, line in enumerate(lines):
                # تجاهل جميع الخطوط المزخرفة والزخرفية
                if line.strip().startswith('=') or line.strip().startswith('-'):
                    continue

                # تجاهل السنة الدراسية وتفاصيل المهمة وانتهى التقرير
                if ('السنة الدراسية' in line or 'تفاصيل المهمة' in line or
                    'انتهى التقرير' in line):
                    continue

                if ('قائمة المهام' in line or
                    (i < 5 and not ':' in line and not 'تاريخ الطباعة' in line and 
                     'السنة الدراسية' not in line)):

                    # إذا كنا في قسم الجدول، ارسم الجدول أولاً
                    if table_data and table_start_y is not None:
                        y_position = draw_table(painter, table_data, table_start_y, page_width, line_height, font_metrics)
                        table_data = []
                        table_start_y = None
                        in_table_section = False

                    # رسم العناوين في الوسط
                    text_width = font_metrics.width(line)
                    x_position = int((page_width - text_width) / 2 + 10)
                    x_position = max(10, x_position)

                    painter.drawText(x_position, int(y_position), line)
                    y_position += line_height

                elif ':' in line and not line.strip().startswith('تاريخ الطباعة'):
                    # تجميع البيانات لرسم جدول واحد
                    if not in_table_section:
                        table_start_y = y_position
                        in_table_section = True

                    parts = line.split(':', 1)
                    if len(parts) == 2:
                        label = parts[0].strip()
                        value = parts[1].strip()
                        table_data.append((label, value))

                elif line.strip().startswith('تاريخ الطباعة'):
                    # إذا كنا في قسم الجدول، ارسم الجدول أولاً
                    if table_data and table_start_y is not None:
                        y_position = draw_table(painter, table_data, table_start_y, page_width, line_height, font_metrics)
                        table_data = []
                        table_start_y = None
                        in_table_section = False

                    # رسم تاريخ الطباعة في الوسط
                    text_width = font_metrics.width(line)
                    x_position = int((page_width - text_width) / 2 + 10)
                    x_position = max(10, x_position)

                    painter.drawText(x_position, int(y_position), line)
                    y_position += line_height

                elif line.strip() and not line.strip().startswith('=') and not line.strip().startswith('-'):
                    # إذا كنا في قسم الجدول، ارسم الجدول أولاً
                    if table_data and table_start_y is not None:
                        y_position = draw_table(painter, table_data, table_start_y, page_width, line_height, font_metrics)
                        table_data = []
                        table_start_y = None
                        in_table_section = False

                    # رسم النص العادي
                    wrapped_lines = wrap_text_to_width(line, LINE_WIDTH)
                    for wrapped_line in wrapped_lines:
                        painter.drawText(10, int(y_position), wrapped_line)
                        y_position += line_height

                else:
                    # سطر فارغ
                    y_position += line_height // 2

            # رسم الجدول الأخير إذا كان متبقي
            if table_data and table_start_y is not None:
                y_position = draw_table(painter, table_data, table_start_y, page_width, line_height, font_metrics)

            painter.end()
            print(f"تم إرسال الطباعة إلى {printer_name} باستخدام PyQt5")
            return True

    except ImportError:
        print("PyQt5 غير متوفر، استخدام الطريقة التقليدية...")
        return direct_print_fallback(content)
    except Exception as e:
        print(f"خطأ في الطباعة المباشرة: {e}")
        return direct_print_fallback(content)

def calculate_content_height(content):
    """حساب الارتفاع المطلوب للمحتوى"""
    lines = content.split('\n')
    total_lines = 0
    
    for line in lines:
        if line.strip():
            # حساب عدد الأسطر المطلوبة للسطر الطويل
            wrapped_lines = wrap_text_to_width(line, LINE_WIDTH)
            total_lines += len(wrapped_lines)
        else:
            total_lines += 0.5  # سطر فارغ
    
    # حساب الارتفاع: عدد الأسطر × ارتفاع السطر + هوامش
    line_height_mm = 4.5  # تقريباً 4.5mm لكل سطر بخط Calibri 12
    estimated_height = (total_lines * line_height_mm) + 20  # إضافة 20mm للهوامش
    
    # حد أدنى وأعلى للارتفاع
    min_height = 50
    max_height = 300
    
    return max(min_height, min(estimated_height, max_height))

def wrap_text_to_width(text, max_width):
    """تقسيم النص إلى أسطر حسب العرض المحدد"""
    if len(text) <= max_width:
        return [text]
    
    lines = []
    words = text.split(' ')
    current_line = ""
    
    for word in words:
        if len(current_line + word + " ") <= max_width:
            current_line += word + " "
        else:
            if current_line:
                lines.append(current_line.strip())
            current_line = word + " "
    
    if current_line:
        lines.append(current_line.strip())
    
    return lines

def direct_print_fallback(content):
    """طباعة المحتوى باستخدام الطريقة التقليدية (الملف النصي)"""
    printer_name = get_printer_name()

    if not printer_name:
        print("لم يتم العثور على طابعة متاحة")
        return False

    # إنشاء ملف مؤقت
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=".txt", mode="w", encoding="utf-8") as f:
            f.write(content)
            file_path = f.name

        print(f"تم إنشاء ملف مؤقت: {file_path}")
        print(f"محتوى الملف:\n{content}")

        success = False

        # طباعة الملف باستخدام الطريقة المناسبة
        if os.name == 'nt':  # Windows
            try:
                # طريقة win32api
                import win32api
                win32api.ShellExecute(0, "printto", file_path, f'"{printer_name}"', ".", 0)
                print(f"تم إرسال الطباعة إلى {printer_name} باستخدام win32api")
                success = True
            except ImportError:
                try:
                    # طريقة print command
                    import subprocess
                    subprocess.run(f'print /d:"{printer_name}" "{file_path}"', shell=True)
                    print(f"تم إرسال الطباعة باستخدام أمر print")
                    success = True
                except Exception as e:
                    try:
                        # طريقة startfile
                        os.startfile(file_path, "print")
                        print("تم إرسال الطباعة باستخدام startfile")
                        success = True
                    except Exception as e2:
                        print(f"خطأ في الطباعة: {e2}")
        else:  # Linux/Mac
            try:
                import subprocess
                subprocess.run(["lp", "-d", printer_name, file_path])
                print(f"تم إرسال الطباعة باستخدام lp")
                success = True
            except Exception as e:
                print(f"خطأ في الطباعة: {e}")

        # الانتظار وحذف الملف المؤقت
        time.sleep(2)
        try:
            os.unlink(file_path)
            print("تم حذف الملف المؤقت")
        except:
            print("لم يمكن حذف الملف المؤقت")

        return success

    except Exception as e:
        print(f"خطأ عام: {e}")
        return False

def get_institution_info():
    """الحصول على معلومات المؤسسة من قاعدة البيانات"""
    # القيم الافتراضية
    institution_name = "المؤسسة التعليمية"
    school_year = "2024/2025"

    try:
        conn = sqlite3.connect("data.db")
        cursor = conn.cursor()

        # استخراج اسم المؤسسة والسنة الدراسية
        cursor.execute("SELECT المؤسسة, السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
        result = cursor.fetchone()
        if result:
            institution_name = result[0] or institution_name
            school_year = result[1] or school_year

        conn.close()

    except Exception as e:
        print(f"خطأ في استخراج معلومات المؤسسة: {e}")

    return institution_name, school_year

def print_activity_list(activities, date_str=None):
    """طباعة قائمة المهام/النشاطات بطريقة الملف النصي"""
    try:
        # تحديد التاريخ إذا لم يتم تمريره
        if not date_str:
            date_str = datetime.now().strftime("%Y/%m/%d")
        
        # الحصول على معلومات المؤسسة
        institution_name, school_year = get_institution_info()
        
        # بناء محتوى الطباعة
        content = "=" * 50 + "\n"
        content += f"{institution_name}\n"
        content += f"السنة الدراسية: {school_year}\n"
        content += "=" * 50 + "\n"
        content += "قائمة المهام والنشاطات\n"
        content += "=" * 50 + "\n"
        content += f"تاريخ الطباعة: {date_str}\n"
        content += f"وقت الطباعة: {datetime.now().strftime('%H:%M')}\n"
        content += "\n"

        # إضافة المهام/النشاطات بصيغة جدول
        for i, activity in enumerate(activities, 1):
            content += f"رقم المهمة: {i}\n"
            
            if isinstance(activity, dict):
                # إذا كان المحتوى في شكل قاموس
                if 'title' in activity:
                    content += f"العنوان: {activity['title']}\n"
                if 'task' in activity:
                    content += f"المهمة: {activity['task']}\n"
                elif 'activity' in activity:
                    content += f"النشاط: {activity['activity']}\n"
                if 'date' in activity:
                    content += f"التاريخ: {activity['date']}\n"
                if 'status' in activity:
                    content += f"الحالة: {activity['status']}\n"
            else:
                # إذا كان المحتوى نص بسيط
                content += f"المحتوى: {str(activity)}\n"
            
            content += "\n"

        # إضافة تذييل
        content += "\n"
        content += "انتهى التقرير\n"
        content += "=" * 50 + "\n"

        # طباعة المحتوى
        return direct_print(content)

    except Exception as e:
        print(f"خطأ في طباعة قائمة المهام: {e}")
        return False

def print_single_activity(title, task, date_str=None):
    """طباعة مهمة واحدة بطريقة الملف النصي"""
    try:
        # تحديد التاريخ إذا لم يتم تمرره
        if not date_str:
            date_str = datetime.now().strftime("%Y/%m/%d")
        
        # الحصول على معلومات المؤسسة
        institution_name, school_year = get_institution_info()
        
        # بناء محتوى الطباعة
        content = "=" * 50 + "\n"
        content += f"{institution_name}\n"
        content += f"السنة الدراسية: {school_year}\n"
        content += "=" * 50 + "\n"
        content += "تفاصيل المهمة\n"
        content += "=" * 50 + "\n"
        content += f"تاريخ الطباعة: {date_str}\n"
        content += f"وقت الطباعة: {datetime.now().strftime('%H:%M')}\n"
        content += "\n"

        # إضافة تفاصيل المهمة بصيغة جدول
        content += f"عنوان المهمة: {title}\n"
        content += f"تفاصيل المهمة: {task}\n"
        content += f"تاريخ الإنشاء: {date_str}\n"
        content += "\n"

        # إضافة تذييل
        content += "\n"
        content += "انتهى التقرير\n" 
        content += "=" * 50 + "\n"

        # طباعة المحتوى
        return direct_print(content)

    except Exception as e:
        print(f"خطأ في طباعة المهمة: {e}")
        return False

def print_activity_report(activities, title="تقرير المهام اليومية", date_str=None):
    """طباعة تقرير مفصل للمهام مع إحصائيات"""
    try:
        # تحديد التاريخ إذا لم يتم تمريره
        if not date_str:
            date_str = datetime.now().strftime("%Y/%m/%d")
        
        # الحصول على معلومات المؤسسة
        institution_name, school_year = get_institution_info()
        
        # بناء محتوى الطباعة
        content = "=" * 50 + "\n"
        content += f"{institution_name}\n"
        content += f"السنة الدراسية: {school_year}\n"
        content += "=" * 50 + "\n"
        content += f"{title}\n"
        content += "=" * 50 + "\n"
        content += f"تاريخ التقرير: {date_str}\n"
        content += f"وقت الطباعة: {datetime.now().strftime('%H:%M')}\n"
        content += "\n"

        # إضافة إحصائيات
        total_activities = len(activities)
        content += f"إجمالي المهام: {total_activities}\n"
        
        # حساب الإحصائيات إذا كانت البيانات تحتوي على حالة
        completed_count = 0
        pending_count = 0
        
        for activity in activities:
            if isinstance(activity, dict) and 'status' in activity:
                if activity['status'] in ['مكتملة', 'تمت', 'منجزة']:
                    completed_count += 1
                else:
                    pending_count += 1
        
        if completed_count > 0 or pending_count > 0:
            content += f"المهام المكتملة: {completed_count}\n"
            content += f"المهام المعلقة: {pending_count}\n"
        
        content += "\n"

        # إضافة تفاصيل المهام
        for i, activity in enumerate(activities, 1):
            content += f"المهمة رقم: {i}\n"
            
            if isinstance(activity, dict):
                if 'title' in activity:
                    content += f"العنوان: {activity['title']}\n"
                if 'task' in activity:
                    content += f"الوصف: {activity['task']}\n"
                elif 'activity' in activity:
                    content += f"النشاط: {activity['activity']}\n"
                if 'date' in activity:
                    content += f"التاريخ: {activity['date']}\n"
                if 'status' in activity:
                    content += f"الحالة: {activity['status']}\n"
                if 'priority' in activity:
                    content += f"الأولوية: {activity['priority']}\n"
                if 'notes' in activity:
                    content += f"ملاحظات: {activity['notes']}\n"
            else:
                content += f"المحتوى: {str(activity)}\n"
            
            content += "\n"

        # إضافة تذييل
        content += "\n"
        content += "انتهى التقرير\n"
        content += "=" * 50 + "\n"

        # طباعة المحتوى
        return direct_print(content)

    except Exception as e:
        print(f"خطأ في طباعة التقرير: {e}")
        return False

def configure_thermal_printer_settings():
    """إعداد إعدادات الطابعة الحرارية"""
    settings = {
        'paper_width': PAPER_WIDTH,  # 75mm
        'paper_margins': PAPER_MARGINS,  # 0.4mm
        'font_name': FONT_NAME,  # Calibri
        'font_size': FONT_SIZE,  # 12
        'line_width': LINE_WIDTH,  # 70 أحرف
        'line_height_mm': 4.5,  # ارتفاع السطر بالمليمتر
        'min_paper_height': 50,  # الحد الأدنى لارتفاع الورق
        'max_paper_height': 300,  # الحد الأعلى لارتفاع الورق
    }
    
    return settings

def get_print_preview(content):
    """معاينة المحتوى قبل الطباعة"""
    try:
        lines = content.split('\n')
        preview = "=== معاينة الطباعة ===\n"
        preview += f"عدد الأسطر: {len(lines)}\n"
        preview += f"الارتفاع المقدر: {calculate_content_height(content)}mm\n"
        preview += f"إعدادات الورق: {PAPER_WIDTH}mm عرض\n"
        preview += "=" * 30 + "\n"
        
        # إضافة أول 10 أسطر كمعاينة
        for i, line in enumerate(lines[:10]):
            preview += f"{i+1:2d}: {line}\n"
        
        if len(lines) > 10:
            preview += f"... و {len(lines) - 10} سطر إضافي\n"
        
        preview += "=" * 30 + "\n"
        
        return preview
        
    except Exception as e:
        return f"خطأ في المعاينة: {e}"

def draw_table(painter, table_data, start_y, page_width, line_height, font_metrics):
    """رسم جدول حقيقي بخطوط وحدود - مستخرج من monthly_duties_window.py"""
    try:
        from PyQt5.QtGui import QPen
        from PyQt5.QtCore import QRect, Qt

        if not table_data:
            return start_y

        # إعدادات الجدول - عكس الأعمدة
        col1_width = int(page_width * 0.6)  # 60% للقيمة (العمود الأول الآن)
        col2_width = int(page_width * 0.4)  # 40% للتسمية (العمود الثاني الآن)
        table_x = 10
        table_width = col1_width + col2_width
        row_height = line_height + 4  # ارتفاع أكبر للصفوف

        # حساب ارتفاع الجدول الكامل
        table_height = len(table_data) * row_height

        # إعداد القلم للخطوط
        pen = QPen(Qt.black, 1)
        painter.setPen(pen)

        # رسم الحدود الخارجية للجدول
        painter.drawRect(table_x, int(start_y), table_width, table_height)

        # رسم الخط الفاصل العمودي
        painter.drawLine(table_x + col1_width, int(start_y), 
                        table_x + col1_width, int(start_y + table_height))

        # رسم الخطوط الأفقية وإدراج النصوص
        current_y = start_y
        for i, (label, value) in enumerate(table_data):
            # رسم الخط الأفقي العلوي للصف
            if i > 0:  # لا نرسم الخط الأول لأنه جزء من الحدود الخارجية
                painter.drawLine(table_x, int(current_y), 
                               table_x + table_width, int(current_y))

            # حساب موضع النص داخل الخلية
            text_y = current_y + (row_height / 2) + (font_metrics.height() / 2) - 2

            # رسم القيمة في العمود الأول (الأيسر)
            value_rect = QRect(table_x + 5, int(current_y), col1_width - 10, row_height)
            painter.drawText(value_rect, Qt.AlignRight | Qt.AlignVCenter, str(value))

            # رسم التسمية في العمود الثاني (الأيمن)
            label_rect = QRect(table_x + col1_width + 5, int(current_y), col2_width - 10, row_height)
            painter.drawText(label_rect, Qt.AlignRight | Qt.AlignVCenter, str(label))

            current_y += row_height

        # إعادة تعيين القلم للنص العادي
        painter.setPen(Qt.black)

        return current_y + 10  # إضافة مساحة بعد الجدول

    except Exception as e:
        print(f"خطأ في رسم الجدول: {e}")
        return start_y + (len(table_data) * line_height)

def test_print_activities():
    """اختبار طباعة المهام"""
    print("=== اختبار طباعة المهام ===")
    
    # عرض إعدادات الطابعة
    settings = configure_thermal_printer_settings()
    print(f"\nإعدادات الطابعة:")
    for key, value in settings.items():
        print(f"  {key}: {value}")
    
    # اختبارات الطباعة
    test_activities = [
        {
            'title': 'مهمة الصباح',
            'task': 'مراجعة الحضور والغياب',
            'date': '2024/12/27',
            'status': 'مكتملة',
            'priority': 'عالية'
        },
        {
            'title': 'مهمة الظهيرة', 
            'task': 'متابعة الطلاب المتأخرين',
            'date': '2024/12/27',
            'status': 'معلقة',
            'priority': 'متوسطة'
        },
        {
            'title': 'مهمة المساء',
            'task': 'إعداد تقرير يومي للإدارة',
            'date': '2024/12/27',
            'status': 'مكتملة',
            'priority': 'عالية',
            'notes': 'تم إرسال التقرير للمدير'
        }
    ]
    
    # اختبار معاينة الطباعة
    print("\n1. اختبار معاينة الطباعة:")
    sample_content = """المؤسسة التعليمية
السنة الدراسية: 2024/2025
قائمة المهام اليومية
تاريخ الطباعة: 2024/12/27
المهمة الأولى: مراجعة الحضور
تفاصيل المهمة: متابعة حضور الطلاب وتسجيل الغياب"""
    
    preview = get_print_preview(sample_content)
    print(preview)
    
    # اختبار طباعة مهمة واحدة
    print("\n2. اختبار طباعة مهمة واحدة:")
    result1 = print_single_activity(
        title="مهمة تجريبية",
        task="هذه مهمة تجريبية لاختبار نظام الطباعة الجديد باستخدام PyQt5 QPrinter مع حجم ورق 75mm",
        date_str="2024/12/27"
    )
    print(f"نتيجة طباعة المهمة الواحدة: {'✅ نجاح' if result1 else '❌ فشل'}")
    
    # اختبار طباعة قائمة مهام
    print("\n3. اختبار طباعة قائمة مهام:")
    result2 = print_activity_list(test_activities, "2024/12/27")
    print(f"نتيجة طباعة قائمة المهام: {'✅ نجاح' if result2 else '❌ فشل'}")
    
    # اختبار طباعة تقرير مفصل
    print("\n4. اختبار طباعة تقرير مفصل:")
    result3 = print_activity_report(test_activities, "تقرير المهام اليومية", "2024/12/27")
    print(f"نتيجة طباعة التقرير المفصل: {'✅ نجاح' if result3 else '❌ فشل'}")
    
    # معلومات إضافية
    print(f"\n=== معلومات الطباعة ===")
    printer_name = get_printer_name()
    print(f"الطابعة المستخدمة: {printer_name if printer_name else 'غير محددة'}")
    
    institution_name, school_year = get_institution_info()
    print(f"المؤسسة: {institution_name}")
    print(f"السنة الدراسية: {school_year}")
    
    print(f"\n💡 نصائح:")
    print(f"  - حجم الورق: {PAPER_WIDTH}mm عرض")
    print(f"  - الخط المستخدم: {FONT_NAME} {FONT_SIZE}")
    print(f"  - عدد الأحرف في السطر: {LINE_WIDTH}")
    print(f"  - الهوامش: {PAPER_MARGINS}mm من جميع الجهات")

if __name__ == "__main__":
    test_print_activities()
