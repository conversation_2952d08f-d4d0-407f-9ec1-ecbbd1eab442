#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشخيص مشكلة القائمة المنسدلة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

def diagnose_dropdown():
    """تشخيص مشكلة القائمة المنسدلة"""
    print("=" * 60)
    print("🔍 تشخيص مشكلة القائمة المنسدلة")
    print("=" * 60)
    
    # إنشاء تطبيق Qt
    app = QApplication(sys.argv)
    app.setApplicationName("تشخيص القائمة المنسدلة")
    
    try:
        print("📥 استيراد main_window...")
        from main_window import MainWindow
        print("✅ تم استيراد main_window بنجاح")
        
        print("\n🏗️ إنشاء النافذة الرئيسية...")
        main_window = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        print("\n🔍 فحص القائمة المنسدلة...")
        
        # التحقق من وجود القائمة المنسدلة
        if hasattr(main_window, 'dropdown_widget'):
            print("✅ تم العثور على dropdown_widget")
            dropdown = main_window.dropdown_widget
            
            print(f"   📏 الحجم: {dropdown.size()}")
            print(f"   📐 الارتفاع: {dropdown.height()}")
            print(f"   📐 العرض: {dropdown.width()}")
            print(f"   👁️ مرئي: {dropdown.isVisible()}")
            print(f"   🎨 اسم الكائن: {dropdown.objectName()}")
            
            # التحقق من الوالد
            parent = dropdown.parent()
            if parent:
                print(f"   👨‍👩‍👧‍👦 الوالد: {parent.objectName()}")
            else:
                print("   ❌ لا يوجد والد")
                
            # التحقق من التخطيط
            layout = dropdown.layout()
            if layout:
                print(f"   📋 التخطيط: {type(layout).__name__}")
                print(f"   📊 عدد العناصر: {layout.count()}")
            else:
                print("   ❌ لا يوجد تخطيط")
                
        else:
            print("❌ لم يتم العثور على dropdown_widget")
            
        # التحقق من فهرس التبويب
        if hasattr(main_window, 'institution_tab_index'):
            print(f"✅ فهرس تبويب بيانات المؤسسة: {main_window.institution_tab_index}")
        else:
            print("❌ لم يتم العثور على فهرس تبويب بيانات المؤسسة")
            
        # التحقق من التخطيط الرئيسي
        if hasattr(main_window, 'main_layout'):
            print(f"✅ التخطيط الرئيسي موجود - عدد العناصر: {main_window.main_layout.count()}")
            
            # طباعة جميع العناصر في التخطيط
            print("   📋 عناصر التخطيط الرئيسي:")
            for i in range(main_window.main_layout.count()):
                item = main_window.main_layout.itemAt(i)
                if item and item.widget():
                    widget = item.widget()
                    print(f"      {i}: {widget.objectName()} - {type(widget).__name__}")
                    
        else:
            print("❌ لم يتم العثور على التخطيط الرئيسي")
            
        # التحقق من navbar_frame
        if hasattr(main_window, 'navbar_frame'):
            print(f"✅ navbar_frame موجود: {main_window.navbar_frame.objectName()}")
            navbar_index = main_window.main_layout.indexOf(main_window.navbar_frame)
            print(f"   📍 فهرس navbar_frame في التخطيط: {navbar_index}")
        else:
            print("❌ لم يتم العثور على navbar_frame")
        
        print("\n🖥️ عرض النافذة...")
        main_window.show()
        main_window.setWindowTitle("تشخيص القائمة المنسدلة")
        
        print("\n🎯 اختبار إظهار القائمة يدوياً...")
        if hasattr(main_window, '_show_dropdown'):
            print("محاولة إظهار القائمة...")
            main_window._show_dropdown()
        else:
            print("❌ دالة _show_dropdown غير موجودة")
        
        print("\n📝 تعليمات الاختبار:")
        print("   1. تحقق من الرسائل التشخيصية أعلاه")
        print("   2. انقر على تبويب 'بيانات المؤسسة'")
        print("   3. راقب الرسائل في وحدة التحكم")
        print("   4. ابحث عن القائمة المنسدلة تحت التبويبات")
        
        print("\n🚀 تشغيل التطبيق...")
        return app.exec_()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد main_window: {e}")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    print("🔍 بدء تشخيص القائمة المنسدلة...")
    exit_code = diagnose_dropdown()
    print(f"\n🔚 انتهى التشخيص بكود الخروج: {exit_code}")
    sys.exit(exit_code)
