def print_doctor_visit_form(students, section, date_str=None, time_str=None, font_name=None, font_size=None, font_bold=None, font_italic=None, margin=None, silent_print=None, use_regular_printer=True):
    """طباعة نموذج زيارة الطبيب

    المعلمات:
        students: قائمة الطلاب للطباعة
        section: القسم
        date_str: تاريخ الطباعة (اختياري)
        time_str: وقت الطباعة (اختياري)
        font_name: اسم الخط (اختياري)
        font_size: حجم الخط (اختياري)
        font_bold: خط عريض (اختياري)
        font_italic: خط مائل (اختياري)
        margin: الهوامش (اختياري)
        silent_print: طباعة صامتة (اختياري)
        use_regular_printer: استخدام الطابعة العادية بدلاً من الطابعة الحرارية (افتراضي: True)
    """
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.lib.units import cm
        from reportlab.pdfgen import canvas
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        import arabic_reshaper
        from bidi.algorithm import get_display
        import os, sqlite3, time, subprocess
        from datetime import datetime

        # تحديد التاريخ والوقت إذا لم يتم تمريرهما
        if not date_str:
            date_str = datetime.now().strftime("%Y/%m/%d")
        if not time_str:
            time_str = datetime.now().strftime("%H:%M")

        # --- إعدادات قابلة للتعديل ---
        settings = {
            "logo_top": 3 * cm,
            "logo_width": 300,
            "logo_height": 70,

            "title_top": 4 * cm,
            "institution_font_size": 18,
            "form_title_font_size": 14,

            "year_right": 6.5 * cm,
            "year_top": 3 * cm,
            "semester_top": 4 * cm,

            "box_top": 5 * cm,  # تقليل المسافة من 6 إلى 5 سم
            "box_left": 1 * cm,
            "box_width": 19 * cm,
            "box_height": 9 * cm,

            "content_font_size": 14,
            "line_spacing": 1 * cm,
            "doctor_lines": 8,
        }

        pdfmetrics.registerFont(TTFont("Arabic", "arial.ttf"))

        def reshape(text):
            return get_display(arabic_reshaper.reshape(str(text)))

        student = students[0] if students else {"name": "", "code": ""}

        # استخراج اسم التلميذ ورمزه لاستخدامهما في اسم الملف
        student_name = student.get('name', 'بدون_اسم').replace(' ', '_')
        student_code = student.get('code', 'بدون_رمز')
        current_date = datetime.now().strftime('%Y%m%d')

        # إنشاء اسم الملف باستخدام اسم التلميذ ورمزه وتاريخ الطلب
        file_name = f"زيارة_طبيب_{student_name}_{student_code}_{current_date}.pdf"

        # إنشاء مجلد تقارير_زيارة_الطبيب داخل مجلد البرنامج
        doctor_visit_dir = os.path.join(os.path.dirname(__file__), "تقارير_زيارة_الطبيب")
        os.makedirs(doctor_visit_dir, exist_ok=True)

        # إنشاء مجلد temp للتوافق مع الكود القديم
        temp_dir = os.path.join(os.path.dirname(__file__), "temp")
        os.makedirs(temp_dir, exist_ok=True)

        # حفظ الملف في مجلد تقارير_زيارة_الطبيب
        file_path = os.path.join(doctor_visit_dir, file_name)

        c = canvas.Canvas(file_path, pagesize=A4)
        width, height = A4

        # --- استخراج الشعار من قاعدة البيانات ---
        logo_found = False
        try:
            db_path = os.path.join(os.path.dirname(__file__), "data.db")
            conn = sqlite3.connect(db_path)
            cur = conn.cursor()
            cur.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            logo_row = cur.fetchone()
            if logo_row and logo_row[0]:
                logo_path = logo_row[0]
                if os.path.exists(logo_path):
                    logo_x = (width - settings["logo_width"]) / 2
                    logo_y = height - settings["logo_top"]
                    c.drawImage(logo_path, logo_x, logo_y, width=settings["logo_width"], height=settings["logo_height"], preserveAspectRatio=True, mask='auto')
                    logo_found = True
        except Exception as e:
            print(f"خطأ في استخراج الشعار: {e}")

        # --- استخراج بيانات المؤسسة ---
        institution = "المؤسسة التعليمية"
        year_text = "2024/2025"
        semester = "الأول"
        try:
            conn = sqlite3.connect("data.db")
            cur = conn.cursor()
            cur.execute("SELECT المؤسسة, السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
            row = cur.fetchone()
            if row:
                institution = row[0] or institution
                year_text = row[1] or year_text
                semester = row[2] or semester
            conn.close()
        except Exception as e:
            print(f"خطأ في استخراج بيانات المؤسسة: {e}")

        # --- عنوان المؤسسة ---
        c.setFont("Arabic", settings["institution_font_size"])
        c.drawCentredString(width/2, height - settings["title_top"], reshape(institution))

        c.setFont("Arabic", settings["form_title_font_size"])
        c.drawCentredString(width/2, height - (settings["title_top"] + 20), reshape("ورقة زيارة الطبيب"))

        # --- السنة الدراسية والأسدس ---
        c.setFont("Arabic", settings["form_title_font_size"])
        c.drawString(width - settings["year_right"], height - settings["year_top"], reshape(f"السنة الدراسية : {year_text}"))

        # --- الإطار الرئيسي ---
        box_top = height - settings["box_top"]
        box_left = settings["box_left"]
        box_width = settings["box_width"]
        box_height = settings["box_height"]
        c.rect(box_left, box_top - box_height, box_width, box_height)

        # --- محتوى التلميذ ---
        right_x = width - 1.2 * cm
        y = box_top - 1.2 * cm
        c.setFont("Arabic", settings["content_font_size"])
        c.drawRightString(right_x, y, reshape(f"يسمح للتلميذ(ة) : {student.get('name', '')}"))
        y -= settings["line_spacing"]
        c.drawRightString(right_x, y, reshape(f"قسم : {section}  رقم التسجيل : {student.get('code', '')}"))
        y -= settings["line_spacing"]
        c.drawRightString(right_x, y, reshape(f"بزيارة الطبيب يوم : {date_str}"))
        y -= settings["line_spacing"]
        c.drawRightString(right_x, y, reshape(f"على الساعة : {time_str}"))

        # --- توقيع الحراسة العامة ---
        c.drawRightString(right_x, box_top - box_height + 2.2 * cm, reshape("ختم وتوقيع الحراسة العامة"))

        # --- رأي الطبيب ---
        left_x = box_left + 1.5 * cm
        y2 = box_top - 1.2 * cm
        c.drawString(left_x, y2, reshape("رأي الطبيب"))
        y2 -= 0.7 * cm
        for _ in range(settings["doctor_lines"]):
            c.line(left_x, y2, left_x + 8 * cm, y2)
            y2 -= 0.7 * cm
        y2 -= 0.5 * cm
        c.drawString(left_x, y2, reshape("توقيع الطبيب"))

        c.save()

        # --- طباعة الملف ---
        # استخدام معلمة use_regular_printer للتحكم في طريقة الطباعة
        print(f"طباعة نموذج زيارة الطبيب باستخدام {'الطابعة العادية' if use_regular_printer else 'الطابعة الحرارية'}")

        if os.name == 'nt':
            try:
                # طباعة الملف مباشرة
                os.startfile(file_path, "print")
            except Exception as e:
                print(f"خطأ في الطباعة المباشرة: {e}")
                # فتح الملف فقط إذا فشلت الطباعة المباشرة
                subprocess.Popen(["start", "", file_path], shell=True)
        else:
            # لأنظمة لينكس وماك
            subprocess.Popen(["xdg-open", file_path])

        time.sleep(4)
        return True

    except Exception as e:
        print(f"خطأ أثناء الطباعة: {e}")
        return False

def print_school_certificates_list():
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.lib.units import cm
        from reportlab.pdfgen import canvas
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        import arabic_reshaper
        from bidi.algorithm import get_display
        import os, sqlite3, time, subprocess
        from datetime import datetime

        # تسجيل الخط العربي
        pdfmetrics.registerFont(TTFont("Arabic", "arial.ttf"))

        def reshape(text):
            return get_display(arabic_reshaper.reshape(str(text)))

        # إنشاء اسم الملف والمجلد
        file_name = f"سجلات_طلبات_الشهادة_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

        # إنشاء مجلد طلبات_الشواهد_المدرسية داخل مجلد البرنامج
        certificates_dir = os.path.join(os.path.dirname(__file__), "طلبات_الشواهد_المدرسية")
        os.makedirs(certificates_dir, exist_ok=True)

        # إنشاء مجلد temp للتوافق مع الكود القديم
        temp_dir = os.path.join(os.path.dirname(__file__), "temp")
        os.makedirs(temp_dir, exist_ok=True)

        # حفظ الملف في مجلد طلبات_الشواهد_المدرسية
        file_path = os.path.join(certificates_dir, file_name)

        # إنشاء PDF
        c = canvas.Canvas(file_path, pagesize=A4)
        width, height = A4

        # --- استخراج الشعار والبيانات من قاعدة البيانات ---
        db_path = os.path.join(os.path.dirname(__file__), "data.db")
        conn = sqlite3.connect(db_path)
        cur = conn.cursor()

        # استخراج بيانات المؤسسة
        cur.execute("SELECT المؤسسة, السنة_الدراسية, الأسدس, ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
        institution_data = cur.fetchone()
        institution = institution_data[0] if institution_data else "المؤسسة"
        year = institution_data[1] if institution_data else ""
        semester = institution_data[2] if institution_data else ""
        logo_path = institution_data[3] if institution_data else None

        # طباعة الشعار إذا وجد
        if logo_path and os.path.exists(logo_path):
            c.drawImage(logo_path, 150, height - 80, width=300, height=70, preserveAspectRatio=True)

        # طباعة العنوان والمعلومات الأساسية بشكل موسط
        c.setFont("Arabic", 16)
        c.drawCentredString(width/2, height - 100, reshape(institution))
        c.setFont("Arabic", 14)
        c.drawCentredString(width/2, height - 120, reshape(f"السنة الدراسية: {year}"))
        c.drawCentredString(width/2, height - 140, reshape("سجلات طلبات الشهادة المدرسية"))

        # طباعة رأس الجدول
        headers = ["الرمز", "القسم", "الاسم والنسب", "تاريخ الطلب", "تاريخ التسليم"]
        col_widths = [100, 80, 150, 100, 100]  # عرض الأعمدة
        total_width = sum(col_widths)
        right_margin = width - 50  # هامش من اليمين
        start_y = height - 200
        row_height = 30

        # رسم خلفية رأس الجدول
        c.setFillColorRGB(0.2, 0.4, 0.6)
        c.rect(right_margin - total_width, start_y, total_width, row_height, fill=1)

        # طباعة عناوين الأعمدة
        c.setFillColorRGB(1, 1, 1)  # لون أبيض للنص
        x = right_margin
        for i, header in enumerate(headers):
            # حساب موقع النص داخل كل عمود مع محاذاة لليمين
            text_width = c.stringWidth(reshape(header), "Arabic", 14)
            text_x = x - text_width - 10  # إضافة هامش 10 نقاط من اليمين
            c.drawString(text_x, start_y + 10, reshape(header))
            x -= col_widths[i]

        # استخراج البيانات من الجدول
        cur.execute("""
            SELECT الرمز, القسم, الاسم_والنسب, تاريخ_الطلب, تاريخ_التسليم
            FROM الشهادة_المدرسية
            ORDER BY الرقم
        """)
        records = cur.fetchall()

        # طباعة البيانات
        y = start_y - row_height
        c.setFillColorRGB(0, 0, 0)  # لون أسود للنص
        for record in records:
            if y < 50:  # صفحة جديدة
                c.showPage()
                c.setFont("Arabic", 14)
                y = height - 100

            x = right_margin
            for i, value in enumerate(record):
                # حساب موقع النص مع محاذاة لليمين
                value_str = str(value if value is not None else "")
                text_width = c.stringWidth(reshape(value_str), "Arabic", 14)
                text_x = x - text_width - 10  # هامش 10 نقاط من اليمين
                c.drawString(text_x, y + 10, reshape(value_str))
                c.rect(x - col_widths[i], y, col_widths[i], row_height)
                x -= col_widths[i]
            y -= row_height

        # حفظ وإغلاق الملف
        c.save()
        conn.close()

        # فتح الملف
        if os.name == 'nt':  # Windows
            os.startfile(file_path)
        else:  # Linux/Mac
            subprocess.Popen(['xdg-open', file_path])

        time.sleep(1)  # انتظار لحظة للتأكد من فتح الملف
        return True

    except Exception as e:
        print(f"خطأ في طباعة سجلات طلبات الشهادة: {e}")
        return False

def print_activity_list(activities):
    try:
        from reportlab.lib.pagesizes import custom
        from reportlab.lib.units import mm
        from reportlab.pdfgen import canvas
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase import TTFont
        import arabic_reshaper
        from bidi.algorithm import get_display
        import os, sqlite3, time, subprocess
        from datetime import datetime

        # تسجيل الخط العربي
        pdfmetrics.registerFont(TTFont("Arabic", "arial.ttf"))

        def reshape(text):
            return get_display(arabic_reshaper.reshape(str(text)))

        # إنشاء اسم الملف والمجلد
        file_name = f"النشاطات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        temp_dir = os.path.join(os.path.dirname(__file__), "temp")
        os.makedirs(temp_dir, exist_ok=True)
        file_path = os.path.join(temp_dir, file_name)

        # تعيين حجم الورق للطابعة الحرارية (80 مم × طول مناسب)
        page_width = 80 * mm
        page_height = 297 * mm  # يمكن تعديله حسب الحاجة

        # إنشاء PDF
        c = canvas.Canvas(file_path, pagesize=(page_width, page_height))

        # استخراج بيانات المؤسسة
        db_path = os.path.join(os.path.dirname(__file__), "data.db")
        conn = sqlite3.connect(db_path)
        cur = conn.cursor()

        institution = "المؤسسة"
        year = ""

        try:
            cur.execute("SELECT المؤسسة, السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            row = cur.fetchone()
            if row:
                institution = row[0] if row[0] else institution
                year = row[1] if row[1] else year
        except Exception as e:
            print(f"خطأ في استخراج بيانات المؤسسة: {e}")

        # طباعة العنوان
        y = page_height - 20 * mm
        c.setFont("Arabic", 12)
        c.drawCentredString(page_width/2, y, reshape(institution))
        y -= 5 * mm
        c.setFont("Arabic", 10)
        c.drawCentredString(page_width/2, y, reshape(f"السنة الدراسية: {year}"))
        y -= 5 * mm
        c.drawCentredString(page_width/2, y, reshape("النشاطات المدرسية"))
        y -= 10 * mm

        # طباعة التاريخ
        current_date = datetime.now().strftime("%Y-%m-%d")
        c.setFont("Arabic", 8)
        c.drawRightString(page_width - 5*mm, y, reshape(f"التاريخ: {current_date}"))
        y -= 10 * mm

        # طباعة النشاطات
        c.setFont("Arabic", 10)
        for activity in activities:
            # التحقق من المساحة المتبقية
            if y < 20 * mm:  # إنشاء صفحة جديدة إذا لم تكن هناك مساحة كافية
                c.showPage()
                c.setFont("Arabic", 10)
                y = page_height - 20 * mm

            # طباعة العنوان
            c.setFont("Arabic", 10)
            title_text = reshape(activity['title'])
            c.drawString(page_width - 10*mm, y, title_text)
            y -= 5 * mm

            # طباعة تفاصيل النشاط
            c.setFont("Arabic", 8)
            activity_lines = activity['activity'].split('\n')
            for line in activity_lines:
                c.drawString(page_width - 15*mm, y, reshape(line))
                y -= 4 * mm

            # إضافة مسافة بين النشاطات
            y -= 5 * mm

            # خط فاصل
            c.line(10*mm, y, page_width - 10*mm, y)
            y -= 5 * mm

        # إضافة التذييل
        footer_text = "شكراً لكم"
        c.setFont("Arabic", 8)
        c.drawCentredString(page_width/2, 10*mm, reshape(footer_text))

        # حفظ وإغلاق الملف
        c.save()
        conn.close()

        # طباعة الملف
        if os.name == 'nt':  # Windows
            try:
                os.startfile(file_path, "print")
            except:
                subprocess.Popen(["start", "", file_path], shell=True)
        else:  # Linux/Mac
            subprocess.Popen(['lpr', file_path])  # طباعة مباشرة للطابعة الافتراضية

        time.sleep(1)
        return True

    except Exception as e:
        print(f"خطأ في طباعة النشاطات: {e}")
        return False
if __name__ == "__main__":
    # تجربة دالة الطباعة الأولى
    student_data = [{"name": "أمال الصغير", "code": "A135018031"}]
    print_doctor_visit_form(student_data, section="2BACSP-2", date_str="2025-04-18", time_str="09:30")
