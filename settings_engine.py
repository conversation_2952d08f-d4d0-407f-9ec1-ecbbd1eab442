"""
محرك الإعدادات - ملف منفصل محدث للعمل مع QWebChannel
يحتوي على جميع دوال الإعدادات والصيانة
"""

import os
import json
import sqlite3
import shutil
import zipfile
import datetime
import tempfile
import sys
from pathlib import Path
from PyQt5.QtWidgets import QFileDialog, QInputDialog, QLineEdit, QMessageBox, QProgressDialog, QApplication
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal, QDateTime, Qt
from PyQt5.QtGui import QIcon, QColor

# محاولة استيراد pandas مع معالجة الخطأ
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

# محاولة استيراد xlrd
try:
    import xlrd
    XLRD_AVAILABLE = True
except ImportError:
    XLRD_AVAILABLE = False

# محاولة استيراد openpyxl
try:
    import openpyxl
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

class SettingsEngine(QObject):
    """محرك الإعدادات - مسؤول عن كل وظائف الإعدادات والصيانة"""
    
    # إشارات للتواصل مع واجهة HTML
    logUpdated = pyqtSignal(str, str, str)  # message, status, timestamp (للتوافق مع النظام الرئيسي)
    operationProgressUpdated = pyqtSignal(int, str)  # progress percentage, status text (للتوافق مع النظام الرئيسي)
    progress_updated = pyqtSignal(int)  # إشارة تحديث شريط التقدم
    status_updated = pyqtSignal(str)   # إشارة تحديث الحالة
    operation_completed = pyqtSignal(dict)  # إشارة إكمال العملية
    
    def __init__(self, parent_window=None):
        super().__init__()
        self.db_path = "data.db"
        self.parent_window = parent_window
        self.pandas_available = PANDAS_AVAILABLE
        self.xlrd_available = XLRD_AVAILABLE  
        self.openpyxl_available = OPENPYXL_AVAILABLE    
    @pyqtSlot(result=str)
    def get_libraries_status(self):
        """الحصول على حالة المكتبات المستخدمة"""
        try:
            libraries_info = []
            
            # معلومات المكتبات
            library_details = [
                ("pandas", self.pandas_available, "للتعامل مع البيانات وملفات Excel (أساسي)"),
                ("xlrd", self.xlrd_available, "للتعامل مع ملفات Excel القديمة (اختياري)"),
                ("openpyxl", self.openpyxl_available, "للتعامل مع ملفات Excel الحديثة (اختياري)"),
                ("sqlite3", True, "لقاعدة البيانات المحلية (مدمج مع Python)"),
                ("PyQt5", True, "لواجهة المستخدم الرسومية (مثبت بالفعل)")
            ]
            
            for name, status, description in library_details:
                libraries_info.append({
                    "name": name,
                    "status": status,
                    "description": description
                })
            
            # معلومات النظام
            system_info = {
                "python_version": sys.version.split()[0],
                "platform": sys.platform,
                "executable": sys.executable
            }
            
            result = {
                "libraries": libraries_info,
                "system": system_info,
                "install_tips": [
                    "لتثبيت pandas: pip install pandas",
                    "لتثبيت xlrd: pip install xlrd", 
                    "لتثبيت openpyxl: pip install openpyxl"
                ]
            }
            
            return json.dumps(result, ensure_ascii=False)
            
        except Exception as e:
            return json.dumps({"error": f"خطأ في الحصول على حالة المكتبات: {str(e)}"}, ensure_ascii=False)    
    @pyqtSlot(result=str)
    def get_database_info(self):
        """الحصول على معلومات قاعدة البيانات"""
        try:
            if not os.path.exists(self.db_path):
                return json.dumps({
                    "exists": False,
                    "size_mb": 0,
                    "tables_count": 0,
                    "last_modified": "غير موجود"
                }, ensure_ascii=False)

            # حجم الملف
            size_bytes = os.path.getsize(self.db_path)
            size_mb = round(size_bytes / (1024 * 1024), 2)
            
            # تاريخ آخر تعديل
            last_modified = datetime.datetime.fromtimestamp(os.path.getmtime(self.db_path))
            last_modified_str = last_modified.strftime("%Y-%m-%d %H:%M:%S")
            
            # عدد الجداول
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            tables_count = cursor.fetchone()[0]
            conn.close()
            
            info = {
                "exists": True,
                "size_mb": size_mb,
                "tables_count": tables_count,
                "last_modified": last_modified_str
            }
            
            return json.dumps(info, ensure_ascii=False)
            
        except Exception as e:
            return json.dumps({"error": str(e)}, ensure_ascii=False)
    
    @pyqtSlot(str, result=str)
    def delete_all_data(self, password):
        """حذف جميع البيانات مع التحقق من كلمة المرور"""
        try:
            # التحقق من كلمة المرور
            if password != "12345":
                return json.dumps({"success": False, "message": "رمز الحذف غير صحيح!"}, ensure_ascii=False)
                
            self.status_updated.emit("جاري حذف جميع البيانات...")
            self.progress_updated.emit(10)
            
            # قائمة الجداول المراد حذف بياناتها
            tables_to_clear = [
                'البنية_التربوية', 'زيارة_ولي_الأمر', 'تبريرات_الغياب', 'المخالفات',
                'الأساتذة', 'السجل_العام', 'اللوائح', 'الشهادة_المدرسية', 'ورقة_السماح_بالدخول',
                'اخبار_بنشاط', 'الرمز_السري', 'السجل_الاولي', 'زيارات_أولياء_الأمور',
                'غياب_الأسدس_الأول', 'غياب_الأسدس_الثاني', 'مجموع_الغياب_السنوي', 'مسك_الغياب_الأسبوعي'
            ]
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            self.progress_updated.emit(30)
            
            # حذف البيانات من الجداول
            deleted_tables = []
            for table in tables_to_clear:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                if cursor.fetchone():
                    cursor.execute(f"DELETE FROM {table}")
                    deleted_tables.append(table)
            
            self.progress_updated.emit(60)
            
            # معالجة خاصة لجدول بيانات_المؤسسة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='بيانات_المؤسسة'")
            if cursor.fetchone():
                # الحصول على السنة الدراسية
                cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
                result = cursor.fetchone()
                current_school_year = result[0] if result else None
                
                if current_school_year:
                    # حذف وإعادة إدخال مع الاحتفاظ بالسنة الدراسية
                    cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
                    columns = [column[1] for column in cursor.fetchall()]
                    
                    cursor.execute("DELETE FROM بيانات_المؤسسة")
                    
                    # إعادة إدخال مع الاحتفاظ بالسنة الدراسية
                    column_names = ", ".join(columns)
                    values = []
                    placeholders = []
                    
                    for column in columns:
                        if column == "السنة_الدراسية":
                            values.append(current_school_year)
                        else:
                            values.append("")
                        placeholders.append("?")
                    
                    placeholders_str = ", ".join(placeholders)
                    insert_query = f"INSERT INTO بيانات_المؤسسة ({column_names}) VALUES ({placeholders_str})"
                    cursor.execute(insert_query, values)
                else:
                    cursor.execute("DELETE FROM بيانات_المؤسسة")
            
            self.progress_updated.emit(80)
            
            # إنشاء/تحديث جدول اللوائح مع بيانات افتراضية
            self._setup_default_data(cursor, current_school_year)
            
            # ضغط قاعدة البيانات
            try:
                conn.execute("VACUUM")
            except Exception as e:
                print(f"خطأ أثناء ضغط قاعدة البيانات: {str(e)}")
            
            conn.commit()
            conn.close()
            
            self.progress_updated.emit(100)
            
            result = {
                "success": True,
                "message": "تم حذف جميع البيانات بنجاح",
                "deleted_tables": deleted_tables,
                "school_year_preserved": current_school_year
            }
            
            return json.dumps(result, ensure_ascii=False)
            
        except Exception as e:
            return json.dumps({"success": False, "message": f"خطأ أثناء حذف البيانات: {str(e)}"}, ensure_ascii=False)
    
    @pyqtSlot(str, result=str)
    def reset_school_year(self, password):
        """تهيئة البرنامج لبداية سنة دراسية جديدة"""
        try:
            # التحقق من كلمة المرور
            if password != "12345":
                return json.dumps({"success": False, "message": "رمز التأكيد غير صحيح!"}, ensure_ascii=False)
                
            self.status_updated.emit("جاري تهيئة البرنامج للسنة الدراسية الجديدة...")
            self.progress_updated.emit(20)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # قائمة الجداول المراد تفريغها
            tables_to_clear = ['ورقة_السماح_بالدخول', 'تبريرات_الغياب', 'المخالفات', 'الشهادة_المدرسية', 'مسك_الغياب_الأسبوعي']
            tables_cleared = []
            
            self.progress_updated.emit(50)
            
            for table in tables_to_clear:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                if cursor.fetchone():
                    cursor.execute(f"DELETE FROM '{table}'")
                    tables_cleared.append(table)
            
            self.progress_updated.emit(80)
            
            conn.commit()
            conn.close()
            
            self.progress_updated.emit(100)
            
            result = {
                "success": True,
                "message": "تم تهيئة البرنامج لسنة دراسية جديدة بنجاح!",
                "cleared_tables": tables_cleared
            }
            
            return json.dumps(result, ensure_ascii=False)
            
        except Exception as e:
            return json.dumps({"success": False, "message": f"خطأ أثناء تهيئة البرنامج: {str(e)}"}, ensure_ascii=False)
    
    @pyqtSlot(result=str)
    def backup_database(self):
        """عمل نسخة احتياطية لقاعدة البيانات"""
        try:
            self.status_updated.emit("جاري إنشاء نسخة احتياطية...")
            self.progress_updated.emit(10)
            
            # إنشاء مجلد النسخ الاحتياطي على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            main_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            backup_folder = os.path.join(main_folder, "النسخ الاحتياطي للبرنامج")
            
            if not os.path.exists(main_folder):
                os.makedirs(main_folder)
            if not os.path.exists(backup_folder):
                os.makedirs(backup_folder)
            
            self.progress_updated.emit(20)
            
            # توليد اسم ملف النسخة الاحتياطية
            current_datetime = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            backup_name = f"database_backup_{current_datetime}"
            backup_sqlite = os.path.join(backup_folder, f"{backup_name}.sqlite")
            backup_zip = os.path.join(backup_folder, f"{backup_name}.zip")
            
            self.progress_updated.emit(40)
            
            # إصلاح وضغط قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            conn.execute("PRAGMA integrity_check")
            conn.execute("VACUUM")
            
            # إنشاء نسخة احتياطية
            backup_conn = sqlite3.connect(backup_sqlite)
            conn.backup(backup_conn)
            
            backup_conn.close()
            conn.close()
            
            self.progress_updated.emit(70)
            
            # ضغط ملف النسخة الاحتياطية
            with zipfile.ZipFile(backup_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipf.write(backup_sqlite, os.path.basename(backup_sqlite))
            
            # حذف الملف المؤقت
            os.remove(backup_sqlite)
            
            self.progress_updated.emit(90)
            
            # حساب حجم النسخة الاحتياطية
            backup_size_kb = os.path.getsize(backup_zip) / 1024
            backup_size_mb = backup_size_kb / 1024
            size_text = f"{backup_size_mb:.2f} MB" if backup_size_mb >= 1 else f"{backup_size_kb:.2f} KB"
            
            self.progress_updated.emit(100)
            
            result = {
                "success": True,
                "message": "تم عمل نسخة احتياطية بنجاح!",
                "filename": os.path.basename(backup_zip),
                "path": backup_folder,
                "size": size_text,
                "datetime": current_datetime.replace('_', ' ')
            }
            
            return json.dumps(result, ensure_ascii=False)
            
        except Exception as e:
            return json.dumps({"success": False, "message": f"حدث خطأ أثناء عمل نسخة احتياطية: {str(e)}"}, ensure_ascii=False)
    
    @pyqtSlot(result=str)
    def insert_weekly_absence(self):
        """إدراج الغياب الأسبوعي"""
        try:
            self.status_updated.emit("جاري إدراج الغياب الأسبوعي...")
            self.progress_updated.emit(20)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # الحصول على السنة الدراسية
            cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            if not result:
                conn.close()
                return json.dumps({"success": False, "message": "لم يتم العثور على بيانات في جدول بيانات_المؤسسة"}, ensure_ascii=False)
            
            السنة = result[0]
            
            self.progress_updated.emit(50)
            
            # إدراج الغياب الأسبوعي
            insert_query = """
            WITH limited_schedule AS (
                SELECT *
                FROM (
                    SELECT j.*, ROW_NUMBER() OVER (ORDER BY j.الشهر) as rn
                    FROM جدولة_مسك_الغياب j
                    WHERE j.السنة_الدراسية = ?
                ) sub
                WHERE rn <= 10
            )
            INSERT OR IGNORE INTO مسك_الغياب_الأسبوعي
                (السنة_الدراسية, ملاحظات,الأسدس, الشهر, "1", "2", "3", "4", "5", بداية_الشهر, رمز_التلميذ)
            SELECT
                ls.السنة_الدراسية,
                '' as "ملاحظات",
                ls.الأسدس,
                ls.الشهر,
                '0' as "1",
                '0' as "2",
                '0' as "3",
                '0' as "4",
                '0' as "5",
                ls.بداية_الشهر,
                l.الرمز
            FROM limited_schedule ls
            CROSS JOIN (SELECT * FROM اللوائح WHERE السنة_الدراسية = ?) l
            """
            
            cursor.execute(insert_query, (السنة, السنة))
            conn.commit()
            
            self.progress_updated.emit(80)
            
            # حساب عدد السجلات
            cursor.execute("SELECT COUNT(*) FROM مسك_الغياب_الأسبوعي")
            count = cursor.fetchone()[0]
            
            conn.close()
            
            self.progress_updated.emit(100)
            
            result = {
                "success": True,
                "message": f"تم إدراج الغياب الأسبوعي بنجاح!\nعدد السجلات الحالي: {count}",
                "records_count": count
            }
            
            return json.dumps(result, ensure_ascii=False)
            
        except Exception as e:
            return json.dumps({"success": False, "message": f"حدث خطأ أثناء الإدراج: {str(e)}"}, ensure_ascii=False)
    
    def _setup_default_data(self, cursor, school_year):
        """إعداد البيانات الافتراضية للجداول"""
        try:
            # إنشاء جدول اللوائح إذا لم يكن موجوداً
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='اللوائح'")
            if not cursor.fetchone():
                try:
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS اللوائح (
                            السنة_الدراسية TEXT,
                            القسم TEXT,
                            المستوى TEXT,
                            الرمز TEXT,
                            رت TEXT,
                            مجموع التلاميذ INTEGER DEFAULT 0,
                            PRIMARY KEY(السنة_الدراسية, الرمز)
                        )
                    """)
                except Exception:
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS [اللوائح] (
                            [السنة_الدراسية] TEXT,
                            [القسم] TEXT,
                            [المستوى] TEXT,
                            [الرمز] TEXT,
                            [رت] TEXT,
                            [مجموع التلاميذ] INTEGER DEFAULT 0,
                            PRIMARY KEY([السنة_الدراسية], [الرمز])
                        )
                    """)
            
            # إضافة البيانات الافتراضية
            if school_year:
                # إنشاء/تحديث جدول السجل_العام
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='السجل_العام'")
                if cursor.fetchone():
                    cursor.execute("SELECT COUNT(*) FROM السجل_العام WHERE الرمز = 'A12345678'")
                    if cursor.fetchone()[0] == 0:
                        cursor.execute("""
                            INSERT INTO السجل_العام (الرمز, الاسم_والنسب, السماح, التأخر, عدد_المخالفات, الهاتف_الأول, ملاحظات)
                            VALUES ('A12345678', 'تلميذ افتراضي', '0', '0', '0', '', '')
                        """)
                else:
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS 'السجل_العام' (
                            'الرمز' TEXT PRIMARY KEY,
                            'الاسم_والنسب' TEXT,
                            'السماح' TEXT,
                            'التأخر' TEXT,
                            'عدد_المخالفات' TEXT,
                            'الهاتف_الأول' TEXT,
                            'ملاحظات' TEXT
                        )
                    """)
                    cursor.execute("""
                        INSERT INTO السجل_العام (الرمز, الاسم_والنسب, السماح, التأخر, عدد_المخالفات, الهاتف_الأول, ملاحظات)
                        VALUES ('A12345678', 'تلميذ افتراضي', '0', '0', '0', '', '')
                    """)
                
                # إضافة بيانات افتراضية لجدول اللوائح
                try:
                    cursor.execute("""
                        INSERT INTO اللوائح (السنة_الدراسية, القسم, المستوى, الرمز, رت, مجموع التلاميذ)
                        VALUES (?, '1APIC-1', 'الأولى إعدادي مسار دولي', 'A12345678', '1', 43)
                    """, (school_year,))
                except Exception:
                    cursor.execute("""
                        INSERT INTO [اللوائح] (السنة_الدراسية, القسم, المستوى, الرمز, رت, [مجموع التلاميذ])
                        VALUES (?, '1APIC-1', 'الأولى إعدادي مسار دولي', 'A12345678', '1', 43)
                    """, (school_year,))
                    
        except Exception as e:
            print(f"خطأ في إعداد البيانات الافتراضية: {e}")
    
    # دوال للتوافق مع النظام القديم - يمكن حذفها لاحقاً
    @pyqtSlot()
    def deleteAllData(self):
        """للتوافق مع النظام القديم"""
        return self.delete_all_data("12345")
    
    @pyqtSlot()
    def resetSchoolYear(self):
        """للتوافق مع النظام القديم"""
        return self.reset_school_year("12345")
    
    @pyqtSlot()
    def backupDatabase(self):
        """للتوافق مع النظام القديم"""
        return self.backup_database()
    
    @pyqtSlot()
    def insertWeeklyAbsence(self):
        """للتوافق مع النظام القديم"""
        return self.insert_weekly_absence()    
    @pyqtSlot()
    def resetSchoolYear(self):
        """تهيئة البرنامج لبداية سنة دراسية جديدة"""
        return self.reset_school_year("12345")
    
    @pyqtSlot()
    def backupDatabase(self):
        """عمل نسخة احتياطية لقاعدة البيانات"""
        return self.backup_database()
    
    @pyqtSlot()
    def insertWeeklyAbsence(self):
        """إدراج الغياب الأسبوعي"""
        return self.insert_weekly_absence()
    
    @pyqtSlot()
    def showDatabaseStatistics(self):
        """عرض إحصائيات قاعدة البيانات"""
        return self.get_libraries_status()
    
    @pyqtSlot()
    def getDatabaseInfo(self):
        """الحصول على معلومات قاعدة البيانات"""
        return self.get_database_info()
    
    def emit_log(self, message, status="info"):
        """إرسال رسالة إلى واجهة HTML - للتوافق مع النظام الرئيسي"""
        timestamp = QDateTime.currentDateTime().toString("hh:mm:ss")
        self.logUpdated.emit(message, status, timestamp)
    
    @pyqtSlot(result=bool)
    def checkPandasAvailability(self):
        """التحقق من توفر pandas - للتوافق مع النظام الرئيسي"""
        return self.pandas_available
