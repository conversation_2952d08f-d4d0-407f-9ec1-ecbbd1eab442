#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف اختبار التحسينات المطبقة على sub4_window.py
اختبار الميزات المتقدمة المنقولة من sub252_window.py
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

def test_enhanced_features():
    """اختبار الميزات المحسنة"""
    print("=" * 60)
    print("🎨 اختبار التحسينات المطبقة على sub4_window.py")
    print("=" * 60)
    
    # إنشاء تطبيق Qt
    app = QApplication(sys.argv)
    app.setApplicationName("اختبار التحسينات")
    
    # تعيين أيقونة التطبيق إذا كانت متوفرة
    if os.path.exists("01.ico"):
        app.setWindowIcon(QIcon("01.ico"))
    
    try:
        # استيراد النافذة المحسنة
        from sub4_window import Sub4Window
        
        print("✅ تم استيراد sub4_window بنجاح")
        
        # محاولة إنشاء اتصال بقاعدة البيانات
        from PyQt5.QtSql import QSqlDatabase
        
        db = QSqlDatabase.addDatabase('QSQLITE')
        db.setDatabaseName('data.db')
        
        if not db.open():
            print("⚠️  تحذير: لم يتم العثور على قاعدة البيانات")
            print("💡 سيتم تشغيل النافذة بدون بيانات حقيقية")
        else:
            print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # إنشاء النافذة المحسنة
        print("\n🚀 إنشاء النافذة مع التحسينات المتقدمة...")
        window = Sub4Window(db=db, academic_year="2024-2025")
        
        print("\n🎨 الميزات المحسنة المطبقة:")
        print("   ✅ نظام الأزرار المتطور مع حساب العرض التلقائي")
        print("   ✅ التأثيرات اللونية المتقدمة للتحديد والتفاعل")
        print("   ✅ الرسائل المحسنة بالألوان والتدرجات")
        print("   ✅ مربعات الاختيار الكبيرة مع التأثيرات البصرية")
        print("   ✅ النظام المتجاوب للتخطيط والأحجام")
        print("   ✅ الألوان الجميلة والتنسيقات الرائعة")
        
        # عرض النافذة
        window.show()
        window.setWindowTitle("نافذة البحث المحسنة - الميزات المتقدمة من sub252_window.py")
        
        print("\n🌟 تم تشغيل النافذة بنجاح!")
        print("💡 لاحظ التحسينات التالية:")
        print("   • الأزرار الملونة مع العرض المحسوب تلقائياً")
        print("   • مربعات الاختيار الكبيرة الذهبية")
        print("   • الألوان الجميلة والتدرجات")
        print("   • التأثيرات التفاعلية عند التمرير والنقر")
        print("   • التخطيط المتجاوب والمنظم")
        
        # تشغيل التطبيق
        return app.exec_()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد sub4_window: {e}")
        QMessageBox.critical(None, "خطأ", f"فشل في استيراد النافذة:\n{e}")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        QMessageBox.critical(None, "خطأ", f"حدث خطأ غير متوقع:\n{e}")
        return 1

def show_features_info():
    """عرض معلومات الميزات المحسنة"""
    print("\n📋 تفاصيل الميزات المحسنة:")
    print("-" * 40)
    
    features = [
        ("🎯 الأزرار المتطورة", [
            "حساب العرض تلقائياً حسب النص",
            "ألوان مميزة لكل وظيفة",
            "تأثيرات التدرج اللوني",
            "تأثيرات Hover وPress"
        ]),
        ("🎨 التأثيرات اللونية", [
            "نظام ألوان ذهبي للتحديد",
            "تأثيرات تفاعلية للصفوف",
            "ألوان متدرجة للخلفيات",
            "تحسين ألوان الجداول"
        ]),
        ("💬 الرسائل المحسنة", [
            "رسائل ملونة حسب النوع",
            "تصميم حديث وجذاب",
            "تأثيرات بصرية متقدمة",
            "أسئلة بتصميم بنفسجي"
        ]),
        ("☑️ مربعات الاختيار", [
            "حجم أكبر (28x28 بكسل)",
            "ألوان ذهبية للمحدد",
            "علامة صح مخصصة",
            "تأثيرات الصفوف المحددة"
        ]),
        ("📐 التخطيط المتجاوب", [
            "توزيع الأعمدة بنسب محسوبة",
            "ارتفاع صفوف محسن",
            "تمدد ذكي للعناصر",
            "تحسين أحجام الخطوط"
        ]),
        ("🌈 الألوان الجميلة", [
            "نظام ألوان متكامل",
            "تدرجات لونية متناسقة",
            "ألوان تفاعلية",
            "تحسين التباين والوضوح"
        ])
    ]
    
    for title, items in features:
        print(f"\n{title}:")
        for item in items:
            print(f"   • {item}")

if __name__ == "__main__":
    print("🎨 مرحباً بك في اختبار التحسينات المتقدمة!")
    
    # عرض معلومات الميزات
    show_features_info()
    
    print("\n" + "=" * 60)
    print("🚀 بدء تشغيل النافذة المحسنة...")
    print("=" * 60)
    
    # تشغيل الاختبار
    exit_code = test_enhanced_features()
    
    print(f"\n🔚 انتهى الاختبار بكود الخروج: {exit_code}")
    sys.exit(exit_code)
