"""
نافذة تعديل المسميات - Python + HTML
تم إعادة تصميم النافذة لتستخدم منهجية Python + HTML الحديثة

الميزات:
- واجهة HTML جميلة ومتجاوبة مع تبويبات
- تكامل كامل مع قاعدة البيانات
- ثلاثة أقسام منفصلة: الأساسي، المخالفات، الإجراءات
- تحرير مباشر للبيانات
- تصميم عصري ومرن
"""

import sys
import os
import json
import sqlite3
from datetime import datetime
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal, Qt
from PyQt5.QtGui import QIcon

class DataEngine(QObject):
    """محرك إدارة بيانات المسميات"""

    # إشارات للتواصل مع واجهة HTML
    logUpdated = pyqtSignal(str, str, str)  # message, status, timestamp
    dataUpdated = pyqtSignal(str)  # data JSON

    def __init__(self):
        super().__init__()
        self.db_path = "data.db"
        self.table_name = "تعديل_المسميات"
        self.setup_database()

    def emit_log(self, message, status="info"):
        """إرسال رسالة إلى واجهة HTML"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.logUpdated.emit(message, status, timestamp)

    def setup_database(self):
        """إعداد قاعدة البيانات وإنشاء الجداول المطلوبة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # إنشاء جدول تعديل المسميات
            cursor.execute(f'''
                CREATE TABLE IF NOT EXISTS "{self.table_name}" (
                    ID INTEGER PRIMARY KEY AUTOINCREMENT,
                    الاسم TEXT NOT NULL,
                    العنوان TEXT,
                    ملاحظات TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            self.emit_log("✅ تم إعداد قاعدة بيانات المسميات بنجاح", "success")
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في إعداد قاعدة البيانات: {str(e)}", "error")

    @pyqtSlot(result=str)
    def getBasicData(self):
        """الحصول على البيانات الأساسية (ID 1-6)"""
        return self.get_data_by_range(1, 6, ["الاسم", "العنوان", "ملاحظات"])

    @pyqtSlot(result=str)
    def getViolationsData(self):
        """الحصول على بيانات المخالفات (ID 7-16)"""
        return self.get_data_by_range(7, 16, ["الاسم", "العنوان"])

    @pyqtSlot(result=str)
    def getProceduresData(self):
        """الحصول على بيانات الإجراءات (ID 17-20)"""
        return self.get_data_by_range(17, 20, ["الاسم", "العنوان"])

    def get_data_by_range(self, min_id, max_id, columns):
        """الحصول على البيانات ضمن نطاق معين"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            select_cols = ["ID"] + columns
            cols_str = ", ".join([f'"{col}"' for col in select_cols])
            query = f'SELECT {cols_str} FROM "{self.table_name}" WHERE ID BETWEEN ? AND ? ORDER BY ID'
            cursor.execute(query, (min_id, max_id))
            data = cursor.fetchall()
            
            # تحويل البيانات إلى قائمة من القواميس
            result = []
            for row in data:
                item = {"ID": row[0]}
                for i, col in enumerate(columns):
                    item[col] = row[i + 1] if row[i + 1] is not None else ""
                result.append(item)
            
            conn.close()
            return json.dumps(result, ensure_ascii=False)
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في جلب البيانات: {str(e)}", "error")
            return json.dumps([], ensure_ascii=False)

    @pyqtSlot(int, str, str)
    def updateData(self, record_id, column_name, new_value):
        """تحديث بيانات سجل معين"""
        try:
            # منع تحرير عمود الاسم
            if column_name == "الاسم":
                self.emit_log("⚠️ لا يمكن تحرير عمود الاسم", "warning")
                return
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            query = f'UPDATE "{self.table_name}" SET "{column_name}" = ? WHERE ID = ?'
            cursor.execute(query, (new_value, record_id))
            conn.commit()
            conn.close()
            
            self.emit_log(f"✅ تم تحديث السجل {record_id} بنجاح", "success")
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في تحديث البيانات: {str(e)}", "error")

    @pyqtSlot()
    def refreshData(self):
        """تحديث جميع البيانات"""
        try:
            self.emit_log("🔄 جاري تحديث البيانات...", "info")
            # إرسال إشارة تحديث البيانات
            self.dataUpdated.emit("refresh")
            self.emit_log("✅ تم تحديث البيانات بنجاح", "success")
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في تحديث البيانات: {str(e)}", "error")


class DataEditWindow(QMainWindow):
    """نافذة تعديل المسميات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("📝 تعديل المسميات")
        
        # إزالة أزرار التحكم وإظهار شريط العنوان فقط
        self.setWindowFlags(Qt.Window | Qt.CustomizeWindowHint | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        
        # تحديد حجم النافذة
        self.setMinimumSize(1200, 680)
        self.resize(1200, 680)

        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # إنشاء محرك البيانات
        self.data_engine = DataEngine()

        # إعداد الواجهة
        self.setup_ui()
        self.setup_web_channel()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء عرض الويب
        self.web_view = QWebEngineView()
        layout.addWidget(self.web_view)

        # تحميل واجهة HTML
        html_content = self.get_complete_html()
        self.web_view.setHtml(html_content)

    def setup_web_channel(self):
        """إعداد قناة التواصل"""
        self.channel = QWebChannel()
        self.channel.registerObject("dataEngine", self.data_engine)
        self.web_view.page().setWebChannel(self.channel)
        self.web_view.loadFinished.connect(self.on_page_loaded)

    def on_page_loaded(self):
        """استدعاء عند انتهاء تحميل الصفحة"""
        self.channel.registerObject("dataEngine", self.data_engine)

    def get_complete_html(self):
        """HTML كامل مع CSS و JavaScript"""
        return """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8">
    <title>تعديل المسميات</title>
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Calibri', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #B0E0E6 0%, #87CEEB 100%);
            min-height: 100vh;
            direction: rtl;
            color: #333;
            overflow: hidden;
        }        /* إخفاء أشرطة التمرير من العناصر العامة ما عدا الجداول */
        body, .container, .main-content, .tab-content {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        body::-webkit-scrollbar, 
        .container::-webkit-scrollbar, 
        .main-content::-webkit-scrollbar, 
        .tab-content::-webkit-scrollbar {
            display: none;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 15px;
            height: 100vh;
            overflow: hidden;
        }        .header {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 10px;
            margin-bottom: 10px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-family: 'Calibri', sans-serif;
            font-size: 20pt;
            font-weight: bold;
            color: #1e3a8a;
            margin-bottom: 5px;
        }

        .header p {
            font-family: 'Calibri', sans-serif;
            font-size: 13pt;
            color: #666;
        }

        .main-content {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            height: calc(100vh - 150px);
        }

        /* تبويبات */
        .tabs {
            display: flex;
            border-bottom: 2px solid #1976d2;
            margin-bottom: 20px;
        }        .tab {
            flex: 1;
            padding: 7px 20px;
            background: #f5f5f5;
            border: none;
            cursor: pointer;
            font-family: 'Calibri', sans-serif;
            font-size: 16pt;
            font-weight: bold;
            color: #666;
            transition: all 0.3s ease;
            border-radius: 10px 10px 0 0;
            margin-left: 5px;
        }

        .tab.active {
            background: #1976d2;
            color: white;
        }

        .tab:hover {
            background: #1565c0;
            color: white;
        }

        /* محتوى التبويبات */
        .tab-content {
            display: none;
            height: calc(100vh - 300px);
            overflow: hidden;
        }

        .tab-content.active {
            display: block;
        }        /* الجداول */
        .table-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            height: 100%;
            overflow-y: auto;
            overflow-x: hidden;
        }

        /* تصميم مخصص لأشرطة التمرير */
        .table-container::-webkit-scrollbar {
            width: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
            border-radius: 10px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
        }

        /* للمتصفحات الأخرى */
        .table-container {
            scrollbar-width: thin;
            scrollbar-color: #1976d2 #f1f1f1;
        }        table {
            width: 100%;
            border-collapse: collapse;
            font-family: 'Calibri', sans-serif;
            font-size: 13pt;
            margin-bottom: 10px;
        }

        thead {
            position: sticky;
            top: 0;
            z-index: 10;
        }th {
            background: #1976d2;
            color: white;
            padding: 3px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #1976d2;
        }        td {
            padding: 3px 6px;
            border: 1px solid #ddd;
            text-align: center;
        }

        tr:nth-child(even) {
            background: #f9f9f9;
        }

        tr:hover {
            background: #e3f2fd;
        }        /* حقول الإدخال */
        .editable-cell {
            background: transparent;
            border: none;
            width: 100%;
            padding: 3px;
            font-family: 'Calibri', sans-serif;
            font-size: 13pt;
            font-weight: bold;
            color: #333;
            text-align: center;
        }

        .editable-cell:focus {
            outline: 2px solid #1976d2;
            border-radius: 4px;
            background: white;
        }

        .readonly-cell {
            background: #f5f5f5;
            color: #666;
        }        /* أزرار التحكم */
        .button-group {
            display: flex;
            gap: 15px;
            margin-top: 15px;
            margin-bottom: 10px;
            justify-content: center;
            flex-wrap: wrap;
            padding: 10px;
            background: rgba(255,255,255,0.5);
            border-radius: 10px;
            backdrop-filter: blur(5px);
        }

        .btn {
            font-family: 'Calibri', sans-serif;
            font-size: 13pt;
            font-weight: bold;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            min-width: 140px;
            backdrop-filter: blur(10px);
        }

        .btn-primary {
            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(25, 118, 210, 0.4);
            background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(86, 171, 47, 0.4);
            background: linear-gradient(135deg, #4caf50 0%, #56ab2f 100%);
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #5bc0de 100%);
        }

        .btn-info:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4);
            background: linear-gradient(135deg, #138496 0%, #17a2b8 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
            background: linear-gradient(135deg, #e0a800 0%, #ffc107 100%);
        }

        /* رسائل الحالة */
        .message-box {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            font-weight: bold;
            color: white;
            display: none;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .message-box.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }

        .message-box.error {
            background: linear-gradient(135deg, #e74c3c 0%, #f39c12 100%);
        }

        .message-box.warning {
            background: linear-gradient(135deg, #f39c12 0%, #f8c471 100%);
        }

        .message-box.info {
            background: linear-gradient(135deg, #3498db 0%, #85c1e9 100%);
        }

        .message-box.show {
            display: block;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }        @media (max-width: 900px) {
            .tabs {
                flex-direction: column;
            }
            
            .tab {
                margin-left: 0;
                margin-bottom: 5px;
            }
            
            .button-group {
                flex-direction: column;
                align-items: center;
                gap: 10px;
            }
            
            .btn {
                width: 100%;
                max-width: 200px;
                min-width: unset;
            }
        }

        @media (max-width: 600px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 18pt;
            }
            
            .header p {
                font-size: 11pt;
            }
            
            .main-content {
                padding: 15px;
            }
            
            .table-container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- الرأس -->
        <div class="header">
            <h1>📝 تعديل المسميات</h1>
            <p>إدارة وتحرير بيانات المسميات والمخالفات والإجراءات</p>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="main-content">
            <!-- التبويبات -->
            <div class="tabs">
                <button class="tab active" onclick="showTab('basic')">📋 البيانات الأساسية</button>
                <button class="tab" onclick="showTab('violations')">⚠️ المخالفات</button>
                <button class="tab" onclick="showTab('procedures')">📊 الإجراءات</button>
            </div>            <!-- محتوى التبويب الأساسي -->
            <div id="basic-content" class="tab-content active">
                <div class="table-container">
                    <table id="basic-table">
                        <thead>
                            <tr>
                                <th style="width: 150px;">الاسم</th>
                                <th style="width: 250px;">العنوان</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم ملء البيانات بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>            <!-- محتوى تبويب المخالفات -->
            <div id="violations-content" class="tab-content">
                <div class="table-container">
                    <table id="violations-table">
                        <thead>
                            <tr>
                                <th style="width: 200px;">الاسم</th>
                                <th>العنوان</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم ملء البيانات بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>            <!-- محتوى تبويب الإجراءات -->
            <div id="procedures-content" class="tab-content">
                <div class="table-container">
                    <table id="procedures-table">
                        <thead>
                            <tr>
                                <th style="width: 200px;">الاسم</th>
                                <th>العنوان</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم ملء البيانات بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>            <!-- أزرار التحكم -->
            <div class="button-group">
                <button class="btn btn-primary" onclick="refreshAllData()">
                    🔄 تحديث البيانات
                </button>
                <button class="btn btn-success" onclick="saveAllChanges()">
                    💾 حفظ التغييرات
                </button>
                <button class="btn btn-info" onclick="exportData()">
                    📤 تصدير البيانات
                </button>
                <button class="btn btn-warning" onclick="resetCurrentTab()">
                    🔄 إعادة تعيين
                </button>
            </div>
        </div>

        <!-- رسائل الحالة -->
        <div class="message-box" id="messageBox"></div>
    </div>

    <script>
        let dataEngine = null;
        let isChannelReady = false;
        let currentTab = 'basic';

        // إعداد قناة التواصل مع Python
        function initializeChannel() {
            if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                new QWebChannel(qt.webChannelTransport, function(channel) {
                    dataEngine = channel.objects.dataEngine;
                    isChannelReady = true;

                    console.log('🚀 QWebChannel initialized successfully');

                    // ربط الإشارات
                    if (dataEngine) {
                        dataEngine.logUpdated.connect(handleLogUpdate);
                        dataEngine.dataUpdated.connect(handleDataUpdate);

                        // تحميل البيانات الأولية
                        loadAllData();

                        console.log('✅ تم تهيئة نظام تعديل المسميات بنجاح');
                    }
                });
            } else {
                console.log('⚠️ QWebChannel not available, retrying...');
                setTimeout(initializeChannel, 100);
            }
        }

        // تحميل جميع البيانات
        function loadAllData() {
            loadBasicData();
            loadViolationsData();
            loadProceduresData();
        }

        // تحميل البيانات الأساسية
        function loadBasicData() {
            if (dataEngine) {
                dataEngine.getBasicData(function(result) {
                    try {
                        let data;
                        if (typeof result === 'string') {
                            data = JSON.parse(result);
                        } else {
                            data = result;
                        }
                        
                        fillTable('basic-table', data, ['الاسم', 'العنوان', 'ملاحظات']);
                    } catch (error) {
                        console.error('خطأ في تحليل البيانات الأساسية:', error);
                    }
                });
            }
        }

        // تحميل بيانات المخالفات
        function loadViolationsData() {
            if (dataEngine) {
                dataEngine.getViolationsData(function(result) {
                    try {
                        let data;
                        if (typeof result === 'string') {
                            data = JSON.parse(result);
                        } else {
                            data = result;
                        }
                        
                        fillTable('violations-table', data, ['الاسم', 'العنوان']);
                    } catch (error) {
                        console.error('خطأ في تحليل بيانات المخالفات:', error);
                    }
                });
            }
        }

        // تحميل بيانات الإجراءات
        function loadProceduresData() {
            if (dataEngine) {
                dataEngine.getProceduresData(function(result) {
                    try {
                        let data;
                        if (typeof result === 'string') {
                            data = JSON.parse(result);
                        } else {
                            data = result;
                        }
                        
                        fillTable('procedures-table', data, ['الاسم', 'العنوان']);
                    } catch (error) {
                        console.error('خطأ في تحليل بيانات الإجراءات:', error);
                    }
                });
            }
        }

        // ملء الجدول بالبيانات
        function fillTable(tableId, data, columns) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            tbody.innerHTML = '';

            data.forEach(row => {
                const tr = document.createElement('tr');
                
                columns.forEach(column => {
                    const td = document.createElement('td');
                    
                    if (column === 'الاسم') {
                        // عمود الاسم للقراءة فقط
                        const input = document.createElement('input');
                        input.className = 'editable-cell readonly-cell';
                        input.value = row[column] || '';
                        input.readOnly = true;
                        td.appendChild(input);
                    } else {
                        // أعمدة قابلة للتحرير
                        const input = document.createElement('input');
                        input.className = 'editable-cell';
                        input.value = row[column] || '';
                        input.onblur = function() {
                            updateData(row.ID, column, this.value);
                        };
                        input.onkeypress = function(e) {
                            if (e.key === 'Enter') {
                                this.blur();
                            }
                        };
                        td.appendChild(input);
                    }
                    
                    tr.appendChild(td);
                });
                
                tbody.appendChild(tr);
            });
        }

        // تحديث البيانات
        function updateData(recordId, columnName, newValue) {
            if (dataEngine) {
                dataEngine.updateData(recordId, columnName, newValue);
            }
        }

        // عرض التبويب
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // إزالة التمييز من جميع أزرار التبويبات
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // إظهار التبويب المحدد
            document.getElementById(tabName + '-content').classList.add('active');
            
            // تمييز زر التبويب المحدد
            event.target.classList.add('active');
            
            currentTab = tabName;
        }

        // تحديث جميع البيانات
        function refreshAllData() {
            if (dataEngine) {
                dataEngine.refreshData();
                setTimeout(loadAllData, 500);
            } else {
                showMessage('❌ النظام غير جاهز بعد', 'error');
            }
        }        // حفظ جميع التغييرات
        function saveAllChanges() {
            showMessage('✅ تم حفظ جميع التغييرات', 'success');
        }

        // تصدير البيانات
        function exportData() {
            if (dataEngine) {
                showMessage('📤 جاري تصدير بيانات التبويب الحالي...', 'info');
                // يمكن إضافة منطق التصدير هنا لاحقاً
                setTimeout(() => {
                    showMessage('✅ تم تصدير البيانات بنجاح', 'success');
                }, 1500);
            } else {
                showMessage('❌ النظام غير جاهز بعد', 'error');
            }
        }

        // إعادة تعيين التبويب الحالي
        function resetCurrentTab() {
            if (confirm('هل أنت متأكد من إعادة تعيين بيانات التبويب الحالي؟')) {
                showMessage('🔄 جاري إعادة تعيين البيانات...', 'info');
                
                // إعادة تحميل بيانات التبويب الحالي
                if (currentTab === 'basic') {
                    loadBasicData();
                } else if (currentTab === 'violations') {
                    loadViolationsData();
                } else if (currentTab === 'procedures') {
                    loadProceduresData();
                }
                
                setTimeout(() => {
                    showMessage('✅ تم إعادة تعيين البيانات بنجاح', 'success');
                }, 1000);
            }
        }

        // معالجة تحديث السجل
        function handleLogUpdate(message, status, timestamp) {
            showMessage(message, status);
        }

        // معالجة تحديث البيانات
        function handleDataUpdate(action) {
            if (action === 'refresh') {
                loadAllData();
            }
        }

        // عرض الرسائل
        function showMessage(message, type) {
            const messageBox = document.getElementById('messageBox');
            messageBox.textContent = message;
            messageBox.className = `message-box ${type} show`;
            
            setTimeout(() => {
                messageBox.classList.remove('show');
            }, 3000);
        }

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeChannel();
        });
    </script>
</body>
</html>"""


def main():
    """تشغيل نافذة تعديل المسميات"""
    app = QApplication(sys.argv)

    # إعداد التطبيق
    app.setApplicationName("تعديل المسميات")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Modern Education Systems")

    # إنشاء النافذة
    window = DataEditWindow()
    window.show()

    # تشغيل التطبيق
    sys.exit(app.exec_())


if __name__ == '__main__':
    print("🌐 بدء تشغيل نظام تعديل المسميات...")
    print("=" * 60)
    print("📋 الميزات:")
    print("   🔹 واجهة HTML جميلة مع تبويبات")
    print("   🔹 ثلاثة أقسام منفصلة")
    print("   🔹 تحرير مباشر للبيانات")
    print("   🔹 تكامل كامل مع قاعدة البيانات")
    print("   🔹 تصميم عصري ومتجاوب")
    print("=" * 60)
    print("🚀 جاري تشغيل النظام...")

    try:
        main()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("💡 تأكد من تثبيت PyQt5:")
        print("   pip install PyQt5 PyQtWebEngine")
