#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تبويب إعدادات البرنامج - التحقق من التفعيل والعمل
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon

def test_program_settings_tab():
    """اختبار تبويب إعدادات البرنامج"""
    print("⚙️ اختبار تبويب إعدادات البرنامج")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    
    try:
        from main_window import MainWindow
        print("✅ تم استيراد MainWindow")
        
        main_window = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # فحص تبويب إعدادات البرنامج
        def check_program_settings_tab():
            print("\n🔍 فحص تبويب إعدادات البرنامج:")
            
            # البحث عن التبويب
            program_settings_found = False
            program_settings_index = -1
            
            tab_count = main_window.tabWidget.count()
            print(f"📋 عدد التبويبات: {tab_count}")
            
            for i in range(tab_count):
                tab_text = main_window.tabWidget.tabText(i)
                tab_data = main_window.tabWidget.tabData(i)
                print(f"   {i}: {tab_text} ({tab_data})")
                
                if tab_text == "إعدادات البرنامج":
                    program_settings_found = True
                    program_settings_index = i
                    print(f"   ✅ تم العثور على تبويب إعدادات البرنامج في الفهرس {i}")
                    
                    # التحقق من حالة التبويب
                    is_enabled = main_window.tabWidget.isTabEnabled(i)
                    print(f"   📌 حالة التفعيل: {'مفعل' if is_enabled else 'غير مفعل'}")
            
            if not program_settings_found:
                print("   ❌ لم يتم العثور على تبويب إعدادات البرنامج")
                return
            
            # التحقق من النافذة المرتبطة
            if "program_settings" in main_window.windows:
                print("   ✅ النافذة المرتبطة موجودة")
                window = main_window.windows["program_settings"]
                print(f"   📄 نوع النافذة: {type(window).__name__}")
            else:
                print("   ❌ النافذة المرتبطة غير موجودة")
            
            # اختبار النقر على التبويب
            print("\n🎯 اختبار النقر على التبويب:")
            if program_settings_index >= 0:
                print(f"   محاولة النقر على التبويب {program_settings_index}...")
                
                # محاكاة النقر على التبويب
                main_window.tabWidget.setCurrentIndex(program_settings_index)
                current_index = main_window.tabWidget.currentIndex()
                print(f"   الفهرس الحالي بعد النقر: {current_index}")
                
                # التحقق من ظهور القائمة المنسدلة
                if hasattr(main_window, 'dropdown_widget'):
                    dropdown = main_window.dropdown_widget
                    print(f"   ارتفاع القائمة المنسدلة: {dropdown.height()}")
                    print(f"   مرئية: {dropdown.isVisible()}")
                    
                    if dropdown.height() > 0:
                        print("   ✅ القائمة المنسدلة ظهرت بنجاح!")
                    else:
                        print("   ❌ القائمة المنسدلة لم تظهر")
                        
                        # محاولة إظهار القائمة يدوياً
                        print("   🔧 محاولة إظهار القائمة يدوياً...")
                        if hasattr(main_window, '_show_dropdown'):
                            main_window._show_dropdown()
                            print(f"   ارتفاع القائمة بعد الإظهار اليدوي: {dropdown.height()}")
                else:
                    print("   ❌ القائمة المنسدلة غير موجودة")
        
        # فحص التبويب بعد ثانية واحدة
        QTimer.singleShot(1000, check_program_settings_tab)
        
        main_window.show()
        main_window.setWindowTitle("اختبار تبويب إعدادات البرنامج")
        
        print("\n📋 تعليمات الاختبار:")
        print("1. ابحث عن تبويب 'إعدادات البرنامج'")
        print("2. تحقق من أنه مفعل (ليس رمادي)")
        print("3. انقر على التبويب")
        print("4. يجب أن تظهر القائمة المنسدلة بـ 6 أزرار")
        print("5. إذا لم تظهر، راقب الرسائل التشخيصية")
        
        print("\n🔧 الإصلاحات المطبقة:")
        print("   ✅ ربط تبويب 'إعدادات البرنامج' بنافذة بيانات المؤسسة")
        print("   ✅ إضافة النافذة لقاموس النوافذ")
        print("   ✅ تحديث معالج النقر على التبويب")
        
        print("\n⚠️ ملاحظات:")
        print("   • التبويب يجب أن يكون مفعلاً الآن")
        print("   • النقر عليه يظهر القائمة المنسدلة")
        print("   • القائمة تحتوي على 6 أزرار ملونة")
        print("   • كل زر ينقل للنافذة المطلوبة")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return 1

def show_troubleshooting():
    """عرض دليل استكشاف الأخطاء"""
    print("\n" + "=" * 60)
    print("🔧 دليل استكشاف الأخطاء")
    print("=" * 60)
    
    issues = [
        ("التبويب غير مفعل", [
            "تحقق من وجود النافذة في قاموس النوافذ",
            "تأكد من ربط التبويب بنافذة صحيحة",
            "راجع رسائل الخطأ في وحدة التحكم"
        ]),
        ("القائمة لا تظهر", [
            "تحقق من معالج النقر على التبويب",
            "تأكد من أن window_key صحيح",
            "راجع دالة _show_dropdown"
        ]),
        ("الأزرار لا تعمل", [
            "تحقق من ربط الأزرار بالنوافذ",
            "تأكد من وجود النوافذ في قاموس النوافذ",
            "راجع دالة _navigate_to_dropdown_tab"
        ])
    ]
    
    for issue, solutions in issues:
        print(f"\n🚨 {issue}:")
        for solution in solutions:
            print(f"   • {solution}")

if __name__ == "__main__":
    print("⚙️ مرحباً بك في اختبار تبويب إعدادات البرنامج!")
    
    # عرض دليل استكشاف الأخطاء
    show_troubleshooting()
    
    print("\n" + "=" * 60)
    print("🚀 بدء تشغيل الاختبار...")
    print("=" * 60)
    
    # تشغيل الاختبار
    exit_code = test_program_settings_tab()
    
    print(f"\n🔚 انتهى الاختبار بكود الخروج: {exit_code}")
    sys.exit(exit_code)
