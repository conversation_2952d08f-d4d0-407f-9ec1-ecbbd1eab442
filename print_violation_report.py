#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ملف توجيه لوظيفة طباعة تقرير المخالفة
تم نقل الكود إلى ملف print3.py
"""

import traceback

def print_violation_details(violation_data, db_path=None):
    """
    دالة توجيه لطباعة تقرير المخالفة
    تم نقل الكود إلى ملف print3.py

    Args:
        violation_data (dict): بيانات المخالفة
        db_path (str, optional): مسار قاعدة البيانات. Defaults to None.

    Returns:
        bool: نجاح العملية
    """
    try:
        # محاولة استيراد الدالة من الملف الجديد
        from print3 import print_violation_details as new_print_violation

        print("تنبيه: تم نقل وظيفة طباعة تقرير المخالفة إلى ملف print3.py")
        print("جاري استخدام الملف الجديد...")

        # استدعاء الدالة من الملف الجديد
        success, _ = new_print_violation(violation_data, db_path, auto_open=True)
        return success

    except ImportError:
        print("خطأ: لم يتم العثور على ملف print3.py")
        print("الرجاء التأكد من وجود الملف في نفس المجلد")
        return False
    except Exception as e:
        print(f"خطأ غير متوقع: {str(e)}")
        traceback.print_exc()
        return False

# قسم اختباري
if __name__ == "__main__":
    # بيانات تجريبية للاختبار
    violation_info_test = {
        "id": 123,
        "date": "2024-03-15",
        "student_name": "أحمد المنصوري",
        "student_code": "A123456",
        "section": "1APIC-1",
        "level": "الأولى إعدادي",
        "subject": "الرياضيات",
        "teacher": "محمد العلوي",
        "notes": "تأخر عن الحصة بدون مبرر وأحدث فوضى في القسم",
        "procedures": "إنذار شفوي وإخبار ولي الأمر"
    }

    # طباعة المخالفة التجريبية
    try:
        print_violation_details(violation_info_test)
    except Exception as e:
        print(f"خطأ في تشغيل الاختبار: {e}")
        print("تأكد من وجود ملف print3.py في نفس المجلد")